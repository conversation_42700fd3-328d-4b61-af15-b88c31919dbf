{"version": 3, "file": "WingmanShip-BsAce294.js", "sources": ["../../src/entities/WingmanShip.js"], "sourcesContent": ["import { GameObject } from '../utils/GameObject.js';\nimport { Vector2 } from '../utils/Vector2.js';\nimport { WeaponSystem } from '../systems/WeaponSystem.js';\n\n/**\n * WingmanShip class - AI-controlled ship that follows the player and provides covering fire\n * Implements formation flying and autonomous targeting\n */\nexport class WingmanShip extends GameObject {\n    constructor(x, y, playerShip, gameObjectManager) {\n        super(x, y);\n        \n        // Reference to player ship for formation flying\n        this.playerShip = playerShip;\n        this.gameObjectManager = gameObjectManager;\n        \n        // Ship properties\n        this.maxSpeed = 250; // Slightly slower than player\n        this.acceleration = 600;\n        this.friction = 0.9;\n        \n        // Formation flying properties\n        this.formationOffset = new Vector2(-60, 20); // Position relative to player\n        this.targetPosition = new Vector2(x, y);\n        this.followDistance = 80;\n        this.maxFormationDistance = 150;\n        \n        // AI behavior properties\n        this.fireRate = 400; // Slightly slower than player\n        this.lastFireTime = 0;\n        this.targetingRange = 300;\n        this.currentTarget = null;\n        \n        // Visual properties\n        this.width = 48;\n        this.height = 64;\n        this.color = '#00ff88';\n        this.glowColor = '#44ffaa';\n        \n        // Health and status\n        this.maxHealth = 50;\n        this.health = this.maxHealth;\n        this.isDestroyed = false;\n        \n        // Animation properties\n        this.animationTime = 0;\n        this.thrusterIntensity = 0;\n        \n        // Weapon system\n        this.weaponSystem = new WeaponSystem(this, gameObjectManager);\n        this.weaponSystem.setFireRate(this.fireRate);\n        this.weaponSystem.setProjectileDamage(20); // Slightly less damage than player\n        this.weaponSystem.projectileType = 'wingman';\n        \n        // Add wingman tag\n        this.addTag('wingman');\n        this.addTag('friendly');\n\n        // Hangar upgrade system\n        this.hangarManager = null;\n        this.baseStats = {\n            maxHealth: this.maxHealth,\n            fireRate: this.fireRate\n        };\n        \n        \n    }\n    \n    /**\n     * Update wingman behavior, movement, and combat\n     * @param {number} deltaTime - Time elapsed since last update in milliseconds\n     */\n    update(deltaTime) {\n        if (!this.active || this.isDestroyed) return;\n        \n        // Update animation timer\n        this.animationTime += deltaTime / 1000;\n        \n        // Update formation flying\n        this.updateFormationFlying(deltaTime);\n        \n        // Update AI targeting and combat\n        this.updateCombatAI(deltaTime);\n        \n        // Update weapon system\n        if (this.weaponSystem) {\n            this.weaponSystem.update(deltaTime);\n        }\n        \n        // Update collision bounds\n        this.updateCollisionBounds();\n    }\n    \n    /**\n     * Update formation flying behavior\n     * @param {number} deltaTime - Time elapsed since last update\n     */\n    updateFormationFlying(deltaTime) {\n        if (!this.playerShip || !this.playerShip.active) return;\n        \n        // Calculate desired formation position\n        const playerPos = this.playerShip.position;\n        const playerVel = this.playerShip.velocity;\n        \n        // Adjust formation offset based on player movement\n        let dynamicOffset = this.formationOffset.clone();\n        \n        // If player is moving, adjust formation to be more behind\n        if (playerVel.magnitude() > 50) {\n            const playerDirection = playerVel.normalize();\n            const sideOffset = new Vector2(-playerDirection.y, playerDirection.x).multiply(40);\n            const backOffset = playerDirection.multiply(-30);\n            dynamicOffset = sideOffset.add(backOffset);\n        }\n        \n        this.targetPosition = playerPos.add(dynamicOffset);\n        \n        // Calculate distance to target position\n        const toTarget = this.targetPosition.subtract(this.position);\n        const distance = toTarget.magnitude();\n        \n        // Only move if we're too far from formation position\n        if (distance > this.followDistance) {\n            // Calculate movement force\n            const desiredVelocity = toTarget.normalize().multiply(this.maxSpeed);\n            const velocityDiff = desiredVelocity.subtract(this.velocity);\n\n            // Apply acceleration without overshooting target velocity\n            const dtSeconds = deltaTime / 1000;\n            const maxDeltaV = this.acceleration * dtSeconds;\n            const diffMag = velocityDiff.magnitude();\n            if (diffMag > maxDeltaV) {\n                this.velocity.addInPlace(velocityDiff.normalize().multiply(maxDeltaV));\n            } else {\n                this.velocity.addInPlace(velocityDiff);\n            }\n\n            // Limit velocity\n            if (this.velocity.magnitude() > this.maxSpeed) {\n                this.velocity = this.velocity.normalize().multiply(this.maxSpeed);\n            }\n\n            this.thrusterIntensity = Math.min(1.0, distance / 100);\n        } else {\n            // Apply friction when in formation\n            this.velocity.multiplyInPlace(Math.pow(this.friction, deltaTime / 16.67));\n            // Stop tiny velocities to prevent micro jitter\n            if (this.velocity.magnitude() < 1) {\n                this.velocity.set(0, 0);\n            }\n            this.thrusterIntensity *= 0.95;\n        }\n        \n        // Update position\n        this.position.addInPlace(this.velocity.multiply(deltaTime / 1000));\n        \n        // Keep wingman on screen (with some margin)\n        const margin = 50;\n        const canvasWidth = this.playerShip.canvasWidth || 800;\n        const canvasHeight = this.playerShip.canvasHeight || 600;\n        this.position.x = Math.max(margin, Math.min(this.position.x, canvasWidth - margin));\n        this.position.y = Math.max(margin, Math.min(this.position.y, canvasHeight - margin));\n    }\n    \n    /**\n     * Update combat AI - targeting and firing\n     * @param {number} deltaTime - Time elapsed since last update\n     */\n    updateCombatAI(deltaTime) {\n        // Update fire cooldown\n        this.lastFireTime += deltaTime;\n        \n        // Find nearest enemy target\n        this.currentTarget = this.findNearestEnemy();\n        \n        // Fire at target if available and in range\n        if (this.currentTarget && this.lastFireTime >= this.fireRate) {\n            const distanceToTarget = this.position.distance(this.currentTarget.position);\n            \n            if (distanceToTarget <= this.targetingRange) {\n                // Calculate firing direction\n                const toTarget = this.currentTarget.position.subtract(this.position);\n                const fireDirection = toTarget.normalize();\n                \n                // Fire weapon\n                if (this.weaponSystem && this.weaponSystem.fire(fireDirection)) {\n                    this.lastFireTime = 0;\n                }\n            }\n        }\n    }\n    \n    /**\n     * Find the nearest enemy within targeting range\n     * @returns {GameObject|null} Nearest enemy or null if none found\n     */\n    findNearestEnemy() {\n        if (!this.gameObjectManager) return null;\n        \n        let nearestEnemy = null;\n        let nearestDistance = this.targetingRange;\n        \n        // Get all enemies from game object manager\n        const enemies = this.gameObjectManager.findByTag('enemy');\n        \n        for (const enemy of enemies) {\n            if (!enemy.active) continue;\n            \n            const distance = this.position.distance(enemy.position);\n            if (distance < nearestDistance) {\n                nearestEnemy = enemy;\n                nearestDistance = distance;\n            }\n        }\n        \n        return nearestEnemy;\n    }\n    \n    /**\n     * Take damage and handle destruction\n     * @param {number} damage - Amount of damage to take\n     * @returns {object} Damage result information\n     */\n    takeDamage(damage) {\n        if (this.isDestroyed) {\n            return {\n                damageTaken: 0,\n                destroyed: true,\n                scoreValue: 0\n            };\n        }\n        \n        const actualDamage = Math.max(0, damage);\n        this.health -= actualDamage;\n        \n        const result = {\n            damageTaken: actualDamage,\n            destroyed: false,\n            scoreValue: 0\n        };\n        \n        if (this.health <= 0) {\n            this.health = 0;\n            this.destroy();\n            result.destroyed = true;\n        }\n        \n        return result;\n    }\n    \n    /**\n     * Destroy the wingman ship\n     */\n    destroy() {\n        this.isDestroyed = true;\n        this.active = false;\n        \n        // TODO: Add destruction visual effects\n        \n    }\n    \n    /**\n     * Render the wingman ship\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     * @param {number} interpolation - Interpolation factor (ignored for wingman ship)\n     */\n    render(ctx, interpolation = 0) {\n        if (!this.active || this.isDestroyed) return;\n\n        ctx.save();\n        // Snap to integer pixels to reduce subpixel jitter\n        const rx = Math.round(this.position.x);\n        const ry = Math.round(this.position.y);\n        ctx.translate(rx, ry);\n\n        // Draw ship glow effect\n        if (this.thrusterIntensity > 0.1) {\n            ctx.shadowColor = this.glowColor;\n            ctx.shadowBlur = 15 * this.thrusterIntensity;\n        }\n        \n        // Use sprite if available, otherwise fall back to original drawing\n        const sprite = this.getWingmanSprite();\n        if (sprite) {\n            // Draw sprite with bloom effect\n            const spriteSize = this.width * 1.5; // Slightly larger than the original ship\n            \n            // Apply shadow blur for bloom effect\n            ctx.shadowColor = this.color;\n            ctx.shadowBlur = 15;\n            \n            ctx.drawImage(\n                sprite,\n                -spriteSize / 2,\n                -spriteSize / 2,\n                spriteSize,\n                spriteSize\n            );\n            \n            ctx.shadowBlur = 0;\n        } else {\n            // Fallback to original ship drawing if sprite not available\n            // Draw main ship body\n            ctx.fillStyle = this.color;\n            ctx.beginPath();\n            ctx.moveTo(0, -this.height / 2);\n            ctx.lineTo(-this.width / 3, this.height / 3);\n            ctx.lineTo(-this.width / 6, this.height / 2);\n            ctx.lineTo(this.width / 6, this.height / 2);\n            ctx.lineTo(this.width / 3, this.height / 3);\n            ctx.closePath();\n            ctx.fill();\n\n            // Draw ship details\n            ctx.fillStyle = '#ffffff';\n            ctx.fillRect(-2, -this.height / 4, 4, 8);\n\n            // Draw thrusters\n            if (this.thrusterIntensity > 0.1) {\n                const thrusterLength = 15 * this.thrusterIntensity;\n                const thrusterFlicker = Math.sin(this.animationTime * 20) * 0.3 + 0.7;\n                \n                ctx.fillStyle = `rgba(0, 150, 255, ${thrusterFlicker * this.thrusterIntensity})`;\n                ctx.fillRect(-4, this.height / 2, 3, thrusterLength);\n                ctx.fillRect(1, this.height / 2, 3, thrusterLength);\n            }\n        }\n        \n        // Render weapon effects\n        if (this.weaponSystem) {\n            this.weaponSystem.render(ctx);\n        }\n        \n        ctx.restore();\n    }\n    \n    /**\n     * Update collision bounds\n     */\n    updateCollisionBounds() {\n        this.bounds = {\n            left: this.position.x - this.width / 2,\n            right: this.position.x + this.width / 2,\n            top: this.position.y - this.height / 2,\n            bottom: this.position.y + this.height / 2\n        };\n    }\n    \n    /**\n     * Get wingman status information\n     * @returns {object} Status information\n     */\n    getStatus() {\n        return {\n            health: this.health,\n            maxHealth: this.maxHealth,\n            healthPercentage: this.health / this.maxHealth,\n            isDestroyed: this.isDestroyed,\n            hasTarget: !!this.currentTarget,\n            distanceToPlayer: this.playerShip ? this.position.distance(this.playerShip.position) : 0\n        };\n    }\n\n    /**\n     * Fire weapon in the same direction as the player\n     * @param {Vector2} direction - Direction to fire (from player)\n     * @returns {boolean} True if weapon fired successfully\n     */\n    fireOnPlayerFire(direction) {\n        if (!this.active || this.isDestroyed) return false;\n        \n        // Check if weapon is ready\n        if (this.weaponSystem && this.weaponSystem.isReady()) {\n            // Fire in the same direction as player\n            return this.weaponSystem.fire(direction);\n        }\n        return false;\n    }\n\n    /**\n     * Get the wingman sprite from the game engine\n     * @returns {Image|null} Wingman sprite or null if not loaded\n     */\n    getWingmanSprite() {\n        if (window.gameEngine && window.gameEngine.wingmanSprite) {\n            const sprite = window.gameEngine.wingmanSprite;\n            if (sprite && sprite.complete) {\n                return sprite;\n            }\n        }\n        return null;\n    }\n\n    /**\n     * Set the hangar manager reference for upgrade support\n     * @param {HangarManager} hangarManager - The hangar manager instance\n     */\n    setHangarManager(hangarManager) {\n        this.hangarManager = hangarManager;\n        this.applyHangarUpgrades();\n    }\n\n    /**\n     * Apply hangar upgrades to wingman stats\n     * No wingman upgrades implemented - no-op\n     */\n    applyHangarUpgrades() {\n        // Wingman upgrades removed - no stats to apply\n        console.log('WingmanShip: No hangar upgrades available');\n    }\n\n    /**\n     * Get current upgrade status for display\n     * @returns {object} Current upgrade status\n     */\n    getUpgradeStatus() {\n        return {\n            hasUpgrades: false,\n            baseStats: this.baseStats,\n            currentStats: {\n                maxHealth: this.maxHealth,\n                fireRate: this.fireRate\n            }\n        };\n    }\n}\n"], "names": ["WingmanShip", "GameObject", "x", "y", "playerShip", "gameObjectManager", "Vector2", "WeaponSystem", "deltaTime", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "dynamicOffset", "playerDirection", "sideOffset", "backOffset", "<PERSON><PERSON><PERSON><PERSON>", "distance", "velocityDiff", "dtSeconds", "maxDeltaV", "margin", "canvasWidth", "canvasHeight", "fireDirection", "nearestEnemy", "nearestDistance", "enemies", "enemy", "damage", "actualDamage", "result", "ctx", "interpolation", "rx", "ry", "sprite", "spriteSize", "thrusterLength", "thrusterFlicker", "direction", "hangarManager"], "mappings": "qDAQO,MAAMA,UAAoBC,CAAW,CACxC,YAAYC,EAAGC,EAAGC,EAAYC,EAAmB,CAC7C,MAAMH,EAAGC,CAAC,EAGV,KAAK,WAAaC,EAClB,KAAK,kBAAoBC,EAGzB,KAAK,SAAW,IAChB,KAAK,aAAe,IACpB,KAAK,SAAW,GAGhB,KAAK,gBAAkB,IAAIC,EAAQ,IAAK,EAAE,EAC1C,KAAK,eAAiB,IAAIA,EAAQJ,EAAGC,CAAC,EACtC,KAAK,eAAiB,GACtB,KAAK,qBAAuB,IAG5B,KAAK,SAAW,IAChB,KAAK,aAAe,EACpB,KAAK,eAAiB,IACtB,KAAK,cAAgB,KAGrB,KAAK,MAAQ,GACb,KAAK,OAAS,GACd,KAAK,MAAQ,UACb,KAAK,UAAY,UAGjB,KAAK,UAAY,GACjB,KAAK,OAAS,KAAK,UACnB,KAAK,YAAc,GAGnB,KAAK,cAAgB,EACrB,KAAK,kBAAoB,EAGzB,KAAK,aAAe,IAAII,EAAa,KAAMF,CAAiB,EAC5D,KAAK,aAAa,YAAY,KAAK,QAAQ,EAC3C,KAAK,aAAa,oBAAoB,EAAE,EACxC,KAAK,aAAa,eAAiB,UAGnC,KAAK,OAAO,SAAS,EACrB,KAAK,OAAO,UAAU,EAGtB,KAAK,cAAgB,KACrB,KAAK,UAAY,CACb,UAAW,KAAK,UAChB,SAAU,KAAK,QAC3B,CAGI,CAMA,OAAOG,EAAW,CACV,CAAC,KAAK,QAAU,KAAK,cAGzB,KAAK,eAAiBA,EAAY,IAGlC,KAAK,sBAAsBA,CAAS,EAGpC,KAAK,eAAeA,CAAS,EAGzB,KAAK,cACL,KAAK,aAAa,OAAOA,CAAS,EAItC,KAAK,sBAAqB,EAC9B,CAMA,sBAAsBA,EAAW,CAC7B,GAAI,CAAC,KAAK,YAAc,CAAC,KAAK,WAAW,OAAQ,OAGjD,MAAMC,EAAY,KAAK,WAAW,SAC5BC,EAAY,KAAK,WAAW,SAGlC,IAAIC,EAAgB,KAAK,gBAAgB,MAAK,EAG9C,GAAID,EAAU,UAAS,EAAK,GAAI,CAC5B,MAAME,EAAkBF,EAAU,UAAS,EACrCG,EAAa,IAAIP,EAAQ,CAACM,EAAgB,EAAGA,EAAgB,CAAC,EAAE,SAAS,EAAE,EAC3EE,EAAaF,EAAgB,SAAS,GAAG,EAC/CD,EAAgBE,EAAW,IAAIC,CAAU,CAC7C,CAEA,KAAK,eAAiBL,EAAU,IAAIE,CAAa,EAGjD,MAAMI,EAAW,KAAK,eAAe,SAAS,KAAK,QAAQ,EACrDC,EAAWD,EAAS,UAAS,EAGnC,GAAIC,EAAW,KAAK,eAAgB,CAGhC,MAAMC,EADkBF,EAAS,UAAS,EAAG,SAAS,KAAK,QAAQ,EAC9B,SAAS,KAAK,QAAQ,EAGrDG,EAAYV,EAAY,IACxBW,EAAY,KAAK,aAAeD,EACtBD,EAAa,UAAS,EACxBE,EACV,KAAK,SAAS,WAAWF,EAAa,YAAY,SAASE,CAAS,CAAC,EAErE,KAAK,SAAS,WAAWF,CAAY,EAIrC,KAAK,SAAS,UAAS,EAAK,KAAK,WACjC,KAAK,SAAW,KAAK,SAAS,UAAS,EAAG,SAAS,KAAK,QAAQ,GAGpE,KAAK,kBAAoB,KAAK,IAAI,EAAKD,EAAW,GAAG,CACzD,MAEI,KAAK,SAAS,gBAAgB,KAAK,IAAI,KAAK,SAAUR,EAAY,KAAK,CAAC,EAEpE,KAAK,SAAS,UAAS,EAAK,GAC5B,KAAK,SAAS,IAAI,EAAG,CAAC,EAE1B,KAAK,mBAAqB,IAI9B,KAAK,SAAS,WAAW,KAAK,SAAS,SAASA,EAAY,GAAI,CAAC,EAGjE,MAAMY,EAAS,GACTC,EAAc,KAAK,WAAW,aAAe,IAC7CC,EAAe,KAAK,WAAW,cAAgB,IACrD,KAAK,SAAS,EAAI,KAAK,IAAIF,EAAQ,KAAK,IAAI,KAAK,SAAS,EAAGC,EAAcD,CAAM,CAAC,EAClF,KAAK,SAAS,EAAI,KAAK,IAAIA,EAAQ,KAAK,IAAI,KAAK,SAAS,EAAGE,EAAeF,CAAM,CAAC,CACvF,CAMA,eAAeZ,EAAW,CAQtB,GANA,KAAK,cAAgBA,EAGrB,KAAK,cAAgB,KAAK,iBAAgB,EAGtC,KAAK,eAAiB,KAAK,cAAgB,KAAK,UACvB,KAAK,SAAS,SAAS,KAAK,cAAc,QAAQ,GAEnD,KAAK,eAAgB,CAGzC,MAAMe,EADW,KAAK,cAAc,SAAS,SAAS,KAAK,QAAQ,EACpC,UAAS,EAGpC,KAAK,cAAgB,KAAK,aAAa,KAAKA,CAAa,IACzD,KAAK,aAAe,EAE5B,CAER,CAMA,kBAAmB,CACf,GAAI,CAAC,KAAK,kBAAmB,OAAO,KAEpC,IAAIC,EAAe,KACfC,EAAkB,KAAK,eAG3B,MAAMC,EAAU,KAAK,kBAAkB,UAAU,OAAO,EAExD,UAAWC,KAASD,EAAS,CACzB,GAAI,CAACC,EAAM,OAAQ,SAEnB,MAAMX,EAAW,KAAK,SAAS,SAASW,EAAM,QAAQ,EAClDX,EAAWS,IACXD,EAAeG,EACfF,EAAkBT,EAE1B,CAEA,OAAOQ,CACX,CAOA,WAAWI,EAAQ,CACf,GAAI,KAAK,YACL,MAAO,CACH,YAAa,EACb,UAAW,GACX,WAAY,CAC5B,EAGQ,MAAMC,EAAe,KAAK,IAAI,EAAGD,CAAM,EACvC,KAAK,QAAUC,EAEf,MAAMC,EAAS,CACX,YAAaD,EACb,UAAW,GACX,WAAY,CACxB,EAEQ,OAAI,KAAK,QAAU,IACf,KAAK,OAAS,EACd,KAAK,QAAO,EACZC,EAAO,UAAY,IAGhBA,CACX,CAKA,SAAU,CACN,KAAK,YAAc,GACnB,KAAK,OAAS,EAIlB,CAOA,OAAOC,EAAKC,EAAgB,EAAG,CAC3B,GAAI,CAAC,KAAK,QAAU,KAAK,YAAa,OAEtCD,EAAI,KAAI,EAER,MAAME,EAAK,KAAK,MAAM,KAAK,SAAS,CAAC,EAC/BC,EAAK,KAAK,MAAM,KAAK,SAAS,CAAC,EACrCH,EAAI,UAAUE,EAAIC,CAAE,EAGhB,KAAK,kBAAoB,KACzBH,EAAI,YAAc,KAAK,UACvBA,EAAI,WAAa,GAAK,KAAK,mBAI/B,MAAMI,EAAS,KAAK,iBAAgB,EACpC,GAAIA,EAAQ,CAER,MAAMC,EAAa,KAAK,MAAQ,IAGhCL,EAAI,YAAc,KAAK,MACvBA,EAAI,WAAa,GAEjBA,EAAI,UACAI,EACA,CAACC,EAAa,EACd,CAACA,EAAa,EACdA,EACAA,CAChB,EAEYL,EAAI,WAAa,CACrB,SAGIA,EAAI,UAAY,KAAK,MACrBA,EAAI,UAAS,EACbA,EAAI,OAAO,EAAG,CAAC,KAAK,OAAS,CAAC,EAC9BA,EAAI,OAAO,CAAC,KAAK,MAAQ,EAAG,KAAK,OAAS,CAAC,EAC3CA,EAAI,OAAO,CAAC,KAAK,MAAQ,EAAG,KAAK,OAAS,CAAC,EAC3CA,EAAI,OAAO,KAAK,MAAQ,EAAG,KAAK,OAAS,CAAC,EAC1CA,EAAI,OAAO,KAAK,MAAQ,EAAG,KAAK,OAAS,CAAC,EAC1CA,EAAI,UAAS,EACbA,EAAI,KAAI,EAGRA,EAAI,UAAY,UAChBA,EAAI,SAAS,GAAI,CAAC,KAAK,OAAS,EAAG,EAAG,CAAC,EAGnC,KAAK,kBAAoB,GAAK,CAC9B,MAAMM,EAAiB,GAAK,KAAK,kBAC3BC,EAAkB,KAAK,IAAI,KAAK,cAAgB,EAAE,EAAI,GAAM,GAElEP,EAAI,UAAY,qBAAqBO,EAAkB,KAAK,iBAAiB,IAC7EP,EAAI,SAAS,GAAI,KAAK,OAAS,EAAG,EAAGM,CAAc,EACnDN,EAAI,SAAS,EAAG,KAAK,OAAS,EAAG,EAAGM,CAAc,CACtD,CAIA,KAAK,cACL,KAAK,aAAa,OAAON,CAAG,EAGhCA,EAAI,QAAO,CACf,CAKA,uBAAwB,CACpB,KAAK,OAAS,CACV,KAAM,KAAK,SAAS,EAAI,KAAK,MAAQ,EACrC,MAAO,KAAK,SAAS,EAAI,KAAK,MAAQ,EACtC,IAAK,KAAK,SAAS,EAAI,KAAK,OAAS,EACrC,OAAQ,KAAK,SAAS,EAAI,KAAK,OAAS,CACpD,CACI,CAMA,WAAY,CACR,MAAO,CACH,OAAQ,KAAK,OACb,UAAW,KAAK,UAChB,iBAAkB,KAAK,OAAS,KAAK,UACrC,YAAa,KAAK,YAClB,UAAW,CAAC,CAAC,KAAK,cAClB,iBAAkB,KAAK,WAAa,KAAK,SAAS,SAAS,KAAK,WAAW,QAAQ,EAAI,CACnG,CACI,CAOA,iBAAiBQ,EAAW,CACxB,MAAI,CAAC,KAAK,QAAU,KAAK,YAAoB,GAGzC,KAAK,cAAgB,KAAK,aAAa,QAAO,EAEvC,KAAK,aAAa,KAAKA,CAAS,EAEpC,EACX,CAMA,kBAAmB,CACf,GAAI,OAAO,YAAc,OAAO,WAAW,cAAe,CACtD,MAAMJ,EAAS,OAAO,WAAW,cACjC,GAAIA,GAAUA,EAAO,SACjB,OAAOA,CAEf,CACA,OAAO,IACX,CAMA,iBAAiBK,EAAe,CAC5B,KAAK,cAAgBA,EACrB,KAAK,oBAAmB,CAC5B,CAMA,qBAAsB,CAElB,QAAQ,IAAI,2CAA2C,CAC3D,CAMA,kBAAmB,CACf,MAAO,CACH,YAAa,GACb,UAAW,KAAK,UAChB,aAAc,CACV,UAAW,KAAK,UAChB,SAAU,KAAK,QAC/B,CACA,CACI,CACJ"}