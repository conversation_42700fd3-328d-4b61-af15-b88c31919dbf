import{G as c,V as p,W as y}from"./main-Bxx4GkYV.js";class S extends c{constructor(t,e,s,i){super(t,e),this.playerShip=s,this.gameObjectManager=i,this.maxSpeed=250,this.acceleration=600,this.friction=.9,this.formationOffset=new p(-60,20),this.targetPosition=new p(t,e),this.followDistance=80,this.maxFormationDistance=150,this.fireRate=400,this.lastFireTime=0,this.targetingRange=300,this.currentTarget=null,this.width=48,this.height=64,this.color="#00ff88",this.glowColor="#44ffaa",this.maxHealth=50,this.health=this.maxHealth,this.isDestroyed=!1,this.animationTime=0,this.thrusterIntensity=0,this.weaponSystem=new y(this,i),this.weaponSystem.setFireRate(this.fireRate),this.weaponSystem.setProjectileDamage(20),this.weaponSystem.projectileType="wingman",this.addTag("wingman"),this.addTag("friendly"),this.hangarManager=null,this.baseStats={maxHealth:this.maxHealth,fireRate:this.fireRate}}update(t){!this.active||this.isDestroyed||(this.animationTime+=t/1e3,this.updateFormationFlying(t),this.updateCombatAI(t),this.weaponSystem&&this.weaponSystem.update(t),this.updateCollisionBounds())}updateFormationFlying(t){if(!this.playerShip||!this.playerShip.active)return;const e=this.playerShip.position,s=this.playerShip.velocity;let i=this.formationOffset.clone();if(s.magnitude()>50){const r=s.normalize(),o=new p(-r.y,r.x).multiply(40),l=r.multiply(-30);i=o.add(l)}this.targetPosition=e.add(i);const h=this.targetPosition.subtract(this.position),a=h.magnitude();if(a>this.followDistance){const o=h.normalize().multiply(this.maxSpeed).subtract(this.velocity),l=t/1e3,g=this.acceleration*l;o.magnitude()>g?this.velocity.addInPlace(o.normalize().multiply(g)):this.velocity.addInPlace(o),this.velocity.magnitude()>this.maxSpeed&&(this.velocity=this.velocity.normalize().multiply(this.maxSpeed)),this.thrusterIntensity=Math.min(1,a/100)}else this.velocity.multiplyInPlace(Math.pow(this.friction,t/16.67)),this.velocity.magnitude()<1&&this.velocity.set(0,0),this.thrusterIntensity*=.95;this.position.addInPlace(this.velocity.multiply(t/1e3));const n=50,d=this.playerShip.canvasWidth||800,m=this.playerShip.canvasHeight||600;this.position.x=Math.max(n,Math.min(this.position.x,d-n)),this.position.y=Math.max(n,Math.min(this.position.y,m-n))}updateCombatAI(t){if(this.lastFireTime+=t,this.currentTarget=this.findNearestEnemy(),this.currentTarget&&this.lastFireTime>=this.fireRate&&this.position.distance(this.currentTarget.position)<=this.targetingRange){const i=this.currentTarget.position.subtract(this.position).normalize();this.weaponSystem&&this.weaponSystem.fire(i)&&(this.lastFireTime=0)}}findNearestEnemy(){if(!this.gameObjectManager)return null;let t=null,e=this.targetingRange;const s=this.gameObjectManager.findByTag("enemy");for(const i of s){if(!i.active)continue;const h=this.position.distance(i.position);h<e&&(t=i,e=h)}return t}takeDamage(t){if(this.isDestroyed)return{damageTaken:0,destroyed:!0,scoreValue:0};const e=Math.max(0,t);this.health-=e;const s={damageTaken:e,destroyed:!1,scoreValue:0};return this.health<=0&&(this.health=0,this.destroy(),s.destroyed=!0),s}destroy(){this.isDestroyed=!0,this.active=!1}render(t,e=0){if(!this.active||this.isDestroyed)return;t.save();const s=Math.round(this.position.x),i=Math.round(this.position.y);t.translate(s,i),this.thrusterIntensity>.1&&(t.shadowColor=this.glowColor,t.shadowBlur=15*this.thrusterIntensity);const h=this.getWingmanSprite();if(h){const a=this.width*1.5;t.shadowColor=this.color,t.shadowBlur=15,t.drawImage(h,-a/2,-a/2,a,a),t.shadowBlur=0}else if(t.fillStyle=this.color,t.beginPath(),t.moveTo(0,-this.height/2),t.lineTo(-this.width/3,this.height/3),t.lineTo(-this.width/6,this.height/2),t.lineTo(this.width/6,this.height/2),t.lineTo(this.width/3,this.height/3),t.closePath(),t.fill(),t.fillStyle="#ffffff",t.fillRect(-2,-this.height/4,4,8),this.thrusterIntensity>.1){const a=15*this.thrusterIntensity,n=Math.sin(this.animationTime*20)*.3+.7;t.fillStyle=`rgba(0, 150, 255, ${n*this.thrusterIntensity})`,t.fillRect(-4,this.height/2,3,a),t.fillRect(1,this.height/2,3,a)}this.weaponSystem&&this.weaponSystem.render(t),t.restore()}updateCollisionBounds(){this.bounds={left:this.position.x-this.width/2,right:this.position.x+this.width/2,top:this.position.y-this.height/2,bottom:this.position.y+this.height/2}}getStatus(){return{health:this.health,maxHealth:this.maxHealth,healthPercentage:this.health/this.maxHealth,isDestroyed:this.isDestroyed,hasTarget:!!this.currentTarget,distanceToPlayer:this.playerShip?this.position.distance(this.playerShip.position):0}}fireOnPlayerFire(t){return!this.active||this.isDestroyed?!1:this.weaponSystem&&this.weaponSystem.isReady()?this.weaponSystem.fire(t):!1}getWingmanSprite(){if(window.gameEngine&&window.gameEngine.wingmanSprite){const t=window.gameEngine.wingmanSprite;if(t&&t.complete)return t}return null}setHangarManager(t){this.hangarManager=t,this.applyHangarUpgrades()}applyHangarUpgrades(){console.log("WingmanShip: No hangar upgrades available")}getUpgradeStatus(){return{hasUpgrades:!1,baseStats:this.baseStats,currentStats:{maxHealth:this.maxHealth,fireRate:this.fireRate}}}}export{S as WingmanShip};
//# sourceMappingURL=WingmanShip-BsAce294.js.map
