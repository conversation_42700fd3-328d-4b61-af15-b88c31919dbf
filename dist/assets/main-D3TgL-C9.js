var ye=Object.defineProperty;var be=(u,e,t)=>e in u?ye(u,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):u[e]=t;var $=(u,e,t)=>be(u,typeof e!="symbol"?e+"":e,t);(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))s(a);new MutationObserver(a=>{for(const i of a)if(i.type==="childList")for(const n of i.addedNodes)n.tagName==="LINK"&&n.rel==="modulepreload"&&s(n)}).observe(document,{childList:!0,subtree:!0});function t(a){const i={};return a.integrity&&(i.integrity=a.integrity),a.referrerPolicy&&(i.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?i.credentials="include":a.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function s(a){if(a.ep)return;a.ep=!0;const i=t(a);fetch(a.href,i)}})();class m{constructor(e=0,t=0){this.x=e,this.y=t}static zero(){return new m(0,0)}static one(){return new m(1,1)}static up(){return new m(0,-1)}static down(){return new m(0,1)}static left(){return new m(-1,0)}static right(){return new m(1,0)}add(e){return new m(this.x+e.x,this.y+e.y)}subtract(e){return new m(this.x-e.x,this.y-e.y)}multiply(e){return new m(this.x*e,this.y*e)}divide(e){if(e===0)throw new Error("Division by zero");return new m(this.x/e,this.y/e)}magnitude(){return Math.sqrt(this.x*this.x+this.y*this.y)}normalize(){const e=this.magnitude();return e===0?m.zero():this.divide(e)}distance(e){return this.subtract(e).magnitude()}dot(e){return this.x*e.x+this.y*e.y}addInPlace(e){return this.x+=e.x,this.y+=e.y,this}subtractInPlace(e){return this.x-=e.x,this.y-=e.y,this}multiplyInPlace(e){return this.x*=e,this.y*=e,this}normalizeInPlace(){const e=this.magnitude();return e>0&&(this.x/=e,this.y/=e),this}set(e,t){return this.x=e,this.y=t,this}setFromVector(e){return this.x=e.x,this.y=e.y,this}angle(){return Math.atan2(this.y,this.x)}static fromAngle(e,t=1){return new m(Math.cos(e)*t,Math.sin(e)*t)}rotate(e){const t=Math.cos(e),s=Math.sin(e),a=this.x*t-this.y*s,i=this.x*s+this.y*t;return new m(a,i)}rotateInPlace(e){const t=Math.cos(e),s=Math.sin(e),a=this.x*t-this.y*s,i=this.x*s+this.y*t;return this.x=a,this.y=i,this}perpendicular(){return new m(-this.y,this.x)}clone(){return new m(this.x,this.y)}equals(e,t=0){return t===0?this.x===e.x&&this.y===e.y:Math.abs(this.x-e.x)<=t&&Math.abs(this.y-e.y)<=t}toString(){return`Vector2(${this.x.toFixed(2)}, ${this.y.toFixed(2)})`}}class Ee{constructor(e,t=null){this.canvas=e,this.gameEngine=t,this.keys=new Map,this.keysPressed=new Map,this.keysReleased=new Map,this.mousePosition=new m(0,0),this.mouseButtons=new Map,this.mousePressed=new Map,this.mouseReleased=new Map,this.touches=new Map,this.touchStarted=new Map,this.touchEnded=new Map,this.keyMappings=new Map,this.setupDefaultMappings(),this.isTouchDevice="ontouchstart"in window,this.virtualJoystick=null,this.lastTapTime=0,this.lastTapPosition=new m(0,0),this.doubleTapThreshold=300,this.doubleTapDistance=30,this.handleKeyDown=this.handleKeyDown.bind(this),this.handleKeyUp=this.handleKeyUp.bind(this),this.handleMouseDown=this.handleMouseDown.bind(this),this.handleMouseUp=this.handleMouseUp.bind(this),this.handleMouseMove=this.handleMouseMove.bind(this),this.handleTouchStart=this.handleTouchStart.bind(this),this.handleTouchEnd=this.handleTouchEnd.bind(this),this.handleTouchMove=this.handleTouchMove.bind(this),this.init()}init(){document.addEventListener("keydown",this.handleKeyDown),document.addEventListener("keyup",this.handleKeyUp),this.canvas.addEventListener("mousedown",this.handleMouseDown),this.canvas.addEventListener("mouseup",this.handleMouseUp),this.canvas.addEventListener("mousemove",this.handleMouseMove),this.canvas.addEventListener("touchstart",this.handleTouchStart,{passive:!1}),this.canvas.addEventListener("touchend",this.handleTouchEnd,{passive:!1}),this.canvas.addEventListener("touchmove",this.handleTouchMove,{passive:!1}),this.canvas.addEventListener("contextmenu",e=>e.preventDefault()),this.isTouchDevice&&this.initVirtualJoystick()}setupDefaultMappings(){this.keyMappings.set("moveUp",["ArrowUp","KeyW"]),this.keyMappings.set("moveDown",["ArrowDown","KeyS"]),this.keyMappings.set("moveLeft",["ArrowLeft","KeyA"]),this.keyMappings.set("moveRight",["ArrowRight","KeyD"]),this.keyMappings.set("fire",["Space","Enter"]),this.keyMappings.set("pause",["Escape","KeyP"]),this.keyMappings.set("interact",["KeyE","KeyF"]),this.keyMappings.set("boost",["ShiftLeft","ShiftRight"]),this.keyMappings.set("debug",["F1"])}handleKeyDown(e){const t=e.code;this.keys.get(t)||this.keysPressed.set(t,!0),this.keys.set(t,!0),this.isGameKey(t)&&!this.isModalOpen()&&e.preventDefault()}handleKeyUp(e){const t=e.code;this.keys.set(t,!1),this.keysReleased.set(t,!0),this.isGameKey(t)&&!this.isModalOpen()&&e.preventDefault()}handleMouseDown(e){const t=e.button;this.mouseButtons.get(t)||this.mousePressed.set(t,!0),this.mouseButtons.set(t,!0),this.updateMousePosition(e),e.preventDefault()}handleMouseUp(e){const t=e.button;this.mouseButtons.set(t,!1),this.mouseReleased.set(t,!0),this.updateMousePosition(e),e.preventDefault()}handleMouseMove(e){this.updateMousePosition(e)}updateMousePosition(e){const t=this.canvas.getBoundingClientRect();this.mousePosition.set(e.clientX-t.left,e.clientY-t.top)}handleTouchStart(e){e.preventDefault();for(const t of e.changedTouches){const s=this.getTouchPosition(t);this.touches.set(t.identifier,s),this.touchStarted.set(t.identifier,s.clone()),this.checkForDoubleTap(s),this.virtualJoystick&&this.virtualJoystick.handleTouchStart(t.identifier,s)}}handleTouchEnd(e){e.preventDefault();for(const t of e.changedTouches){const s=this.getTouchPosition(t);this.touchEnded.set(t.identifier,s),this.touches.delete(t.identifier),this.virtualJoystick&&this.virtualJoystick.handleTouchEnd(t.identifier)}}handleTouchMove(e){e.preventDefault();for(const t of e.changedTouches){const s=this.getTouchPosition(t);this.touches.set(t.identifier,s),this.virtualJoystick&&this.virtualJoystick.handleTouchMove(t.identifier,s)}}getTouchPosition(e){const t=this.canvas.getBoundingClientRect();return new m(e.clientX-t.left,e.clientY-t.top)}isKeyDown(e){return this.keys.get(e)||!1}isKeyPressed(e){return this.keysPressed.get(e)||!1}isKeyReleased(e){return this.keysReleased.get(e)||!1}isMouseDown(e=0){return this.mouseButtons.get(e)||!1}isMousePressed(e=0){return this.mousePressed.get(e)||!1}isMouseReleased(e=0){return this.mouseReleased.get(e)||!1}isActionDown(e){const t=this.keyMappings.get(e);return!t||e==="fire"&&this.isMenuState()?!1:t.some(s=>this.isKeyDown(s))}isActionPressed(e){const t=this.keyMappings.get(e);if(!t||e==="fire"&&this.isMenuState())return!1;const s=t.some(a=>this.isKeyPressed(a));return e==="interact"&&s&&console.log("🎮 InputManager: interact action pressed!"),s}isActionReleased(e){const t=this.keyMappings.get(e);return t?t.some(s=>this.isKeyReleased(s)):!1}getMovementVector(){const e=new m(0,0);if(this.isActionDown("moveLeft")&&(e.x-=1),this.isActionDown("moveRight")&&(e.x+=1),this.isActionDown("moveUp")&&(e.y-=1),this.isActionDown("moveDown")&&(e.y+=1),this.virtualJoystick&&this.virtualJoystick.isActive()){const t=this.virtualJoystick.getInput();e.addInPlace(t)}return e.magnitude()>1&&e.normalizeInPlace(),e}setKeyMapping(e,t){this.keyMappings.set(e,Array.isArray(t)?t:[t])}addKeyMapping(e,t){const s=this.keyMappings.get(e)||[];s.push(t),this.keyMappings.set(e,s)}removeKeyMapping(e,t){const a=(this.keyMappings.get(e)||[]).filter(i=>i!==t);this.keyMappings.set(e,a)}isGameKey(e){for(const t of this.keyMappings.values())if(t.includes(e))return!0;return!1}isModalOpen(){return document.querySelector(".reality-warp-input-overlay")!==null}isMenuState(){return this.gameEngine?this.gameEngine.gameState==="MENU"||this.gameEngine.gameState==="PAUSED"||this.gameEngine.gameState==="LEVEL_COMPLETE":!1}checkForDoubleTap(e){const t=performance.now();t-this.lastTapTime<this.doubleTapThreshold&&e.distance(this.lastTapPosition)<this.doubleTapDistance&&(this.keysPressed.set("ShiftLeft",!0),console.log("Double-tap detected - activating boost")),this.lastTapTime=t,this.lastTapPosition.setFromVector(e)}initVirtualJoystick(){this.virtualJoystick=new Se(this.canvas)}update(){this.keysPressed.clear(),this.keysReleased.clear(),this.mousePressed.clear(),this.mouseReleased.clear(),this.touchStarted.clear(),this.touchEnded.clear(),this.virtualJoystick&&this.virtualJoystick.update()}resetInputState(){this.keys.clear(),this.keysPressed.clear(),this.keysReleased.clear(),this.mouseButtons.clear(),this.mousePressed.clear(),this.mouseReleased.clear(),this.touches.clear(),this.touchStarted.clear(),this.touchEnded.clear(),this.virtualJoystick&&(this.virtualJoystick.active=!1,this.virtualJoystick.touchId=null,this.virtualJoystick.knobPosition.setFromVector(this.virtualJoystick.center),this.virtualJoystick.firing=!1),console.log("InputManager: All input states reset for level transition")}render(e){this.virtualJoystick&&this.virtualJoystick.render(e)}destroy(){document.removeEventListener("keydown",this.handleKeyDown),document.removeEventListener("keyup",this.handleKeyUp),this.canvas.removeEventListener("mousedown",this.handleMouseDown),this.canvas.removeEventListener("mouseup",this.handleMouseUp),this.canvas.removeEventListener("mousemove",this.handleMouseMove),this.canvas.removeEventListener("touchstart",this.handleTouchStart),this.canvas.removeEventListener("touchend",this.handleTouchEnd),this.canvas.removeEventListener("touchmove",this.handleTouchMove),this.virtualJoystick&&this.virtualJoystick.destroy()}}class Se{constructor(e){this.canvas=e,this.active=!1,this.touchId=null,this.center=new m(100,e.height-100),this.knobPosition=new m(100,e.height-100),this.maxDistance=50,this.deadZone=.1,this.firing=!1,this.baseRadius=60,this.knobRadius=25,this.baseColor="rgba(255, 255, 255, 0.3)",this.knobColor="rgba(255, 255, 255, 0.7)",this.fireColor="rgba(255, 100, 100, 0.7)"}handleTouchStart(e,t){t.distance(this.center)<=this.baseRadius&&(this.active=!0,this.touchId=e,this.knobPosition.setFromVector(t),this.clampKnobPosition(),this.firing=!0)}handleTouchMove(e,t){this.active&&this.touchId===e&&(this.knobPosition.setFromVector(t),this.clampKnobPosition())}handleTouchEnd(e){this.active&&this.touchId===e&&(this.active=!1,this.touchId=null,this.knobPosition.setFromVector(this.center),this.firing=!1)}clampKnobPosition(){const e=this.knobPosition.subtract(this.center);e.magnitude()>this.maxDistance&&(e.normalizeInPlace().multiplyInPlace(this.maxDistance),this.knobPosition=this.center.add(e))}getInput(){if(!this.active)return new m(0,0);const e=this.knobPosition.subtract(this.center),t=e.magnitude()/this.maxDistance;return t<this.deadZone?new m(0,0):e.normalize().multiply(t)}isActive(){return this.active}isFiring(){return this.firing}update(){this.center.set(100,this.canvas.height-100),this.active||this.knobPosition.setFromVector(this.center)}render(e){e.save(),e.fillStyle=this.baseColor,e.strokeStyle="rgba(255, 255, 255, 0.5)",e.lineWidth=2,e.beginPath(),e.arc(this.center.x,this.center.y,this.baseRadius,0,Math.PI*2),e.fill(),e.stroke(),e.fillStyle=this.firing?this.fireColor:this.knobColor,e.beginPath(),e.arc(this.knobPosition.x,this.knobPosition.y,this.knobRadius,0,Math.PI*2),e.fill(),e.stroke(),e.restore()}destroy(){}}var W;let G=(W=class{constructor(e=0,t=0){this.position=new m(e,t),this.velocity=new m(0,0),this.acceleration=new m(0,0),this.rotation=0,this.scale=new m(1,1),this.active=!0,this.visible=!0,this.destroyed=!1,this.collisionRadius=0,this.collisionBounds={x:0,y:0,width:0,height:0},this.id=W.generateId(),this.tags=new Set}static generateId(){return++W.idCounter}update(e){this.active&&(this.velocity.addInPlace(this.acceleration.multiply(e/1e3)),this.position.addInPlace(this.velocity.multiply(e/1e3)),this.updateCollisionBounds())}render(e,t=0){if(!this.visible)return;const s=this.position.add(this.velocity.multiply(t/1e3));e.save(),e.translate(s.x,s.y),e.rotate(this.rotation),e.scale(this.scale.x,this.scale.y),e.restore()}updateCollisionBounds(){this.collisionBounds.x=this.position.x-this.collisionRadius,this.collisionBounds.y=this.position.y-this.collisionRadius,this.collisionBounds.width=this.collisionRadius*2,this.collisionBounds.height=this.collisionRadius*2}collidesWith(e){if(!this.active||!e.active)return!1;if(this.collisionRadius>0&&e.collisionRadius>0){const t=this.position.distance(e.position),s=this.collisionRadius+e.collisionRadius;return t<s}return!1}destroy(){this.destroyed=!0,this.active=!1,this.visible=!1}reset(){this.position.set(0,0),this.velocity.set(0,0),this.acceleration.set(0,0),this.rotation=0,this.scale.set(1,1),this.active=!0,this.visible=!0,this.destroyed=!1,this.tags.clear()}addTag(e){this.tags.add(e)}removeTag(e){this.tags.delete(e)}hasTag(e){return this.tags.has(e)}distanceTo(e){const t=e.position!==void 0?e.position:e;return this.position.distance(t)}directionTo(e){return(e.position!==void 0?e.position:e).subtract(this.position).normalize()}lookAt(e){const t=this.directionTo(e);this.rotation=t.angle()}moveTowards(e,t,s){const i=this.directionTo(e).multiply(t*s/1e3);this.position.addInPlace(i)}applyForce(e){this.acceleration.addInPlace(e)}isOutOfBounds(e){return this.position.x<e.left||this.position.x>e.right||this.position.y<e.top||this.position.y>e.bottom}wrapAroundBounds(e){this.position.x<e.left&&(this.position.x=e.right),this.position.x>e.right&&(this.position.x=e.left),this.position.y<e.top&&(this.position.y=e.bottom),this.position.y>e.bottom&&(this.position.y=e.top)}clampToBounds(e){this.position.x=Math.max(e.left,Math.min(e.right,this.position.x)),this.position.y=Math.max(e.top,Math.min(e.bottom,this.position.y))}},$(W,"idCounter",0),W);class j extends G{constructor(e=0,t=0){super(e,t),this.speed=600,this.damage=1,this.lifetime=3e3,this.age=0,this.ricochetEnabled=!1,this.ricochetCount=0,this.maxRicochets=3,this.ricochetTargets=new Set,this.width=4,this.height=12,this.collisionRadius=3,this.color="#FFD700",this.trailColor="#FFA500",this.weaponVariant="standard",this.effectivenessMultipliers={},this.trailPositions=[],this.maxTrailLength=8,this.trailFadeRate=.8,this.type="player",this.owner=null,this.addTag("projectile"),this.active=!1,this.visible=!1}initialize(e,t,s=600,a="player",i=null,n=!1,r=3){return this.position.setFromVector(e),this.velocity=t.normalize().multiply(s),this.speed=s,this.type=a,this.owner=i,this.age=0,this.ricochetEnabled=n,this.maxRicochets=r,this.ricochetCount=0,this.ricochetTargets.clear(),this.trailPositions=[],this.setupVisualsByType(),this.active=!0,this.visible=!0,this.destroyed=!1,this.tags.clear(),this.addTag("projectile"),this.addTag(a+"Projectile"),this}setupVisualsByType(){switch(this.type){case"player":this.color="#FFD700",this.trailColor="#FFA500",this.width=4,this.height=12;break;case"enemy":this.color="#FF4444",this.trailColor="#FF8888",this.width=3,this.height=8;break;default:this.color="#FFFFFF",this.trailColor="#CCCCCC";break}}update(e){if(this.active){if(this.age+=e,this.age>=this.lifetime){this.destroy();return}this.updateTrail(),super.update(e),this.updateCollisionBounds()}}updateTrail(){this.trailPositions.unshift(this.position.clone()),this.trailPositions.length>this.maxTrailLength&&this.trailPositions.pop()}render(e,t=0){if(!this.visible)return;const s=this.position.add(this.velocity.multiply(t/1e3));e.save(),this.renderTrail(e,t),this.renderProjectile(e,s),window.DEBUG_MODE&&this.renderDebugInfo(e,s),e.restore()}renderTrail(e,t){if(!(this.trailPositions.length<2)){e.strokeStyle=this.trailColor,e.lineWidth=2,e.lineCap="round";for(let s=0;s<this.trailPositions.length-1;s++){const a=Math.pow(this.trailFadeRate,s),i=this.trailPositions[s],n=this.trailPositions[s+1];let r=i;if(s===0){const o=this.velocity.multiply(t/1e3);r=i.add(o)}e.globalAlpha=a,e.beginPath(),e.moveTo(r.x,r.y),e.lineTo(n.x,n.y),e.stroke()}e.globalAlpha=1}}renderProjectile(e,t){e.translate(t.x,t.y),this.renderValueBasedBullet(e)}renderValueBasedBullet(e){e.shadowColor="#000000",e.shadowBlur=2,e.fillStyle="#000000",e.fillRect(-this.width/2-1,-this.height/2-1,this.width+2,this.height+2),e.shadowBlur=0,e.fillStyle=this.color,e.fillRect(-this.width/2,-this.height/2,this.width,this.height),e.shadowColor=this.color,e.shadowBlur=6,e.fillStyle="#FFFFFF",e.fillRect(-1,-this.height/2,2,this.height),e.shadowColor=this.color,e.shadowBlur=4,e.fillStyle=this.color,e.globalAlpha=.8,e.fillRect(-this.width/4,-this.height/2,this.width/2,this.height),e.globalAlpha=1,e.shadowBlur=0}drawPlayerProjectile(e){e.beginPath(),e.moveTo(0,-this.height/2),e.lineTo(this.width/2,0),e.lineTo(0,this.height/2),e.lineTo(-this.width/2,0),e.closePath(),e.fill(),e.stroke(),e.fillStyle=this.lightenColor(this.color,.3),e.beginPath(),e.moveTo(0,-this.height/4),e.lineTo(this.width/4,0),e.lineTo(0,this.height/4),e.lineTo(-this.width/4,0),e.closePath(),e.fill()}drawEnemyProjectile(e){e.beginPath(),e.ellipse(0,0,this.width/2,this.height/2,0,0,Math.PI*2),e.fill(),e.stroke(),e.fillStyle=this.lightenColor(this.color,.2),e.beginPath(),e.ellipse(0,-this.height/6,this.width/4,this.height/4,0,0,Math.PI*2),e.fill()}renderDebugInfo(e,t){e.resetTransform(),e.strokeStyle="#FF0000",e.lineWidth=1,e.beginPath(),e.arc(t.x,t.y,this.collisionRadius,0,Math.PI*2),e.stroke(),e.strokeStyle="#00FF00",e.lineWidth=1,e.beginPath(),e.moveTo(t.x,t.y);const s=t.add(this.velocity.multiply(.05));e.lineTo(s.x,s.y),e.stroke(),e.fillStyle="#FFFF00",e.font="10px Arial",e.fillText(`${Math.floor(this.age)}ms`,t.x+10,t.y-10)}isOutOfBounds(e){const t=Math.max(this.width,this.height);return this.position.x<e.left-t||this.position.x>e.right+t||this.position.y<e.top-t||this.position.y>e.bottom+t}onCollision(e){if((this.type==="player"&&e.hasTag("enemy")||this.type==="enemy"&&e.hasTag("player"))&&this.ricochetEnabled&&this.ricochetCount<this.maxRicochets&&!this.ricochetTargets.has(e.id)){this.ricochetTargets.add(e.id),this.performRicochet(e);return}this.destroy()}performRicochet(e){this.ricochetCount++;const t=this.getGameWorld();if(!t){this.destroy();return}const s=this.type==="player"?"enemy":"player",a=this.findNearestTarget(t,s,e);if(a){const i=a.position.subtract(this.position).normalize();this.velocity=i.multiply(this.speed),this.updateRicochetVisuals()}else this.destroy()}findNearestTarget(e,t,s){const a=e.entities;let i=null,n=1/0;for(const r of a)if(r.hasTag(t)&&r!==s&&!this.ricochetTargets.has(r.id)&&r.active){const o=this.position.distanceTo(r.position);o<n&&(n=o,i=r)}return i}updateRicochetVisuals(){if(this.type==="player"){const e=Math.min(255,255+this.ricochetCount*30);this.color=`rgb(${e}, ${215-this.ricochetCount*20}, 0)`,this.trailColor=`rgb(${e}, ${165-this.ricochetCount*15}, 0)`}else{const e=Math.min(255,68+this.ricochetCount*30),t=Math.min(255,68+this.ricochetCount*30);this.color=`rgb(255, ${e}, ${t})`,this.trailColor=`rgb(255, ${e+40}, ${t+40})`}this.width=Math.min(this.width+1,8),this.height=Math.min(this.height+2,16)}reset(){super.reset(),this.age=0,this.trailPositions=[],this.type="player",this.owner=null,this.speed=600,this.damage=1,this.lifetime=3e3,this.ricochetEnabled=!1,this.ricochetCount=0,this.maxRicochets=3,this.ricochetTargets.clear()}darkenColor(e,t){const s=e.replace("#",""),a=Math.floor(parseInt(s.substr(0,2),16)*(1-t)),i=Math.floor(parseInt(s.substr(2,2),16)*(1-t)),n=Math.floor(parseInt(s.substr(4,2),16)*(1-t));return`rgb(${a}, ${i}, ${n})`}lightenColor(e,t){const s=e.replace("#",""),a=Math.min(255,Math.floor(parseInt(s.substr(0,2),16)*(1+t))),i=Math.min(255,Math.floor(parseInt(s.substr(2,2),16)*(1+t))),n=Math.min(255,Math.floor(parseInt(s.substr(4,2),16)*(1+t)));return`rgb(${a}, ${i}, ${n})`}calculateEffectiveDamage(e){const t=this.effectivenessMultipliers[e]||1;return Math.round(this.damage*t)}}class Me{constructor(e,t,s=null){this.owner=e,this.gameObjectManager=t,this.audioManager=s,this.fireRate=600,this.lastFireTime=0,this.canFire=!0,this.isDisabled=!1,this.disableEndTime=0,this.projectileSpeed=600,this.projectileDamage=25,this.projectileLifetime=3e3,this.projectileType="player",this.currentPattern="single",this.previousPattern="single",this.spreadAngle=Math.PI/6,this.chunkingEnabled=!1,this.chunkSize=3,this.chunkSpacing=15,this.chunkFormation="line",this.ricochetEnabled=!1,this.ricochetCount=0,this.currentVariant="standard",this.variantProperties=this.initializeVariantProperties(),this.muzzleFlashDuration=100,this.muzzleFlashTime=0,this.muzzleFlashPositions=[],this.fireSound=null,this.soundVolume=.5,this.initializeProjectilePool()}initializeProjectilePool(){this.gameObjectManager.pools.has("projectile")||this.gameObjectManager.createPool("projectile",()=>new j,e=>e.reset(),20)}update(e){this.canFire||(this.lastFireTime+=e,this.lastFireTime>=this.fireRate&&(this.canFire=!0,this.lastFireTime=0)),this.muzzleFlashTime>0&&(this.muzzleFlashTime-=e,this.muzzleFlashTime<=0&&(this.muzzleFlashPositions=[]))}fire(e=m.up()){const t=Date.now();if(this.isDisabled&&t<this.disableEndTime||(this.isDisabled&&t>=this.disableEndTime&&(this.isDisabled=!1,this.disableEndTime=0),!this.canFire))return!1;switch(this.currentPattern){case"single":this.fireSingle(e);break;case"double":this.fireDouble(e);break;case"triple":this.fireTriple(e);break;case"spread":this.fireSpread(e);break;default:this.fireSingle(e);break}return this.canFire=!1,this.lastFireTime=0,this.triggerMuzzleFlash(),this.playFireSound(),!0}fireSingle(e){const t=this.getFirePosition();this.createProjectile(t,e)}fireDouble(e){const t=this.getFirePosition(),s=e.perpendicular().multiply(8);this.createProjectile(t.subtract(s),e),this.createProjectile(t.add(s),e)}fireTriple(e){const t=this.getFirePosition(),s=e.perpendicular().multiply(12);this.createProjectile(t,e),this.createProjectile(t.subtract(s),e),this.createProjectile(t.add(s),e)}fireSpread(e){const t=this.getFirePosition(),s=e.angle(),a=this.spreadAngle/2;for(let i=-2;i<=2;i++){const n=s+i*a,r=m.fromAngle(n);this.createProjectile(t,r)}}createProjectile(e,t){const s=new j;s.initialize(e,t,this.projectileSpeed,this.projectileType,this.owner),s.damage=this.projectileDamage,s.lifetime=this.projectileLifetime;const a=this.getCurrentVariantProperties();s.color=a.color,s.trailColor=a.trailColor,s.weaponVariant=this.currentVariant,s.effectivenessMultipliers=a.effectivenessMultipliers,this.ricochetEnabled&&this.ricochetCount>0&&(s.ricochetEnabled=!0,s.ricochetCount=this.ricochetCount,s.ricochetRemaining=this.ricochetCount),this.gameObjectManager.add(s)}getFirePosition(){const e=this.owner.position.clone(),t=new m(0,-this.owner.height/2-5);return e.add(t)}triggerMuzzleFlash(){switch(this.muzzleFlashTime=this.muzzleFlashDuration,this.muzzleFlashPositions=[],this.currentPattern){case"single":this.muzzleFlashPositions.push(this.getFirePosition());break;case"double":const e=this.getFirePosition(),t=new m(8,0);this.muzzleFlashPositions.push(e.subtract(t)),this.muzzleFlashPositions.push(e.add(t));break;case"triple":const s=this.getFirePosition(),a=new m(12,0);this.muzzleFlashPositions.push(s),this.muzzleFlashPositions.push(s.subtract(a)),this.muzzleFlashPositions.push(s.add(a));break;case"spread":this.muzzleFlashPositions.push(this.getFirePosition());break}}playFireSound(){this.audioManager?this.audioManager.playSound("player_shot",{volume:this.soundVolume,pitch:1}):this.fireSound&&typeof this.fireSound.play=="function"&&(this.fireSound.volume=this.soundVolume,this.fireSound.currentTime=0,this.fireSound.play().catch(e=>{console.warn("Could not play fire sound:",e)}))}render(e){this.muzzleFlashTime>0&&this.renderMuzzleFlash(e)}renderMuzzleFlash(e){const t=this.muzzleFlashTime/this.muzzleFlashDuration;e.save(),e.globalAlpha=t;for(const s of this.muzzleFlashPositions){const a=e.createRadialGradient(s.x,s.y,0,s.x,s.y,15);a.addColorStop(0,"#FFFFFF"),a.addColorStop(.3,"#FFD700"),a.addColorStop(.6,"#FF6B35"),a.addColorStop(1,"rgba(255, 107, 53, 0)"),e.fillStyle=a,e.beginPath(),e.arc(s.x,s.y,15,0,Math.PI*2),e.fill(),e.fillStyle="#FFFFFF",e.beginPath(),e.arc(s.x,s.y,3,0,Math.PI*2),e.fill()}e.restore()}setPattern(e){["single","double","triple","spread"].includes(e)?this.currentPattern=e:console.warn(`Invalid weapon pattern: ${e}`)}setFireRate(e){this.fireRate=Math.max(50,e)}setProjectileSpeed(e){this.projectileSpeed=Math.max(100,e)}setProjectileDamage(e){this.projectileDamage=Math.max(1,e)}initializeVariantProperties(){return{standard:{name:"Standard Rounds",color:"#FFD700",trailColor:"#FFA500",effectivenessMultipliers:{},cost:0,description:"Basic ammunition with balanced performance",icon:"🔸"},kinetic:{name:"Kinetic Impact",color:"#FF69B4",trailColor:"#FF1493",effectivenessMultipliers:{crystal:2,shadow:1.5},cost:5e3,description:"High-impact rounds effective against crystal enemies and shadow types",icon:"💎"},laser:{name:"Laser Focus",color:"#FF0000",trailColor:"#FF4444",effectivenessMultipliers:{shadow:2,crystal:2},cost:5e3,description:"Focused energy beams that pierce through shadow and crystal defenses",icon:"🔴"},flame:{name:"Flame Rounds",color:"#FF4500",trailColor:"#FFD700",effectivenessMultipliers:{air:2,crystal:1.5},cost:5e3,description:"Incendiary ammunition that burns through air enemies",icon:"🔥"},ice:{name:"Ice Rounds",color:"#00BFFF",trailColor:"#87CEEB",effectivenessMultipliers:{fire:2,earth:1.5},cost:5e3,description:"Freezing projectiles that extinguish fire enemies",icon:"❄️"},plasma:{name:"Plasma Storm",color:"#9370DB",trailColor:"#DDA0DD",effectivenessMultipliers:{earth:2,water:1.5},cost:7500,description:"Supercharged plasma that disrupts earth and water enemies",icon:"⚡"},sonic:{name:"Sonic Rounds",color:"#32CD32",trailColor:"#00FF00",effectivenessMultipliers:{crystal:2,earth:1.5},cost:7500,description:"Sonic vibrations that shatter crystal defenses",icon:"🔊"}}}setSpreadAngle(e){this.spreadAngle=Math.max(0,Math.min(Math.PI,e))}isReady(){return this.canFire}getCooldownProgress(){return this.canFire?1:this.lastFireTime/this.fireRate}resetCooldown(){this.canFire=!0,this.lastFireTime=0}enableSpreadPattern(e){if(e)this.previousPattern=this.currentPattern,this.setPattern("spread");else{const t=this.previousPattern||"single";this.setPattern(t)}}enableRicochetRounds(e,t=3){this.ricochetEnabled=e,this.ricochetCount=e?t:0,console.log(e?`Ricochet rounds enabled: ${t} bounces per projectile`:"Ricochet rounds disabled")}disableForDuration(e){this.isDisabled=!0,this.disableEndTime=Date.now()+e,console.log(`Weapon disabled for ${e}ms`)}getStats(){return{pattern:this.currentPattern,fireRate:this.fireRate,projectileSpeed:this.projectileSpeed,projectileDamage:this.projectileDamage,isReady:this.canFire,cooldownProgress:this.getCooldownProgress(),ricochetEnabled:this.ricochetEnabled,ricochetCount:this.ricochetCount}}setWeaponVariant(e){return this.variantProperties[e]?(this.currentVariant=e,console.log(`Weapon variant changed to: ${this.variantProperties[e].name}`),!0):(console.warn(`Invalid weapon variant: ${e}`),!1)}getCurrentVariantProperties(){return this.variantProperties[this.currentVariant]}getAllVariants(){return this.variantProperties}calculateEffectiveDamage(e,t){const a=this.getCurrentVariantProperties().effectivenessMultipliers[e]||1;return Math.round(t*a)}enableBulletChunking(e,t=3,s="line"){this.chunkingEnabled=e,this.chunkSize=t,this.chunkFormation=s}createBulletChunk(e,t,s){const a=this.calculateChunkPositions(e,t,s);for(const i of a)this.createProjectile(i,t)}calculateChunkPositions(e,t,s){const a=[],i=t.perpendicular();switch(this.chunkFormation){case"line":for(let n=0;n<s;n++){const r=(n-(s-1)/2)*this.chunkSpacing,o=e.add(i.multiply(r));a.push(o)}break;case"triangle":a.push(e),s>1&&(a.push(e.add(i.multiply(-this.chunkSpacing))),s>2&&a.push(e.add(i.multiply(this.chunkSpacing))));break;case"diamond":a.push(e),s>1&&(a.push(e.add(t.multiply(this.chunkSpacing*.7))),s>2&&(a.push(e.add(t.multiply(-this.chunkSpacing*.7))),s>3&&a.push(e.add(i.multiply(this.chunkSpacing)))));break;default:for(let n=0;n<s;n++){const r=(n-(s-1)/2)*this.chunkSpacing,o=e.add(i.multiply(r));a.push(o)}}return a}}class Ce extends G{constructor(e,t,s,a,i=null,n=null){super(e,t),this.canvasWidth=s,this.canvasHeight=a,this.baseMaxSpeed=300,this.maxSpeed=this.baseMaxSpeed,this.acceleration=800,this.friction=.85,this.boostActive=!1,this.boostMultiplier=2,this.maxHealth=100,this.health=this.maxHealth,this.maxLives=3,this.lives=this.maxLives,this.isInvulnerable=!1,this.invulnerabilityDuration=2e3,this.invulnerabilityTimer=0,this.isDestroyed=!1,this.damageFlashTimer=0,this.damageFlashDuration=200,this.isFlashing=!1;const r=window.innerWidth<=768;this.width=r?32:64,this.height=r?48:96,this.collisionRadius=r?16:32,this.boundaryPadding=Math.max(this.width,this.height)/2,this.animationTime=0,this.thrusterAnimationSpeed=8,this.isMoving=!1,this.movementInput=new m(0,0),this.weaponSystem=null,i&&(this.weaponSystem=new Me(this,i,n),console.log(`PlayerShip weapon system created with fireRate: ${this.weaponSystem.fireRate}ms`)),this.activePowerUps=new Map,this.hasShield=!1,this.addTag("player"),this.hangarManager=null,this.baseStats={baseMaxSpeed:this.baseMaxSpeed,maxSpeed:this.maxSpeed,acceleration:this.acceleration,maxHealth:this.maxHealth,maxLives:this.maxLives,boostMultiplier:this.boostMultiplier}}update(e,t=new m(0,0)){if(this.active){if(this.movementInput=t.clone(),this.isMoving=t.magnitude()>.1,this.updateMaxSpeed(),this.isMoving){const s=t.multiply(this.maxSpeed),a=e/1e3,i=this.acceleration*a,n=s.subtract(this.velocity);n.magnitude()>i?this.velocity.addInPlace(n.normalize().multiply(i)):this.velocity.addInPlace(n),this.velocity.magnitude()>this.maxSpeed&&(this.velocity=this.velocity.normalize().multiply(this.maxSpeed))}else this.velocity.multiplyInPlace(Math.pow(this.friction,e/16.67)),this.velocity.magnitude()<1&&this.velocity.set(0,0);this.position.addInPlace(this.velocity.multiply(e/1e3)),this.checkBoundaries(),this.animationTime+=e/1e3,this.updateHealthSystem(e),this.updateCollisionBounds(),this.weaponSystem&&this.weaponSystem.update(e)}}checkBoundaries(){const e=this.boundaryPadding,t=this.canvasWidth-this.boundaryPadding,s=this.canvasHeight*2/3,a=this.canvasHeight-this.boundaryPadding;this.position.x<e?(this.position.x=e,this.velocity.x=Math.max(0,this.velocity.x)):this.position.x>t&&(this.position.x=t,this.velocity.x=Math.min(0,this.velocity.x)),this.position.y<s?(this.position.y=s,this.velocity.y=Math.max(0,this.velocity.y)):this.position.y>a&&(this.position.y=a,this.velocity.y=Math.min(0,this.velocity.y))}render(e,t=0){if(!this.visible)return;e.save();const s=Math.round(this.position.x),a=Math.round(this.position.y);e.translate(s,a),e.rotate(this.rotation),this.drawShipBody(e),this.isMoving&&this.drawThrusterEffects(e),window.DEBUG_MODE&&this.drawDebugInfo(e),e.restore(),this.weaponSystem&&this.weaponSystem.render(e)}drawShipBody(e){let t=1,s="#4A90E2",a="#2E5C8A";if(this.isInvulnerable){const r=Math.sin(this.invulnerabilityTimer*8*Math.PI/1e3);t=.3+.7*Math.abs(r)}if(this.isFlashing){const n=this.damageFlashTimer/this.damageFlashDuration;s=this.interpolateColor("#4A90E2","#FF4444",n),a=this.interpolateColor("#2E5C8A","#CC2222",n)}e.globalAlpha=t;const i=this.getPlayerSprite();if(i){const n=this.width*1.5;e.shadowColor=s,e.shadowBlur=15,e.drawImage(i,-n/2,-n/2,n,n),e.shadowBlur=0}else e.fillStyle=s,e.strokeStyle=a,e.lineWidth=2,e.beginPath(),e.moveTo(0,-this.height/2),e.lineTo(-this.width/3,this.height/3),e.lineTo(this.width/3,this.height/3),e.closePath(),e.fill(),e.stroke(),e.fillStyle="#7BB3F0",e.beginPath(),e.ellipse(0,-this.height/4,this.width/6,this.height/8,0,0,Math.PI*2),e.fill(),e.fillStyle="#2E5C8A",e.fillRect(-this.width/4,this.height/6,this.width/8,this.height/4),e.fillRect(this.width/8,this.height/6,this.width/8,this.height/4),e.shadowColor="#87CEEB",e.shadowBlur=10,e.fillStyle="#87CEEB",e.beginPath(),e.ellipse(-this.width/6,this.height/3,3,6,0,0,Math.PI*2),e.ellipse(this.width/6,this.height/3,3,6,0,0,Math.PI*2),e.fill(),e.shadowBlur=0;e.globalAlpha=1}drawThrusterEffects(e){const t=this.velocity.magnitude()/this.maxSpeed,s=Math.sin(this.animationTime*this.thrusterAnimationSpeed*Math.PI*2),a=15*t,i=5*s*t,n=a+i;n>2&&(this.drawThrusterFlame(e,-this.width/6,this.height/3,n),this.drawThrusterFlame(e,this.width/6,this.height/3,n)),this.drawDirectionalThrusters(e,t,s)}drawThrusterFlame(e,t,s,a){e.shadowColor="#FFD700",e.shadowBlur=10;const i=e.createLinearGradient(t,s,t,s+a);i.addColorStop(0,"#FFD700"),i.addColorStop(.5,"#FF6B35"),i.addColorStop(1,"rgba(255, 0, 0, 0)"),e.fillStyle=i,e.beginPath(),e.moveTo(t-3,s),e.lineTo(t+3,s),e.lineTo(t+1,s+a),e.lineTo(t-1,s+a),e.closePath(),e.fill(),e.shadowBlur=0}drawDirectionalThrusters(e,t,s){const a=3*t,i=.7*t;if(Math.abs(this.movementInput.x)>.1&&(e.fillStyle=`rgba(135, 206, 235, ${i})`,this.movementInput.x>0?e.fillRect(-this.width/2-a,-2,a,4):e.fillRect(this.width/2,-2,a,4)),this.movementInput.y<-.1){e.fillStyle=`rgba(255, 215, 0, ${i})`;const n=8*t*(1+.3*s);e.fillRect(-2,-this.height/2-n,4,n)}}drawDebugInfo(e){if(e.strokeStyle="#FF0000",e.lineWidth=1,e.beginPath(),e.arc(0,0,this.collisionRadius,0,Math.PI*2),e.stroke(),this.velocity.magnitude()>1){e.strokeStyle="#00FF00",e.lineWidth=2,e.beginPath(),e.moveTo(0,0);const t=.1;e.lineTo(this.velocity.x*t,this.velocity.y*t),e.stroke()}e.fillStyle="#FFFF00",e.fillRect(-1,-1,2,2)}getCurrentSpeed(){return this.velocity.magnitude()}getBoundaryStatus(){const e=this.boundaryPadding,t=this.canvasWidth-this.boundaryPadding,s=this.boundaryPadding,a=this.canvasHeight-this.boundaryPadding;return{left:this.position.x<=e,right:this.position.x>=t,top:this.position.y<=s,bottom:this.position.y>=a}}resetToPosition(e,t){this.position.set(e,t),this.velocity.set(0,0),this.rotation=0,this.animationTime=0,this.isMoving=!1,this.movementInput.set(0,0),this.active=!0,this.visible=!0,this.destroyed=!1}setBoostActive(e){this.boostActive=e,this.updateMaxSpeed()}isBoostActive(){return this.boostActive}updateMaxSpeed(){this.boostActive?this.maxSpeed=this.baseMaxSpeed*this.boostMultiplier:this.maxSpeed=this.baseMaxSpeed}setBoostMultiplier(e){this.boostMultiplier=e,this.baseStats.boostMultiplier=e,this.updateMaxSpeed()}setBaseMaxSpeed(e){this.baseMaxSpeed=e,this.baseStats.baseMaxSpeed=e,this.updateMaxSpeed()}updateCanvasDimensions(e,t){this.canvasWidth=e,this.canvasHeight=t,this.checkBoundaries()}fire(e=m.up()){let t=!1;if(this.weaponSystem&&(t=this.weaponSystem.fire(e)),this.weaponSystem&&this.weaponSystem.gameObjectManager){const s=this.weaponSystem.gameObjectManager.findByTag("wingman");for(const a of s)a.active&&!a.isDestroyed&&typeof a.fireOnPlayerFire=="function"&&a.fireOnPlayerFire(e)}return t}setWeaponSystem(e){this.weaponSystem=e}getWeaponSystem(){return this.weaponSystem}canFire(){return this.weaponSystem?this.weaponSystem.isReady():!1}setWeaponPattern(e){this.weaponSystem&&this.weaponSystem.setPattern(e)}getWeaponStats(){return this.weaponSystem?this.weaponSystem.getStats():null}updateHealthSystem(e){this.isInvulnerable&&(this.invulnerabilityTimer-=e,this.invulnerabilityTimer<=0&&(this.isInvulnerable=!1,this.invulnerabilityTimer=0)),this.isFlashing&&(this.damageFlashTimer-=e,this.damageFlashTimer<=0&&(this.isFlashing=!1,this.damageFlashTimer=0))}takeDamage(e){if(this.isInvulnerable||this.isDestroyed)return{damageTaken:0,health:this.health,lives:this.lives,destroyed:this.isDestroyed};const t=Math.min(e,this.health);return this.health=Math.max(0,this.health-t),(isNaN(this.health)||!isFinite(this.health))&&(console.error("Invalid health value detected in takeDamage, resetting to 0"),this.health=0),this.isFlashing=!0,this.damageFlashTimer=this.damageFlashDuration,this.health<=0?this.destroyShip():(this.isInvulnerable=!0,this.invulnerabilityTimer=this.invulnerabilityDuration),{damageTaken:t,health:this.health,lives:this.lives,destroyed:this.isDestroyed}}destroyShip(){this.health=0,this.lives--,this.lives<=0?(this.isDestroyed=!0,this.active=!1,window.gameEngine&&typeof window.gameEngine.handleGameOver=="function"&&window.gameEngine.handleGameOver()):this.respawn()}respawn(){this.health=this.maxHealth;const e=this.canvasWidth/2,t=this.canvasHeight-100;this.resetToPosition(e,t),this.isInvulnerable=!0,this.invulnerabilityTimer=this.invulnerabilityDuration*2,this.isFlashing=!1,this.damageFlashTimer=0}addLives(e){this.lives+=e}getHealthStatus(){return{health:this.health,maxHealth:this.maxHealth,healthPercentage:this.maxHealth>0?Math.max(0,Math.min(1,this.health/this.maxHealth)):0,lives:this.lives,maxLives:this.maxLives,isInvulnerable:this.isInvulnerable,invulnerabilityTimeRemaining:this.invulnerabilityTimer,isDestroyed:this.isDestroyed,isFlashing:this.isFlashing}}resetHealthAndLives(){this.health=this.maxHealth,this.lives=this.maxLives,this.isInvulnerable=!1,this.invulnerabilityTimer=0,this.isDestroyed=!1,this.isFlashing=!1,this.damageFlashTimer=0,this.hangarManager&&this.applyHangarUpgrades()}resetHealthOnly(){this.health=this.maxHealth,this.isInvulnerable=!1,this.invulnerabilityTimer=0,this.isDestroyed=!1,this.isFlashing=!1,this.damageFlashTimer=0,this.hangarManager&&this.applyHangarUpgrades()}setHangarManager(e){this.hangarManager=e,this.applyHangarUpgrades()}applyHangarUpgrades(){if(!this.hangarManager)return;const e=this.hangarManager.calculatePlayerStats();if(e.maxSpeed!==void 0&&(this.maxSpeed=e.maxSpeed),e.acceleration!==void 0&&(this.acceleration=e.acceleration),e.maxHealth!==void 0){const t=this.maxHealth;if(this.maxHealth=e.maxHealth,this.health<t&&this.health>0){const s=this.health/t;this.health=Math.floor(this.maxHealth*s)}else this.health===t&&(this.health=this.maxHealth)}if(e.maxLives!==void 0){const t=this.maxLives;this.maxLives=e.maxLives,this.lives===t&&e.maxLives>t&&(this.lives=this.maxLives)}this.weaponSystem&&(e.fireRate!==void 0&&this.weaponSystem.setFireRate(e.fireRate),e.projectileDamage!==void 0&&this.weaponSystem.setProjectileDamage(e.projectileDamage)),console.log("Applied hangar upgrades to PlayerShip:",e)}getUpgradeStatus(){var s,a;if(!this.hangarManager)return{hasUpgrades:!1,baseStats:this.baseStats,currentStats:{maxSpeed:this.maxSpeed,acceleration:this.acceleration,maxHealth:this.maxHealth,maxLives:this.maxLives}};const e=this.hangarManager.calculatePlayerStats(),t=this.hangarManager.getUpgradeInvestment();return{hasUpgrades:t.playerUpgradeLevels>0,baseStats:this.baseStats,currentStats:{maxSpeed:this.maxSpeed,acceleration:this.acceleration,maxHealth:this.maxHealth,maxLives:this.maxLives,fireRate:(s=this.weaponSystem)==null?void 0:s.fireRate,projectileDamage:(a=this.weaponSystem)==null?void 0:a.projectileDamage},upgradedStats:e,investment:t}}interpolateColor(e,t,s){s=Math.max(0,Math.min(1,s));const a=e.replace("#",""),i=t.replace("#",""),n=parseInt(a.substr(0,2),16),r=parseInt(a.substr(2,2),16),o=parseInt(a.substr(4,2),16),c=parseInt(i.substr(0,2),16),h=parseInt(i.substr(2,2),16),d=parseInt(i.substr(4,2),16),g=Math.round(n+(c-n)*s),w=Math.round(r+(h-r)*s),v=Math.round(o+(d-o)*s),p=f=>{const y=f.toString(16);return y.length===1?"0"+y:y};return`#${p(g)}${p(w)}${p(v)}`}getPlayerSprite(){if(window.gameEngine&&window.gameEngine.playerSprite){const e=window.gameEngine.playerSprite;if(e&&e.complete)return e}return null}}const Te="modulepreload",Ae=function(u){return"/"+u},Q={},Pe=function(e,t,s){let a=Promise.resolve();if(t&&t.length>0){document.getElementsByTagName("link");const n=document.querySelector("meta[property=csp-nonce]"),r=(n==null?void 0:n.nonce)||(n==null?void 0:n.getAttribute("nonce"));a=Promise.allSettled(t.map(o=>{if(o=Ae(o),o in Q)return;Q[o]=!0;const c=o.endsWith(".css"),h=c?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${o}"]${h}`))return;const d=document.createElement("link");if(d.rel=c?"stylesheet":Te,c||(d.as="script"),d.crossOrigin="",d.href=o,r&&d.setAttribute("nonce",r),document.head.appendChild(d),c)return new Promise((g,w)=>{d.addEventListener("load",g),d.addEventListener("error",()=>w(new Error(`Unable to preload CSS for ${o}`)))})}))}function i(n){const r=new Event("vite:preloadError",{cancelable:!0});if(r.payload=n,window.dispatchEvent(r),!r.defaultPrevented)throw n}return a.then(n=>{for(const r of n||[])r.status==="rejected"&&i(r.reason);return e().catch(i)})},E={CANVAS_WIDTH:800,CANVAS_HEIGHT:600,TARGET_FPS:60,PLAYER_LIVES:3,PLAYER_HEALTH:100,BASE_LEVEL_REWARD:1250,POWER_UP_COSTS:{EXTRA_WINGMAN:1e4,EXTRA_LIFE:15e3,SPREAD_AMMO:7500,REALITY_WARP:25e3},REWARD_SYSTEM:{BASE_VARIANCE:.4,PERFECT_SCORE_VARIANCE:.2,POOR_SCORE_VARIANCE:.6,POWER_UP_VARIANCE_REDUCTION:.1,MAX_VARIANCE_REDUCTION:.3,HOUSE_EDGE:.15,MIN_REWARD_RATIO:.3,MAX_REWARD_CHANCE:.1,ABOVE_AVERAGE_CHANCE:.25,AVERAGE_CHANCE:.4,BELOW_AVERAGE_CHANCE:.2,MIN_REWARD_CHANCE:.05},WARP_BASE_COST:2e4,RAFFLE_PRIZE_POOL_PERCENTAGE:.5,RAFFLE_PRIZES:{GOLD:25e3,SILVER:15e3,BRONZE:1e4},DEBUG_MODE:!0,ENABLE_CONSOLE_LOGS:!0,API_BASE_URL:"https://warpsector.merchgenieai.com/api"},P={SPACE:"space",WATER:"water",FIRE:"fire",AIR:"air",EARTH:"earth",CRYSTAL:"crystal",SHADOW:"shadow"},l={WATER:"water",FIRE:"fire",AIR:"air",EARTH:"earth",CRYSTAL:"crystal",SHADOW:"shadow"},U=class U{constructor(e,t,s=null,a=""){this.type=e,this.cost=t,this.duration=s,this.description=a,this.isActive=!1,this.timeRemaining=s,this.appliedAt=null,this.id=U.generateId(),this.icon=null,this.color="#00ffff",this.glowColor="#ffffff"}static generateId(){return`powerup_${++U.idCounter}`}async apply(e){if(this.isActive)return console.warn(`PowerUp ${this.type} is already active`),!1;this.isActive=!0,this.appliedAt=Date.now(),this.timeRemaining=this.duration===0?null:this.duration;const t=await this.applyEffect(e);return t||(this.isActive=!1,this.appliedAt=null,this.timeRemaining=null),t}remove(e){return this.isActive?(this.isActive=!1,this.timeRemaining=null,this.removeEffect(e)):(console.warn(`PowerUp ${this.type} is not active`),!1)}update(e,t){return this.isActive?this.timeRemaining!==null&&(this.timeRemaining-=e,this.timeRemaining<=0)?(this.remove(t),!1):!0:!1}getTimeRemainingPercentage(){return this.duration===null?1:this.isActive?Math.max(0,this.timeRemaining/this.duration):0}getFormattedTimeRemaining(){return this.duration===null?"Active for Level":this.isActive?`${Math.ceil(this.timeRemaining/1e3)}s`:"Inactive"}canPurchase(e,t){return{canPurchase:t>=this.cost&&!this.isActive,reason:t<this.cost?"insufficient_tokens":this.isActive?"already_active":"available"}}applyEffect(e){return!0}removeEffect(e){return!0}getDisplayInfo(){return{id:this.id,type:this.type,cost:this.cost,duration:this.duration,description:this.description,isActive:this.isActive,timeRemaining:this.timeRemaining,timeRemainingPercentage:this.getTimeRemainingPercentage(),formattedTimeRemaining:this.getFormattedTimeRemaining(),icon:this.icon,color:this.color,glowColor:this.glowColor}}};$(U,"idCounter",0);let A=U;class Re extends A{constructor(){super("EXTRA_LIFE",E.POWER_UP_COSTS.EXTRA_LIFE,null,"Gain an extra life to continue your journey"),this.icon="❤️",this.color="#ff4444",this.glowColor="#ff8888"}applyEffect(e){return e.addLives(1),!0}canPurchase(e,t){return{canPurchase:t>=this.cost,reason:t<this.cost?"insufficient_tokens":"available"}}}class ke extends A{constructor(){super("SPREAD_AMMO",E.POWER_UP_COSTS.SPREAD_AMMO,null,"Fire projectiles in a spread pattern for better coverage"),this.icon="🔥",this.color="#ffaa00",this.glowColor="#ffdd44"}applyEffect(e){return e.weaponSystem?(e.weaponSystem.enableSpreadPattern(!0),!0):!1}removeEffect(e){return e.weaponSystem?(e.weaponSystem.enableSpreadPattern(!1),!0):!1}}class Ie extends A{constructor(){super("EXTRA_WINGMAN",E.POWER_UP_COSTS.EXTRA_WINGMAN,null,"Deploy a wingman ship to provide covering fire"),this.icon="🚀",this.color="#00ff88",this.glowColor="#44ffaa",this.wingmanShip=null}async applyEffect(e){var t;try{const{WingmanShip:s}=await Pe(async()=>{const{WingmanShip:n}=await import("./WingmanShip-CiYChGmd.js");return{WingmanShip:n}},[]),a=(t=e.weaponSystem)==null?void 0:t.gameObjectManager;if(!a)return console.error("Cannot create wingman: GameObjectManager not available"),!1;const i=e.position.add(new m(-40,0));return this.wingmanShip=new s(i.x,i.y,e,a),a.add(this.wingmanShip),window.gameEngine&&window.gameEngine.hangarManager&&this.wingmanShip.setHangarManager(window.gameEngine.hangarManager),!0}catch(s){return console.error("Failed to create wingman ship:",s),!1}}removeEffect(e){return this.wingmanShip&&(this.wingmanShip.destroy(),this.wingmanShip=null),!0}canPurchase(e,t){return{canPurchase:t>=this.cost&&!this.isActive,reason:t<this.cost?"insufficient_tokens":this.isActive?"already_active":"available"}}}class De extends A{constructor(){super("REALITY_WARP",E.POWER_UP_COSTS.REALITY_WARP,null,"Transform the next level with your imagination and reshape the battlefield"),this.icon="🌌",this.color="#6366f1",this.glowColor="#818cf8"}async apply(e){return this.isActive?(console.warn("RealityWarp is already active"),!1):(this.isActive=!0,this.appliedAt=Date.now(),this.timeRemaining=null,e.realityWarpActive===void 0&&(e.realityWarpActive=!1),e.realityWarpActive=!0,!0)}remove(e){return this.isActive?(this.isActive=!1,this.timeRemaining=0,e.realityWarpActive===!0&&(e.realityWarpActive=!1),!0):(console.warn("RealityWarp is not active"),!1)}canPurchase(e,t){return{canPurchase:t>=this.cost&&!this.isActive,reason:t<this.cost?"insufficient_tokens":this.isActive?"already_active":"available"}}}let V=class{static createPowerUp(e){switch(e){case"EXTRA_LIFE":return new Re;case"SPREAD_AMMO":return new ke;case"EXTRA_WINGMAN":return new Ie;case"REALITY_WARP":return new De;case"RAPID_FIRE":return new _e;case"SHIELD":return new xe;case"RICOCHET_ROUNDS":return new Be;case"KINETIC_BOOST":return new We;case"LASER_ROUNDS":return new Fe;case"FLAME_ROUNDS":return new Oe;case"ICE_ROUNDS":return new Le;case"PLASMA_ROUNDS":return new Ue;case"SONIC_ROUNDS":return new He;default:throw new Error(`Unknown power-up type: ${e}`)}}static getAllPowerUpTypes(){return["EXTRA_LIFE","SPREAD_AMMO","EXTRA_WINGMAN","REALITY_WARP","KINETIC_BOOST","LASER_ROUNDS","FLAME_ROUNDS","ICE_ROUNDS","PLASMA_ROUNDS","SONIC_ROUNDS"]}static getWeaponVariantTypes(){return["KINETIC_BOOST","LASER_ROUNDS","FLAME_ROUNDS","ICE_ROUNDS","PLASMA_ROUNDS","SONIC_ROUNDS"]}static createAllPowerUps(){return this.getAllPowerUpTypes().map(e=>this.createPowerUp(e))}static createAllWeaponVariants(){return this.getWeaponVariantTypes().map(e=>this.createPowerUp(e))}};class _e extends A{constructor(){super("RAPID_FIRE",0,3e4,"Double fire rate for increased damage output"),this.icon="⚡",this.color="#ffff00",this.glowColor="#ffaa00",this.originalFireRate=null}applyEffect(e){if(!e.weaponSystem)return!1;e.activePowerUps||(e.activePowerUps=new Map),e.weaponSystem.baseFireRate||(e.weaponSystem.baseFireRate=e.weaponSystem.fireRate);const t=Array.from(e.activePowerUps.values()).filter(n=>n.type==="RAPID_FIRE"&&n.isActive).length;if(t>=2)return console.log(`RapidFire cannot be applied - player already has ${t} active rapid-fire power-ups (limit: 2)`),!1;this.originalFireRate=e.weaponSystem.baseFireRate;const s=t+1,a=Math.pow(2,s),i=e.weaponSystem.baseFireRate/a;return e.weaponSystem.fireRate=i,!0}removeEffect(e){if(!e.weaponSystem||this.originalFireRate===null)return!1;const t=Array.from(e.activePowerUps.values()).filter(s=>s.type==="RAPID_FIRE"&&s.isActive&&s!==this).length;if(t>0){const s=Math.pow(2,t);e.weaponSystem.fireRate=e.weaponSystem.baseFireRate/s,console.log(`RapidFire removed: ${t} remaining, newFireRate=${e.weaponSystem.fireRate}ms`)}else e.weaponSystem.fireRate=e.weaponSystem.baseFireRate,console.log(`RapidFire removed: restored baseFireRate=${e.weaponSystem.baseFireRate}ms`);return this.originalFireRate=null,!0}canPurchase(e,t){return{canPurchase:!1,reason:"drop_only"}}}class xe extends A{constructor(){super("SHIELD",0,3e4,"Shield that reflects enemy fire back at enemies"),this.icon="🛡️",this.color="#00ffff",this.glowColor="#88ffff",this.shieldActive=!1}applyEffect(e){if(!e.weaponSystem)return!1;e.activePowerUps||(e.activePowerUps=new Map);const t=Array.from(e.activePowerUps.values()).filter(a=>a.type==="SHIELD"&&a.isActive),s=t.length;if(s>=2)return console.log(`Shield cannot be applied - player already has ${s} active shield power-ups (limit: 2)`),!1;if(s>0){const a=t[0];return a.timeRemaining=3e4,console.log("Shield power-up collected: resetting existing shield timer to 30 seconds"),!0}return this.shieldActive=!0,e.hasShield=!0,console.log("Shield power-up applied: player now has reflective shield"),!0}removeEffect(e){return this.shieldActive=!1,e.hasShield=!1,console.log("Shield power-up removed: player no longer has reflective shield"),!0}canPurchase(e,t){return{canPurchase:!1,reason:"drop_only"}}}class Be extends A{constructor(){super("RICOCHET_ROUNDS",0,null,"Rare ricochet rounds that bounce off enemies and walls"),this.icon="⚡",this.color="#ff00ff",this.glowColor="#ff88ff",this.ricochetCount=3}applyEffect(e){return e.weaponSystem&&e.weaponSystem?(e.weaponSystem.enableRicochetRounds(!0,this.ricochetCount),console.log(`Ricochet Rounds applied: projectiles can ricochet ${this.ricochetCount} times`),!0):!1}removeEffect(e){return e.weaponSystem?(e.weaponSystem.enableRicochetRounds(!1,0),console.log("Ricochet Rounds removed: projectiles no longer ricochet"),!0):!1}canPurchase(e,t){return{canPurchase:!1,reason:"rare_bonus_only"}}}class We extends A{constructor(){super("KINETIC_BOOST",5e3,null,"High-velocity rounds that shatter crystal enemies and pierce earth defenses"),this.icon="💎",this.color="#FF69B4",this.glowColor="#FF1493",this.weaponVariant="kinetic"}applyEffect(e){return e.weaponSystem?(e.weaponSystem.setWeaponVariant(this.weaponVariant),!0):!1}canPurchase(e,t){var a;const s=(a=e.weaponSystem)==null?void 0:a.currentVariant;return{canPurchase:t>=this.cost&&s!==this.weaponVariant,reason:t<this.cost?"insufficient_tokens":s===this.weaponVariant?"already_equipped":"available"}}}class Fe extends A{constructor(){super("LASER_ROUNDS",5e3,null,"Focused energy beams that pierce through shadow enemies and track air targets"),this.icon="🔴",this.color="#FF0000",this.glowColor="#FF4444",this.weaponVariant="laser"}applyEffect(e){return e.weaponSystem?(e.weaponSystem.setWeaponVariant(this.weaponVariant),!0):!1}canPurchase(e,t){var a;const s=(a=e.weaponSystem)==null?void 0:a.currentVariant;return{canPurchase:t>=this.cost&&s!==this.weaponVariant,reason:t<this.cost?"insufficient_tokens":s===this.weaponVariant?"already_equipped":"available"}}}class Oe extends A{constructor(){super("FLAME_ROUNDS",5e3,null,"Incendiary ammunition that burns through water enemies and melts crystal defenses"),this.icon="🔥",this.color="#FF4500",this.glowColor="#FFD700",this.weaponVariant="flame"}applyEffect(e){return e.weaponSystem?(e.weaponSystem.setWeaponVariant(this.weaponVariant),!0):!1}canPurchase(e,t){var a;const s=(a=e.weaponSystem)==null?void 0:a.currentVariant;return{canPurchase:t>=this.cost&&s!==this.weaponVariant,reason:t<this.cost?"insufficient_tokens":s===this.weaponVariant?"already_equipped":"available"}}}class Le extends A{constructor(){super("ICE_ROUNDS",5e3,null,"Freezing projectiles that extinguish fire enemies and shatter earth defenses"),this.icon="❄️",this.color="#00BFFF",this.glowColor="#87CEEB",this.weaponVariant="ice"}applyEffect(e){return e.weaponSystem?(e.weaponSystem.setWeaponVariant(this.weaponVariant),!0):!1}canPurchase(e,t){var a;const s=(a=e.weaponSystem)==null?void 0:a.currentVariant;return{canPurchase:t>=this.cost&&s!==this.weaponVariant,reason:t<this.cost?"insufficient_tokens":s===this.weaponVariant?"already_equipped":"available"}}}class Ue extends A{constructor(){super("PLASMA_ROUNDS",7500,null,"Supercharged plasma that disrupts air and shadow enemies with electrical energy"),this.icon="⚡",this.color="#9370DB",this.glowColor="#DDA0DD",this.weaponVariant="plasma"}applyEffect(e){return e.weaponSystem?(e.weaponSystem.setWeaponVariant(this.weaponVariant),!0):!1}canPurchase(e,t){var a;const s=(a=e.weaponSystem)==null?void 0:a.currentVariant;return{canPurchase:t>=this.cost&&s!==this.weaponVariant,reason:t<this.cost?"insufficient_tokens":s===this.weaponVariant?"already_equipped":"available"}}}class He extends A{constructor(){super("SONIC_ROUNDS",7500,null,"Sonic vibrations that shatter crystal defenses and disrupt earth enemies"),this.icon="🔊",this.color="#32CD32",this.glowColor="#00FF00",this.weaponVariant="sonic"}applyEffect(e){return e.weaponSystem?(e.weaponSystem.setWeaponVariant(this.weaponVariant),!0):!1}canPurchase(e,t){var a;const s=(a=e.weaponSystem)==null?void 0:a.currentVariant;return{canPurchase:t>=this.cost&&s!==this.weaponVariant,reason:t<this.cost?"insufficient_tokens":s===this.weaponVariant?"already_equipped":"available"}}}class $e extends G{constructor(e,t,s){super(e,t),this.powerUpType=s,this.powerUp=V.createPowerUp(s),this.collected=!1,this.addTag("powerup-collectible"),this.radius=15,this.collisionRadius=15,this.color=this.powerUp.color,this.glowColor=this.powerUp.glowColor,this.pulseAmount=0,this.pulseDirection=1,this.pulseSpeed=.05,this.velocity=new m(0,.5),this.lifetime=1e4,this.timeAlive=0,this.downwardGravity=50,this.maxDownwardSpeed=150,this.gravitationSpeed=100,this.gravitationDistance=200,this.bloomIntensity=.8,this.bloomRadius=25,this.bloomPulseSpeed=.03,this.bloomPulseAmount=0,this.updateCollisionBounds()}update(e){this.collected||(super.update(e),this.pulseAmount+=this.pulseSpeed*this.pulseDirection*(e/16),this.pulseAmount>=1?(this.pulseAmount=1,this.pulseDirection=-1):this.pulseAmount<=0&&(this.pulseAmount=0,this.pulseDirection=1),this.bloomPulseAmount+=this.bloomPulseSpeed*(e/16),this.bloomPulseAmount>=1?(this.bloomPulseAmount=1,this.bloomPulseSpeed=-this.bloomPulseSpeed):this.bloomPulseAmount<=0&&(this.bloomPulseAmount=0,this.bloomPulseSpeed=Math.abs(this.bloomPulseSpeed)),this.applyDownwardGravity(e),this.gravitateTowardPlayer(e),this.timeAlive+=e,this.timeAlive>=this.lifetime&&this.destroy(),this.checkBounds())}applyDownwardGravity(e){this.velocity.y+=this.downwardGravity*e/1e3,this.velocity.y>this.maxDownwardSpeed&&(this.velocity.y=this.maxDownwardSpeed)}gravitateTowardPlayer(e){var n;const t=(n=window.gameEngine)==null?void 0:n.gameObjectManager;if(!t)return;const s=t.findByTag("player");if(s.length===0)return;const a=s[0],i=this.position.distanceTo(a.position);if(i<this.gravitationDistance){const r=a.position.subtract(this.position).normalize(),o=1-i/this.gravitationDistance,c=this.gravitationSpeed*o,h=r.multiply(c*e/1e3);this.velocity.addInPlace(h)}}checkBounds(){var t;const e=(t=window.gameEngine)==null?void 0:t.canvas;e&&this.position.y>e.height+50&&this.destroy()}render(e,t=0){if(this.collected)return;const s=this.position.add(this.velocity.multiply(t/1e3));e.save(),e.translate(s.x,s.y);const a=1+this.bloomPulseAmount*.4,i=this.bloomRadius*a,n=i*1.5,r=e.createRadialGradient(0,0,0,0,0,n);r.addColorStop(0,this.glowColor+"20"),r.addColorStop(.5,this.color+"10"),r.addColorStop(1,"transparent"),e.fillStyle=r,e.beginPath(),e.arc(0,0,n,0,Math.PI*2),e.fill();const o=i*1.2,c=e.createRadialGradient(0,0,0,0,0,o);c.addColorStop(0,this.glowColor+"40"),c.addColorStop(.7,this.color+"20"),c.addColorStop(1,"transparent"),e.fillStyle=c,e.beginPath(),e.arc(0,0,o,0,Math.PI*2),e.fill();const h=i,d=e.createRadialGradient(0,0,0,0,0,h);d.addColorStop(0,this.glowColor),d.addColorStop(.5,this.color+"80"),d.addColorStop(1,this.color+"20"),e.fillStyle=d,e.beginPath(),e.arc(0,0,h,0,Math.PI*2),e.fill(),e.fillStyle=this.color,e.beginPath(),e.arc(0,0,this.radius,0,Math.PI*2),e.fill(),e.shadowColor=this.glowColor,e.shadowBlur=10*a,e.font="bold 20px Arial",e.fillStyle="white",e.textAlign="center",e.textBaseline="middle",e.fillText(this.powerUp.icon,0,0),e.shadowBlur=0,e.restore()}collect(){return this.collected?null:(this.collected=!0,this.destroy(),this.powerUp)}reset(){super.reset(),this.collected=!1,this.timeAlive=0,this.pulseAmount=0,this.pulseDirection=1,this.bloomPulseAmount=0,this.bloomPulseSpeed=.03,this.velocity=new m(0,.5)}}class Ne{constructor(e,t,s=10){this.createFn=e,this.resetFn=t,this.pool=[],this.active=[];for(let a=0;a<s;a++)this.pool.push(this.createFn())}get(){let e;return this.pool.length>0?e=this.pool.pop():e=this.createFn(),this.active.push(e),e}release(e){const t=this.active.indexOf(e);t!==-1&&(this.active.splice(t,1),this.resetFn(e),this.pool.push(e))}releaseAll(){for(;this.active.length>0;){const e=this.active.pop();this.resetFn(e),this.pool.push(e)}}getStats(){return{pooled:this.pool.length,active:this.active.length,total:this.pool.length+this.active.length}}}class Ge{constructor(){this.objects=[],this.objectsToAdd=[],this.objectsToRemove=[],this.pools=new Map}add(e){this.objectsToAdd.push(e)}remove(e){this.objectsToRemove.push(e)}createPool(e,t,s,a=10){this.pools.set(e,new Ne(t,s,a))}getFromPool(e){const t=this.pools.get(e);if(t)return t.get();throw new Error(`Pool for type '${e}' not found`)}returnToPool(e,t){const s=this.pools.get(e);s&&(s.release(t),this.remove(t))}update(e){this.processAdditions(),this.processRemovals();for(let t=this.objects.length-1;t>=0;t--){const s=this.objects[t];if(s.destroyed){this.objectsToRemove.push(s);continue}s.active&&s.update(e)}}render(e,t=0){const s=this.sortObjectsByDepth();for(const a of s)for(const i of a)i.visible&&!i.destroyed&&i.render(e,t)}sortObjectsByDepth(){const e={background:[],playerSprites:[],playerProjectiles:[],items:[],explosions:[],enemyProjectiles:[]};for(const t of this.objects)!t.visible||t.destroyed||(t.hasTag("player")||t.hasTag("wingman")?e.playerSprites.push(t):t.hasTag("projectile")?t.type==="player"||t.type==="wingman"?e.playerProjectiles.push(t):e.enemyProjectiles.push(t):t.hasTag("powerup")||t.hasTag("collectible")?e.items.push(t):t.hasTag("explosion")||t.hasTag("effect")?e.explosions.push(t):e.background.push(t));return this.sortProjectilesBySize(e.playerProjectiles),this.sortProjectilesBySize(e.enemyProjectiles),[e.background,e.playerSprites,e.playerProjectiles,e.items,e.explosions,e.enemyProjectiles]}sortProjectilesBySize(e){e.sort((t,s)=>{const a=(t.width||4)*(t.height||12);return(s.width||4)*(s.height||12)-a})}processAdditions(){this.objectsToAdd.length>0&&(this.objects.push(...this.objectsToAdd),this.objectsToAdd.length=0)}processRemovals(){if(this.objectsToRemove.length>0){for(const e of this.objectsToRemove){const t=this.objects.indexOf(e);t!==-1&&this.objects.splice(t,1)}this.objectsToRemove.length=0}}findByTag(e){const t=this.objects.filter(a=>a.hasTag(e)),s=this.objectsToAdd.filter(a=>a.hasTag(e));return[...t,...s]}findById(e){return this.objects.find(t=>t.id===e)}getActive(){return this.objects.filter(e=>e.active&&!e.destroyed)}getVisible(){return this.objects.filter(e=>e.visible&&!e.destroyed)}checkCollisions(e,t,s){const a=this.findByTag(e),i=this.findByTag(t);for(const n of a)if(n.active)for(const r of i)r.active&&n.collidesWith(r)&&s(n,r)}checkCollisionsOptimized(e,t,s,a=64){const i=this.findByTag(e),n=this.findByTag(t),r=new Map;for(const o of n){if(!o.active)continue;const c=Math.floor(o.position.x/a),h=Math.floor(o.position.y/a),d=`${c},${h}`;r.has(d)||r.set(d,[]),r.get(d).push(o)}for(const o of i){if(!o.active)continue;const c=Math.floor(o.position.x/a),h=Math.floor(o.position.y/a);for(let d=-1;d<=1;d++)for(let g=-1;g<=1;g++){const w=`${c+d},${h+g}`,v=r.get(w);if(v)for(const p of v)o.collidesWith(p)&&s(o,p)}}}clear(){this.objects.length=0,this.objectsToAdd.length=0,this.objectsToRemove.length=0;for(const e of this.pools.values())e.releaseAll()}clearByTag(e){const t=Array.isArray(e)?e:[e],s=[];for(const a of this.objects)for(const i of t)if(a.hasTag(i)){s.push(a);break}for(const a of s)this.remove(a);return s.length}clearLevelObjects(){const e=["projectile","enemy","powerup","collectible","powerup-collectible","effect","explosion"];return this.clearByTag(e)}getStats(){const e={};for(const[t,s]of this.pools.entries())e[t]=s.getStats();return{totalObjects:this.objects.length,activeObjects:this.getActive().length,visibleObjects:this.getVisible().length,pendingAdditions:this.objectsToAdd.length,pendingRemovals:this.objectsToRemove.length,pools:e}}}class je{constructor(){this._baseUrl=null}getApiBaseUrl(){if(this._baseUrl)return this._baseUrl;const{protocol:e,hostname:t}=window.location;return t==="warpsector.merchgenieai.com"?this._baseUrl=`${e}//warpsector.merchgenieai.com`:this._baseUrl=`${e}//${t}:3001`,console.log(`URL Config: Using base URL ${this._baseUrl} for hostname ${t}`),this._baseUrl}getApiUrl(e){const t=this.getApiBaseUrl(),s=e.startsWith("/")?e:`/${e}`;return`${t}${s}`}getImageUrl(e){return e?e.startsWith("http://")||e.startsWith("https://")?e:e.startsWith("/api/")?`${this.getApiBaseUrl()}${e}`:e:""}reset(){this._baseUrl=null}getConfigInfo(){const{protocol:e,hostname:t,port:s}=window.location;return{currentHostname:t,currentProtocol:e,currentPort:s,apiBaseUrl:this.getApiBaseUrl(),isProduction:t==="warpsector.merchgenieai.com",isLocalDevelopment:t!=="warpsector.merchgenieai.com"}}}const D=new je;class Ve{constructor(e=null,t=null){this.gameObjectManager=e,this.audioManager=t,this.currentLevel=1,this.levelInProgress=!1,this.levelStartTime=0,this.levelCompletionTime=0,this.levelConfig=null,this.maxLevels=50,this.currentScore=0,this.levelScore=0,this.enemiesDefeated=0,this.levelEnemiesDefeated=0,this.totalScore=0,this.levelStartScore=0,this.perfectCompletion=!0,this.speedBonus=0,this.accuracyBonus=0,this.enemyDefeatQueue=[],this.requiredEnemiesDefeated=0,this.wavesCompleted=0,this.requiredWaves=0,this.onLevelStartCallback=null,this.onLevelCompleteCallback=null,this.onScoreUpdateCallback=null}startLevel(e=null){if(e!==null&&(this.currentLevel=e),this.levelConfig=this.generateLevelConfig(this.currentLevel),this.levelInProgress=!0,this.levelStartTime=performance.now(),this.levelCompletionTime=0,this.levelScore=0,this.levelEnemiesDefeated=0,this.levelStartScore=this.currentScore,this.perfectCompletion=!0,this.speedBonus=0,this.accuracyBonus=0,this.requiredEnemiesDefeated=this.levelConfig.totalEnemies,this.requiredWaves=this.levelConfig.totalWaves,this.wavesCompleted=0,window.gameEngine&&window.gameEngine.inputManager&&window.gameEngine.inputManager.resetInputState(),this.gameObjectManager){const t=this.gameObjectManager.clearLevelObjects();console.log(`LevelManager: Cleared ${t} objects for level transition`)}if(window.gameEngine&&window.gameEngine.playerShip){const t=window.gameEngine.canvas.width/2,s=window.gameEngine.canvas.height-100;window.gameEngine.playerShip.resetToPosition(t,s),console.log(`LevelManager: Reset player position to (${t}, ${s}) for level ${this.currentLevel}`)}if(window.gameEngine&&window.gameEngine.realityWarpManager){const s=window.gameEngine.realityWarpManager.getAndClearPendingEnvironment();if(s){if(this.levelConfig.environment=s.type||"custom",this.levelConfig.environmentData={type:s.type||"custom",name:s.name||"Custom Environment",description:s.description||"Custom environment from Reality Warp",imagePrompt:s.imagePrompt||"",gameplayModifiers:s.gameplayModifiers||{},imageFileName:s.imageFileName||null,imageUrl:s.imageUrl||null,imageData:s.imageData||{images:[]}},!this.levelConfig.environmentData.imageUrl&&this.levelConfig.environmentData.imageData&&this.levelConfig.environmentData.imageData.images&&this.levelConfig.environmentData.imageData.images[0]){const a=this.levelConfig.environmentData.imageData.images[0],i=a.localUrl||a.url;this.levelConfig.environmentData.imageUrl=D.getImageUrl(i)}console.log("Applied custom environment to level:",this.levelConfig.environmentData.name,"with imageUrl:",this.levelConfig.environmentData.imageUrl)}}return this.onLevelStartCallback&&this.onLevelStartCallback(this.currentLevel,this.levelConfig),this.levelConfig}generateLevelConfig(e){const t=Math.min(3,1+(e-1)*.1),s=3,a=Math.floor(e/2),i=Math.min(10,s+a);let n=0;for(let p=1;p<=i;p++){const M=5+Math.floor(p/2);n+=M}n=Math.floor(n*t);const r=this.selectLevelEnvironment(e),o=e%10===0,c=e%5===0,h=120,d=Math.min(60,e*2),g=h+d,v=Math.floor(1e3*t*e);return{levelNumber:e,difficulty:Math.min(10,Math.floor(e/5)+1),totalEnemies:n,totalWaves:i,environment:r,hasBoss:o,hasSpecialMechanics:c,timeLimit:g,scoreTarget:v,completionReward:this.calculateBaseLevelReward(e),difficultyMultiplier:t,enemyDistribution:this.generateEnemyDistribution(e,r),conditions:this.generateLevelConditions(e),backgroundMusic:this.selectBackgroundMusic(e,r),visualEffects:this.selectVisualEffects(e,r)}}selectLevelEnvironment(e){if(e%10===0){const t=[P.FIRE,P.CRYSTAL,P.SHADOW];return t[Math.floor((e/10-1)%t.length)]}return P.SPACE}generateEnemyDistribution(e,t){const s={};return e<=3?(s.basic=.7,s.advanced=.3,s.elite=0):e<=10?(s.basic=.5,s.advanced=.4,s.elite=.1):(s.basic=.3,s.advanced=.5,s.elite=.2),s}generateLevelConditions(e){const t={primary:"defeat_all_enemies",secondary:[],bonus:[]};return e>=3&&t.secondary.push("complete_under_time_limit"),e>=5&&t.secondary.push("maintain_accuracy_above_70"),e>=7&&t.secondary.push("take_minimal_damage"),t.bonus.push("perfect_accuracy"),t.bonus.push("speed_completion"),t.bonus.push("no_damage_taken"),t}selectBackgroundMusic(e,t){return t===P.SPACE?"space_theme":"water_theme"}playBackgroundMusic(e,t){if(!this.audioManager)return;const s=this.selectBackgroundMusic(e,t);this.audioManager.playMusic(s,!0)}stopBackgroundMusic(){this.audioManager&&this.audioManager.stopAllMusic()}selectVisualEffects(e,t){const s=[];switch(t){case P.SPACE:s.push("star_field","nebula_clouds");break;case P.WATER:s.push("water_bubbles","light_rays","current_effects");break;case P.FIRE:s.push("lava_particles","heat_distortion","ember_effects");break;case P.AIR:s.push("wind_particles","cloud_effects","lightning_streaks");break;case P.EARTH:s.push("rock_particles","dust_clouds","terrain_effects");break;case P.CRYSTAL:s.push("crystal_reflections","prismatic_effects","energy_auras");break;case P.SHADOW:s.push("shadow_tendrils","dark_mist","void_effects");break;default:s.push("star_field","nebula_clouds")}return e>=10&&s.push("intensity_overlay"),s}update(e,t={}){this.levelInProgress&&(this.levelCompletionTime=performance.now()-this.levelStartTime,this.checkLevelCompletion(t),this.updatePerformanceMetrics(t))}checkLevelCompletion(e){if(!this.levelInProgress||!this.levelConfig)return;const t=this.levelEnemiesDefeated>=this.requiredEnemiesDefeated,s=this.wavesCompleted>=this.requiredWaves;t&&s&&this.completeLevel(),(e.playerDestroyed||!1)&&this.failLevel("player_destroyed")}calculateQueuedScore(){let e=0;for(const t of this.enemyDefeatQueue)e+=t;return this.enemyDefeatQueue=[],e}completeLevel(){if(!this.levelInProgress)return;this.levelInProgress=!1;const e=this.levelCompletionTime/1e3,t=this.calculateQueuedScore();this.levelScore=t,this.currentScore+=t,this.totalScore=this.currentScore;const s=this.calculateLevelScore(e),a=this.checkRareBonusRewards(),i={levelNumber:this.currentLevel,completed:!0,completionTime:e,score:s.totalScore,enemiesDefeated:this.levelEnemiesDefeated,totalEnemies:this.levelConfig.totalEnemies,perfectCompletion:this.perfectCompletion,bonuses:s.bonuses,nextLevel:this.currentLevel+1,rareBonus:a};if(this.onLevelCompleteCallback&&this.onLevelCompleteCallback(i),a&&window.gameEngine&&this.awardRareBonus(a).catch(n=>{console.error("Error awarding rare bonus:",n)}),window.gameEngine&&window.gameEngine.realityWarpManager){const n=window.gameEngine.realityWarpManager;n.getWarpState().status==="active"&&n.endWarp()}return i}checkRareBonusRewards(){const e={10:{type:"RICOCHET_ROUNDS",name:"Ricochet Rounds",description:"Projectiles bounce off enemies and walls!"},20:{type:"MEGA_SHIELD",name:"Mega Shield",description:"Extended shield duration with damage reflection!"},30:{type:"SUPER_RAPID_FIRE",name:"Super Rapid Fire",description:"Extreme fire rate for maximum devastation!"},40:{type:"DOUBLE_WINGMAN",name:"Double Wingman",description:"Deploy two wingman ships for double the firepower!"},50:{type:"ULTIMATE_SPREAD",name:"Ultimate Spread",description:"Massive spread pattern coverage!"}};return e[this.currentLevel]?{level:this.currentLevel,...e[this.currentLevel]}:null}async awardRareBonus(e){if(!window.gameEngine||!window.gameEngine.gameObjectManager)return;const s=window.gameEngine.gameObjectManager.findByTag("player");if(s.length===0)return;const a=s[0];try{const i=PowerUpFactory.createPowerUp(e.type);i&&(await i.apply(a),a.activePowerUps||(a.activePowerUps=new Map),a.activePowerUps.set(i.id,i),console.log(`Rare bonus awarded: ${e.name} at level ${e.level}`),window.gameEngine.uiManager&&window.gameEngine.uiManager.showNotification(`Rare Bonus Unlocked: ${e.name}!`,e.description,5e3))}catch(i){console.error("Failed to award rare bonus:",i)}}failLevel(e){if(!this.levelInProgress)return;this.levelInProgress=!1;const t=this.levelCompletionTime/1e3,s={levelNumber:this.currentLevel,completed:!1,reason:e,completionTime:t,score:this.levelScore,enemiesDefeated:this.levelEnemiesDefeated,canRetry:!0};if(this.onLevelCompleteCallback&&this.onLevelCompleteCallback(s),window.gameEngine&&window.gameEngine.realityWarpManager){const a=window.gameEngine.realityWarpManager;a.getWarpState().status==="active"&&a.endWarp()}return s}calculateLevelScore(e){const t=this.levelConfig,s=this.levelScore,a=this.calculateTimeBonus(e,t.timeLimit),i=this.calculateAccuracyBonus(),n=this.perfectCompletion?Math.floor(s*.5):0,r=t.completionReward,o=t.difficultyMultiplier,c=s+a+i+n+r,h=Math.floor(c*o);return{enemyScore:s,timeBonus:a,accuracyBonus:i,perfectBonus:n,completionBonus:r,difficultyMultiplier:o,totalScore:h,bonuses:{speed:a>0,accuracy:i>0,perfect:n>0}}}calculateTimeBonus(e,t){const s=t*.6;if(e<=s){const i=e/s;return Math.floor(500*(2-i))}else{if(e<=t*.8)return 200;if(e<=t)return 50}return 0}calculateAccuracyBonus(){return this.accuracyBonus}updatePerformanceMetrics(e){if(e.playerDamageTaken&&(this.perfectCompletion=!1),e.shotsFired&&e.shotsHit){const t=e.shotsHit/e.shotsFired;t>=.9?this.accuracyBonus=300:t>=.7?this.accuracyBonus=150:t>=.5&&(this.accuracyBonus=50)}}recordEnemyDefeat(e,t){this.enemiesDefeated++,this.levelEnemiesDefeated++,this.enemyDefeatQueue.push(t),this.onScoreUpdateCallback&&this.onScoreUpdateCallback({enemiesDefeated:this.enemiesDefeated,levelEnemiesDefeated:this.levelEnemiesDefeated,currentScore:this.currentScore,levelScore:this.levelScore,scoreGained:0})}recordWaveCompletion(e,t=0){this.wavesCompleted++,t>0&&(this.levelScore+=t,this.currentScore+=t),this.onScoreUpdateCallback&&this.onScoreUpdateCallback({wavesCompleted:this.wavesCompleted,currentScore:this.currentScore,levelScore:this.levelScore,scoreGained:t})}calculateBaseLevelReward(e){const t=E.BASE_LEVEL_REWARD,s=Math.floor(e/5)+1;return t*s}calculateProbabilisticReward(e,t=[],s=1){const a=E.REWARD_SYSTEM,i=this.calculateBaseLevelReward(e);let n=a.BASE_VARIANCE;s>=1?n=a.PERFECT_SCORE_VARIANCE:s<.5&&(n=a.POOR_SCORE_VARIANCE);const r=Math.min(t.length*a.POWER_UP_VARIANCE_REDUCTION,a.MAX_VARIANCE_REDUCTION),o=Math.max(.1,n-r),c=Math.max(i*a.MIN_REWARD_RATIO,i*(1-o));return{maxReward:i,minReward:c,maxPossibleReward:i,variance:o,activePowerUps:t.length,completionPercentage:s,expectedValue:i*(1-a.HOUSE_EDGE),maxRewardChance:a.MAX_REWARD_CHANCE,aboveAverageChance:a.ABOVE_AVERAGE_CHANCE,averageChance:a.AVERAGE_CHANCE,belowAverageChance:a.BELOW_AVERAGE_CHANCE,minRewardChance:a.MIN_REWARD_CHANCE}}rollForReward(e){const t=E.REWARD_SYSTEM,s=Math.random();if(s<t.MIN_REWARD_CHANCE)return Math.floor(e.minReward);if(s<t.MIN_REWARD_CHANCE+t.BELOW_AVERAGE_CHANCE){const a=e.maxReward*.5-e.minReward;return Math.floor(e.minReward+Math.random()*a)}else if(s<t.MIN_REWARD_CHANCE+t.BELOW_AVERAGE_CHANCE+t.AVERAGE_CHANCE){const a=e.maxReward*.5,i=e.maxReward*.75;return Math.floor(a+Math.random()*(i-a))}else if(s<t.MIN_REWARD_CHANCE+t.BELOW_AVERAGE_CHANCE+t.AVERAGE_CHANCE+t.ABOVE_AVERAGE_CHANCE){const a=e.maxReward*.75,i=e.maxReward*.99;return Math.floor(a+Math.random()*(i-a))}else return Math.floor(e.maxReward)}getLevelStatus(){return{currentLevel:this.currentLevel,levelInProgress:this.levelInProgress,levelConfig:this.levelConfig,completionTime:this.levelCompletionTime,score:{current:this.currentScore,level:this.levelScore,total:this.totalScore},progress:{enemiesDefeated:this.levelEnemiesDefeated,requiredEnemies:this.requiredEnemiesDefeated,wavesCompleted:this.wavesCompleted,requiredWaves:this.requiredWaves},performance:{perfectCompletion:this.perfectCompletion,speedBonus:this.speedBonus,accuracyBonus:this.accuracyBonus}}}reset(){this.currentLevel=1,this.levelInProgress=!1,this.levelStartTime=0,this.levelCompletionTime=0,this.currentScore=0,this.levelScore=0,this.enemiesDefeated=0,this.levelEnemiesDefeated=0,this.totalScore=0,this.perfectCompletion=!0,this.speedBonus=0,this.accuracyBonus=0,this.wavesCompleted=0,this.enemyDefeatQueue=[]}setOnLevelStart(e){this.onLevelStartCallback=e}setOnLevelComplete(e){this.onLevelCompleteCallback=e}setOnScoreUpdate(e){this.onScoreUpdateCallback=e}setEnvironmentData(e){this.levelConfig?this.levelConfig.environmentData=e:console.warn("Cannot set environment data: no active level configuration")}}class C{static clamp(e,t,s){return Math.min(Math.max(e,t),s)}static lerp(e,t,s){return e+(t-e)*s}static pointInRect(e,t){return e.x>=t.x&&e.x<=t.x+t.width&&e.y>=t.y&&e.y<=t.y+t.height}static rectCollision(e,t){return e.x<t.x+t.width&&e.x+e.width>t.x&&e.y<t.y+t.height&&e.y+e.height>t.y}static circleCollision(e,t){const s=e.x-t.x,a=e.y-t.y;return Math.sqrt(s*s+a*a)<e.radius+t.radius}static random(e,t){return Math.random()*(t-e)+e}static randomInt(e,t){return Math.floor(Math.random()*(t-e+1))+e}static degToRad(e){return e*(Math.PI/180)}static radToDeg(e){return e*(180/Math.PI)}static circleRectCollision(e,t){const s=C.clamp(e.x,t.x,t.x+t.width),a=C.clamp(e.y,t.y,t.y+t.height),i=e.x-s,n=e.y-a;return Math.sqrt(i*i+n*n)<e.radius}static lineCircleCollision(e,t,s){const a=t.x-e.x,i=t.y-e.y,n=e.x-s.x,r=e.y-s.y,o=a*a+i*i,c=2*(n*a+r*i),h=n*n+r*r-s.radius*s.radius,d=c*c-4*o*h;if(d<0)return!1;const g=Math.sqrt(d),w=(-c-g)/(2*o),v=(-c+g)/(2*o);return w>=0&&w<=1||v>=0&&v<=1}static smoothStep(e,t,s){const a=C.clamp((s-e)/(t-e),0,1);return a*a*(3-2*a)}static easeInQuad(e){return e*e}static easeOutQuad(e){return e*(2-e)}static easeInOutQuad(e){return e<.5?2*e*e:-1+(4-2*e)*e}static wrapAngle(e){for(;e>Math.PI;)e-=2*Math.PI;for(;e<-Math.PI;)e+=2*Math.PI;return e}static angleDifference(e,t){let s=t-e;return C.wrapAngle(s)}static moveTowards(e,t,s){const a=t-e;return Math.abs(a)<=s?t:e+Math.sign(a)*s}static approximately(e,t,s=1e-4){return Math.abs(e-t)<s}static map(e,t,s,a,i){return(e-t)*(i-a)/(s-t)+a}}function H(u){return u<=5?1:u<=10?2:u<=15?3:u<=20?4:5}function z(u){switch(H(u)){case 1:return 1+(u-1)*.2;case 2:return 2+(u-6)*.32;case 3:return 4+(u-11)*.64;case 4:return 8+(u-16)*1.28;case 5:return 16+(u-21)*1.6;default:return 1}}function Z(u,e){const t=z(e),s=H(e);return{maxHealth:Math.round(u.maxHealth*t),speed:u.speed*t,damage:Math.round(u.damage*t),fireRate:u.fireRate/Math.sqrt(t),scoreValue:Math.round(u.scoreValue*t),tier:s,coefficient:t}}function ee(u){const e=H(u);let t=0;switch(e){case 1:t=0;break;case 2:t=.05;break;case 3:t=.1;break;case 4:t=.15;break;case 5:t=.25;break;default:t=0}return Math.random()<t}function te(u,e=!1){const t=["basic_movement","single_shot"];return u>=2&&t.push("spread_shot","leading_shots"),u>=3&&t.push("homing_projectiles","shield_allies"),u>=4&&t.push("complex_patterns","evasion_dash","formation_flying"),u>=5&&t.push("multi_phase_attack","teleport","summon_allies"),e&&(t.includes("homing_projectiles")||t.push("homing_projectiles"),t.includes("evasion_dash")||t.push("evasion_dash"),t.push("unpredictable_movement","enhanced_stats")),t}function se(u,e){if(e==="fire"&&u>=2)return"burst";if(e==="crystal"&&u>=3)return"spiral";if(e==="water"&&u>=4)return"shotgun";switch(u){case 1:return"single";case 2:return"spread";case 3:return"homing";case 4:return"complex";case 5:return"elite";default:return"single"}}function ae(u,e){if(e==="air")return"zigzag";if(e==="shadow"&&u>=3)return"teleport";if(e==="water")return"wave";switch(u){case 1:return"straight";case 2:return"sine";case 3:return"adaptive";case 4:return"evasive";case 5:return"unpredictable";default:return"straight"}}function ie(u,e=!1){const t=[];switch(u){case 1:break;case 2:t.push("leading_shots");break;case 3:t.push("shield_allies","homing_projectiles");break;case 4:t.push("evasion_dash","formation_flying","complex_patterns");break;case 5:t.push("multi_phase_attack","teleport","summon_allies","damage_resistance");break}return e&&(t.push("enhanced_stats","unpredictable_movement"),u>=3&&t.push("area_damage"),u>=4&&t.push("temporal_shield")),t}function ne(u,e){return`rgba(255, 215, 0, ${Math.min(255,100+e*30)/255})`}function re(u,e,t=!1){const s=["","Basic","Adaptive","Tactical","Complex","Elite"],a={air:"Air",water:"Water",fire:"Fire",earth:"Earth",crystal:"Crystal",shadow:"Shadow"},i=s[u]||"Unknown",n=a[e]||"Unknown";return t?`Elite ${n} (${i} tier)`:`${n} (${i} tier)`}class de extends G{constructor(e=0,t=0,s=l.AIR,a=1){super(e,t),this.type=s,this.level=a,this.baseMaxHealth=this.getTypeMaxHealth(s),this.baseSpeed=this.getTypeBaseSpeed(s),this.baseDamage=this.getTypeDamage(s),this.baseScoreValue=this.getTypeScoreValue(s),this.baseFireRate=this.getTypeFireRate(s),this.isDestroyed=!1,this.tier=H(a),this.isElite=ee(a),this.eliteGlow=this.isElite,this.difficultyCoefficient=z(a);const i={maxHealth:this.baseMaxHealth,speed:this.baseSpeed,damage:this.baseDamage,fireRate:this.baseFireRate,scoreValue:this.baseScoreValue},n=Z(i,a);this.scalingInfo={tier:this.tier,coefficient:this.difficultyCoefficient,baseStats:i,scaledStats:n},this.maxHealth=n.maxHealth,this.health=this.maxHealth,this.speed=n.speed,this.damage=n.damage,this.fireRate=n.fireRate,this.scoreValue=n.scoreValue,this.availableBehaviors=te(this.tier,this.isElite),this.firePattern=se(this.tier,this.type),this.movementPattern=ae(this.tier,this.type),this.specialAbilities=ie(this.tier,this.isElite);const r=this.applyEnvironmentalModifiers();this.maxHealth=r.maxHealth,this.health=this.maxHealth,this.damage=r.damage,this.currentSpeed=this.speed,this.isElite&&(this.eliteColor=ne(this.color,this.tier)),this.description=re(this.tier,this.type,this.isElite),this.collisionRadius=this.getTypeSize(s),this.color=this.getColorByType(),this.movementPatternOptions={},this.patternTime=0,this.sprite=null,this.useSprite=!1,this.spriteScale=2,this.specialBehaviorCooldown=0,this.specialBehaviorInterval=this.getTypeSpecialBehaviorInterval(s),this.formationCenter=null,this.formationOffset=null,this.inFormation=!1,this.state="formation",this.formationState="formation",this.formationIndex=0,this.diveStartTime=0,this.diveReturnTime=0,this.originalFormationCenter=null,this.originalFormationOffset=null,this.x=this.position.x,this.y=this.position.y,this.vx=0,this.vy=0,this.baseY=this.position.y,this.updateCollisionBounds()}applyEnvironmentalModifiers(){var c;const e=this.getLevelEnvironmentData();if(!e||!e.gameplayModifiers)return{maxHealth:this.maxHealth,damage:this.damage,speed:this.speed};const t=e.gameplayModifiers,s=this.type,i=1+((((c=t.enemyTypeModifiers)==null?void 0:c[s])??1)-1)*1.5,n=Math.round(this.maxHealth*i),r=Math.round(this.damage*i),o=this.speed;return{maxHealth:n,damage:r,speed:o}}getLevelEnvironmentData(){return window.gameEngine&&window.gameEngine.levelManager&&window.gameEngine.levelManager.levelConfig&&window.gameEngine.levelManager.levelConfig.environmentData?window.gameEngine.levelManager.levelConfig.environmentData:null}getColorByType(){switch(this.type){case l.WATER:return"#3498db";case l.FIRE:return"#e74c3c";case l.AIR:return"#ecf0f1";case l.EARTH:return"#8b4513";case l.CRYSTAL:return"#9b59b6";case l.SHADOW:return"#2c3e50";default:return"#95a5a6"}}getTypeSize(e){const t=window.innerWidth<=768;let s;switch(e){case l.WATER:s=20;case l.FIRE:s=14;case l.AIR:s=16;case l.EARTH:s=28;case l.CRYSTAL:s=24;case l.SHADOW:s=12;default:s=20}return t?s*.5:s}getTypeDamage(e){switch(e){case l.WATER:return 12;case l.FIRE:return 18;case l.AIR:return 10;case l.EARTH:return 25;case l.CRYSTAL:return 15;case l.SHADOW:return 20;default:return 12}}getTypeFireRate(e){switch(e){case l.WATER:return .8;case l.FIRE:return 1.5;case l.AIR:return 1.2;case l.EARTH:return .5;case l.CRYSTAL:return .7;case l.SHADOW:return 1;default:return 1}}getTypeSpecialBehaviorInterval(e){switch(e){case l.WATER:return 5e3;case l.FIRE:return 3e3;case l.AIR:return 4e3;case l.EARTH:return 6e3;case l.CRYSTAL:return 7e3;case l.SHADOW:return 3500;default:return 5e3}}getTypeBaseSpeed(e){switch(e){case l.WATER:return 7;case l.FIRE:return 11;case l.AIR:return 10;case l.EARTH:return 5;case l.CRYSTAL:return 6;case l.SHADOW:return 12;default:return 8}}getTypeMaxHealth(e){switch(e){case l.WATER:return 50;case l.FIRE:return 25;case l.AIR:return 25;case l.EARTH:return 75;case l.CRYSTAL:return 75;case l.SHADOW:return 50;default:return 25}}getTypeScoreValue(e){switch(e){case l.WATER:return 15;case l.FIRE:return 20;case l.AIR:return 18;case l.EARTH:return 25;case l.CRYSTAL:return 30;case l.SHADOW:return 35;default:return 15}}update(e,t=null){if(this.active){if(this.isFrozen&&this.freezeEndTime&&Date.now()<this.freezeEndTime){super.update(e);return}else this.isFrozen&&this.freezeEndTime&&Date.now()>=this.freezeEndTime&&(this.isFrozen=!1,this.freezeEndTime=null,this.originalVelocity&&(this.velocity=this.originalVelocity.clone(),this.originalVelocity=null));if(this.state==="dive"){this.velocity instanceof m||(this.velocity=new m(this.velocity.x||0,this.velocity.y||0)),this.velocity&&this.position.addInPlace(this.velocity.clone().multiply(e/1e3)),this.updateSpecialBehaviorCooldown(e);return}super.update(e),this.updateSpecialBehaviorCooldown(e),this.updateMovementPattern(e,t),this.checkBounds()}}updateSpecialBehaviorCooldown(e){this.specialBehaviorCooldown+=e,this.specialBehaviorCooldown>=this.specialBehaviorInterval&&(this.executeSpecialBehavior(null),this.specialBehaviorCooldown=0)}executeSpecialBehavior(e){if(e){switch(this.type){case l.WATER:this.executeWaterBehavior(e);break;case l.FIRE:this.executeFireBehavior(e);break;case l.AIR:this.executeAirBehavior(e);break;case l.EARTH:this.executeEarthBehavior(e);break;case l.CRYSTAL:this.executeCrystalBehavior(e);break;case l.SHADOW:this.executeShadowBehavior(e);break}this.executeTierBehaviors(e)}}executeTierBehaviors(e){!this.availableBehaviors||!e||(this.tier>=2&&this.availableBehaviors.includes("spread_shot"),this.tier>=3&&this.availableBehaviors.includes("homing_projectiles"),this.tier>=4&&this.availableBehaviors.includes("evasion_dash")&&Math.random()<.03&&this.performEvasionDash(e),this.isElite&&this.executeEliteBehaviors(e))}performEvasionDash(e){const t=e.subtract(this.position),s=new m(-t.y,t.x).normalize(),a=Math.random()<.5?s:s.multiply(-1);this.velocity=a.multiply(this.speed*2)}executeEliteBehaviors(e){if(Math.random()<.02){const t=new m((Math.random()-.5)*2,(Math.random()-.5)*2).normalize();this.velocity=t.multiply(this.speed*1.5)}}executeWaterBehavior(e){const t=this.currentSpeed||this.speed;this.currentSpeed=t*1.5;const s=this.directionTo(e);this.velocity=s.multiply(this.currentSpeed),setTimeout(()=>{this.currentSpeed=t},1e3)}executeFireBehavior(e){const t=this.directionTo(e),s=(this.currentSpeed||this.speed)*2.5;this.velocity=t.multiply(s),this.movementPattern="player_follow",this.movementPatternOptions={duration:1e3},setTimeout(()=>{this.movementPattern="straight",this.movementPatternOptions={}},1e3)}executeAirBehavior(e){const t=this.directionTo(e),s=new m(-t.y,t.x);this.velocity=s.multiply((this.currentSpeed||this.speed)*1.8),this.movementPattern="sine",this.movementPatternOptions={amplitude:100},setTimeout(()=>{this.movementPattern="straight",this.movementPatternOptions={}},1500)}executeEarthBehavior(e){const t=this.currentSpeed||this.speed;this.currentSpeed=t*.5,setTimeout(()=>{this.currentSpeed=t},2e3)}executeCrystalBehavior(e){const t=this.currentSpeed||this.speed;this.currentSpeed=t*1.3,setTimeout(()=>{this.currentSpeed=t},1500)}executeShadowBehavior(e){const t=Math.random()*Math.PI*2,s=100+Math.random()*100,a=e.x+Math.cos(t)*s,i=e.y+Math.sin(t)*s,n=Math.max(50,Math.min(E.CANVAS_WIDTH-50,a)),r=Math.max(50,Math.min(E.CANVAS_HEIGHT-50,i));this.position=new m(n,r),this.velocity=new m(0,0),setTimeout(()=>{this.movementPattern="straight"},500)}updateMovementPattern(e,t){if(this.patternTime+=e,this.state!=="dive")switch(this.movementPattern){case"straight":this.updateStraightMovement();break;case"sine":this.updateSineMovement();break;case"player_follow":this.updatePlayerFollowMovement(t);break;case"formation":if(this.formationCenter&&this.formationOffset){const s=this.formationCenter.add(this.formationOffset);if(this.position.distance(s)>5){const i=this.directionTo(s),n=(this.currentSpeed||this.speed)*1.2;this.velocity=i.multiply(n)}else this.velocity=this.velocity.multiply(.9)}break}}updateStraightMovement(){const e=this.currentSpeed||this.speed;switch(this.type){case l.EARTH:this.movementPatternOptions.pauseInterval&&this.patternTime%this.movementPatternOptions.pauseInterval<100?this.velocity.set(0,0):this.velocity.set(0,e*.6);break;default:this.velocity.set(0,e);break}}updateSineMovement(){const e=this.currentSpeed||this.speed,t=this.movementPatternOptions.amplitude||50,s=this.movementPatternOptions.frequency||1;switch(this.type){case l.WATER:this.velocity.y=e*.8,this.velocity.x=Math.sin(this.patternTime/(200/s))*t,this.movementPatternOptions.changeInterval&&this.patternTime%this.movementPatternOptions.changeInterval<100&&(this.velocity.x*=-1);break;case l.AIR:this.velocity.y=e*1.2;const a=Math.sin(this.patternTime/(100/s))*t,i=Math.sin(this.patternTime/300)*.3;this.velocity.x=a+i*t;break;case l.CRYSTAL:this.velocity.y=e;const n=Math.sin(this.patternTime/(250/s))*t;this.movementPatternOptions.pulseInterval&&this.patternTime%this.movementPatternOptions.pulseInterval<200?this.velocity.x=n*1.5:this.velocity.x=n;break;default:this.velocity.y=e,this.velocity.x=Math.sin(this.patternTime/(200/s))*t;break}}updatePlayerFollowMovement(e){if(!e)return;const t=this.currentSpeed||this.speed,s=this.directionTo(e);switch(this.type){case l.FIRE:const a=this.movementPatternOptions.aggression||.7;this.velocity=s.multiply(t*(1+a));break;case l.SHADOW:if(this.movementPatternOptions.indirectApproach){const i=new m(-s.y,s.x),n=Math.sin(this.patternTime/500)*.4,r=s.add(i.multiply(n));this.velocity=r.normalize().multiply(t*.9)}else this.velocity=s.multiply(t);break;default:this.velocity=s.multiply(t);break}}setMovementPattern(e,t={}){this.movementPattern=e,this.movementPatternOptions=t,this.patternTime=0}checkBounds(){const e=this.canvasWidth||E.CANVAS_WIDTH,t=this.canvasHeight||E.CANVAS_HEIGHT,s={left:-50,right:e+50,top:-50,bottom:t+50};this.isOutOfBounds(s)&&this.destroy()}takeDamage(e){const t=Math.max(0,e);this.health-=t;const s={damageTaken:t,destroyed:!1,scoreValue:0};return this.health<=0&&(this.isDestroyed=!0,this.destroy(),s.destroyed=!0,s.scoreValue=this.scoreValue),s}render(e,t=0){if(!this.visible)return;super.render(e,t);const s=this.position.add(this.velocity.multiply(t/1e3));e.save(),e.translate(s.x,s.y),e.rotate(this.rotation),this.isElite&&this.eliteGlow&&(e.shadowColor="#ffff00",e.shadowBlur=20);const a=this.getSprite();if(a){const i=this.collisionRadius*2*this.spriteScale;this.isElite||(e.shadowColor=this.color,e.shadowBlur=10),e.drawImage(a,-i/2,-i/2,i,i),e.shadowBlur=0}else e.fillStyle=this.color,this.isElite&&(e.strokeStyle="#ffff00",e.lineWidth=3),e.beginPath(),e.arc(0,0,this.collisionRadius,0,Math.PI*2),e.fill(),this.isElite&&e.stroke();if(this.health<this.maxHealth){const i=this.health/this.maxHealth,n=this.collisionRadius*2,r=n*i;e.fillStyle="#ff0000",e.fillRect(-this.collisionRadius,-this.collisionRadius-10,n,4),e.fillStyle="#00ff00",e.fillRect(-this.collisionRadius,-this.collisionRadius-10,r,4)}e.restore()}setFormationTarget(e,t){this.formationCenter=e,this.formationOffset=t,this.inFormation=!0,this.movementPattern="formation",this.initialFormationOffset||(this.initialFormationOffset=t.clone())}reset(){super.reset(),this.baseMaxHealth=this.getTypeMaxHealth(this.type),this.baseSpeed=this.getTypeBaseSpeed(this.type),this.baseDamage=this.getTypeDamage(this.type),this.baseScoreValue=this.getTypeScoreValue(this.type),this.baseFireRate=this.getTypeFireRate(this.type),this.tier=H(this.level),this.isElite=ee(this.level),this.eliteGlow=this.isElite,this.difficultyCoefficient=z(this.level);const e={maxHealth:this.baseMaxHealth,speed:this.baseSpeed,damage:this.baseDamage,fireRate:this.baseFireRate,scoreValue:this.baseScoreValue},t=Z(e,this.level);this.scalingInfo={tier:this.tier,coefficient:this.difficultyCoefficient,baseStats:e,scaledStats:t},this.maxHealth=t.maxHealth,this.health=this.maxHealth,this.speed=t.speed,this.damage=t.damage,this.fireRate=t.fireRate,this.scoreValue=t.scoreValue,this.availableBehaviors=te(this.tier,this.isElite),this.firePattern=se(this.tier,this.type),this.movementPattern=ae(this.tier,this.type),this.specialAbilities=ie(this.tier,this.isElite);const s=this.applyEnvironmentalModifiers();this.maxHealth=s.maxHealth,this.health=s.maxHealth,this.damage=s.damage,this.currentSpeed=this.speed,this.isElite&&(this.eliteColor=ne(this.color,this.tier)),this.description=re(this.tier,this.type,this.isElite),this.isDestroyed=!1,this.movementPatternOptions={},this.patternTime=0,this.color=this.getColorByType(),this.collisionRadius=this.getTypeSize(this.type),this.specialBehaviorCooldown=0,this.specialBehaviorInterval=this.getTypeSpecialBehaviorInterval(this.type),this.formationCenter=null,this.formationOffset=null,this.initialFormationOffset=null,this.inFormation=!1,this.formationState="formation",this.formationIndex=0,this.diveStartTime=0,this.diveReturnTime=0,this.originalFormationCenter=null,this.originalFormationOffset=null,this.enemyManager&&(this.canvasWidth=this.enemyManager.canvasWidth,this.canvasHeight=this.enemyManager.canvasHeight)}getSprite(){if(window.gameEngine&&window.gameEngine.enemySprites){const e=window.gameEngine.enemySprites.get(this.type);if(e&&e.complete)return e}return null}}class ze extends de{constructor(e=0,t=0,s=l.SHADOW){super(e,t,s),this.isBoss=!0,this.bossName=this.generateBossName(),this.bossPhase=1,this.maxPhases=3,this.maxHealth=500,this.health=this.maxHealth,this.baseSpeed=1.5,this.currentSpeed=this.baseSpeed,this.scoreValue=100,this.collisionRadius=40,this.abilities=this.generateBossAbilities(),this.abilityCooldowns={},this.lastAbilityTime=0,this.abilityInterval=5e3,this.canInitiateWarp=!0,this.warpTriggerHealth=[.75,.5,.25],this.warpsInitiated=0,this.maxWarps=3,this.warpCooldown=1e4,this.lastWarpTime=0,this.isWarping=!1,this.warpDuration=3e3,this.movementPatterns=["circular","figure_eight","aggressive_dive","defensive_retreat"],this.currentMovementPattern=0,this.patternChangeInterval=8e3,this.lastPatternChange=0,this.pulseAnimation=0,this.phaseTransitionAnimation=0,this.warpAnimation=0,this.encounterStartTime=Date.now(),this.phaseTransitionThreshold=.3,this.initializeBoss()}initializeBoss(){this.color=this.getBossColor();for(const e of this.abilities)this.abilityCooldowns[e.name]=0;this.setBossMovementPattern(this.movementPatterns[0])}generateBossName(){const e={[l.WATER]:["Abyssal","Oceanic","Tidal","Aquatic"],[l.FIRE]:["Infernal","Burning","Volcanic","Blazing"],[l.AIR]:["Tempest","Zephyr","Aerial","Sky"],[l.EARTH]:["Terran","Mountain","Seismic","Ground"],[l.CRYSTAL]:["Prismatic","Crystal","Refracted","Luminous"],[l.SHADOW]:["Shadow","Dark","Void","Nightmare"]},t=["Destroyer","Overlord","Titan","Colossus","Behemoth","Leviathan"],s=e[this.type]||e[l.SHADOW],a=s[Math.floor(Math.random()*s.length)],i=t[Math.floor(Math.random()*t.length)];return`${a} ${i}`}getBossColor(){return{[l.WATER]:"#3498db",[l.FIRE]:"#e74c3c",[l.AIR]:"#ecf0f1",[l.EARTH]:"#8b4513",[l.CRYSTAL]:"#9b59b6",[l.SHADOW]:"#2c3e50"}[this.type]||"#2c3e50"}generateBossAbilities(){const e={[l.WATER]:[{name:"tidal_wave",damage:30,cooldown:8e3,description:"Unleash a tidal wave"},{name:"water_shield",damage:0,cooldown:12e3,description:"Create protective water shield"},{name:"aquatic_summon",damage:0,cooldown:15e3,description:"Summon water minions"}],[l.FIRE]:[{name:"meteor_shower",damage:40,cooldown:1e4,description:"Rain down meteors"},{name:"fire_nova",damage:25,cooldown:7e3,description:"Explosive fire burst"},{name:"lava_eruption",damage:50,cooldown:2e4,description:"Erupt lava from below"}],[l.AIR]:[{name:"tornado",damage:20,cooldown:9e3,description:"Create damaging tornado"},{name:"lightning_strike",damage:35,cooldown:6e3,description:"Strike with lightning"},{name:"wind_barrier",damage:0,cooldown:11e3,description:"Create wind barrier"}],[l.EARTH]:[{name:"earthquake",damage:30,cooldown:12e3,description:"Shake the battlefield"},{name:"rock_throw",damage:25,cooldown:5e3,description:"Throw massive rocks"},{name:"stone_skin",damage:0,cooldown:15e3,description:"Harden skin for defense"}],[l.CRYSTAL]:[{name:"crystal_shard",damage:20,cooldown:4e3,description:"Shoot crystal shards"},{name:"prismatic_beam",damage:45,cooldown:1e4,description:"Fire prismatic energy beam"},{name:"crystalize",damage:0,cooldown:18e3,description:"Crystalize and heal"}],[l.SHADOW]:[{name:"shadow_bolt",damage:25,cooldown:5e3,description:"Shoot shadow bolts"},{name:"void_portal",damage:0,cooldown:14e3,description:"Open void portal for minions"},{name:"reality_warp",damage:0,cooldown:25e3,description:"Initiate reality warp"}]};return e[this.type]||e[l.SHADOW]}update(e,t=null){this.active&&(super.update(e,t),this.updateAnimations(e),this.updateBossPhase(),this.updateAbilities(e,t),this.updateMovementPattern(e),this.checkWarpTriggers(),this.isWarping&&this.updateWarpAnimation(e))}updateAnimations(e){this.pulseAnimation+=e*.003,this.phaseTransitionAnimation>0&&(this.phaseTransitionAnimation-=e,this.phaseTransitionAnimation<0&&(this.phaseTransitionAnimation=0))}updateBossPhase(){const e=this.health/this.maxHealth,t=Math.max(1,this.maxPhases-Math.floor(e/this.phaseTransitionThreshold));t!==this.bossPhase&&t>this.bossPhase&&(this.bossPhase=t,this.phaseTransitionAnimation=1e3,this.onPhaseTransition())}onPhaseTransition(){this.baseSpeed*=1.2,this.currentSpeed=this.baseSpeed;for(const e of this.abilities)e.cooldown*=.8;this.currentMovementPattern=(this.currentMovementPattern+1)%this.movementPatterns.length,this.setBossMovementPattern(this.movementPatterns[this.currentMovementPattern])}updateAbilities(e,t){if(!t)return;const s=Date.now();for(const a of this.abilities)this.abilityCooldowns[a.name]>0&&(this.abilityCooldowns[a.name]-=e,this.abilityCooldowns[a.name]<0&&(this.abilityCooldowns[a.name]=0));s-this.lastAbilityTime>this.abilityInterval&&(this.useRandomAbility(t),this.lastAbilityTime=s)}useRandomAbility(e){const t=this.abilities.filter(a=>this.abilityCooldowns[a.name]<=0);if(t.length===0)return;const s=t[Math.floor(Math.random()*t.length)];this.useAbility(s,e)}useAbility(e,t){this.abilityCooldowns[e.name]=e.cooldown,this.executeAbilityEffect(e,t),this.onAbilityUsed&&this.onAbilityUsed(e,t)}executeAbilityEffect(e,t){e.name==="reality_warp"&&this.initiateRealityWarp()}updateMovementPattern(e){const t=Date.now();t-this.lastPatternChange>this.patternChangeInterval&&(this.currentMovementPattern=(this.currentMovementPattern+1)%this.movementPatterns.length,this.setBossMovementPattern(this.movementPatterns[this.currentMovementPattern]),this.lastPatternChange=t)}setBossMovementPattern(e){const t={amplitude:100+this.bossPhase*20,frequency:.5+this.bossPhase*.1,phase:this.bossPhase};this.setMovementPattern(e,t)}checkWarpTriggers(){if(!this.canInitiateWarp||this.isWarping)return;const e=Date.now(),t=this.health/this.maxHealth;if(!(e-this.lastWarpTime<this.warpCooldown))for(let s=0;s<this.warpTriggerHealth.length;s++){const a=this.warpTriggerHealth[s];if(t<=a&&this.warpsInitiated<=s){this.initiateRealityWarp();break}}}initiateRealityWarp(){if(this.warpsInitiated>=this.maxWarps)return;this.isWarping=!0,this.warpAnimation=0,this.lastWarpTime=Date.now(),this.warpsInitiated++;const e=this.getBossWarpPrompt();this.onWarpInitiated&&this.onWarpInitiated(this,e)}updateWarpAnimation(e){this.warpAnimation+=e,this.warpAnimation>=this.warpDuration&&this.completeWarp()}completeWarp(){this.isWarping=!1,this.warpAnimation=0,this.onWarpCompleted&&this.onWarpCompleted(this)}onWarpCompleted(e){this.isWarping=!1,this.warpAnimation=0,this.applyPostWarpEffects()}applyPostWarpEffects(){this.baseSpeed*=1.1,this.currentSpeed=this.baseSpeed;for(const t of this.abilities)t.cooldown*=.9;const e=this.maxHealth*.1;this.health=Math.min(this.health+e,this.maxHealth)}getBossWarpPrompt(){const e={[l.WATER]:["The oceanic boss summons a massive tidal wave that transforms the battlefield into a submerged realm with crushing pressure and aquatic hazards.","The abyssal overlord opens a portal to the deepest ocean trenches, filling the battlefield with ancient leviathans and crushing water pressure.","The tidal titan unleashes a great flood that submerges the battlefield in a primordial ocean with ancient sea creatures."],[l.FIRE]:["The infernal boss erupts in volcanic fury, transforming the battlefield into a hellscape of flowing lava and fire storms.","The burning overlord ignites the atmosphere, turning the battlefield into a volcanic wasteland with falling meteors and rivers of lava.","The blazing titan summons a solar flare that incinerates the battlefield into a fiery inferno with intense heat waves."],[l.AIR]:["The tempest boss summons a massive storm that transforms the battlefield into a chaotic sky realm with lightning strikes and tornadoes.","The zephyr overlord creates an eternal storm, filling the battlefield with hurricane winds and electrical discharges.","The aerial titan opens a portal to the sky realm, bringing forth thunderclouds and devastating wind patterns."],[l.EARTH]:["The terran boss causes a massive earthquake that transforms the battlefield into a rocky wasteland with seismic hazards.","The mountain overlord raises ancient mountains, turning the battlefield into a treacherous landscape with falling rocks and seismic activity.","The seismic titan awakens the earth's core, creating a battlefield of molten rock and geological instability."],[l.CRYSTAL]:["The prismatic boss refracts reality, transforming the battlefield into a crystalline dimension with light-based hazards.","The crystal overlord grows massive crystal formations, turning the battlefield into a prismatic maze with refractive defenses.","The luminous titan summons a crystal wave that crystallizes the entire battlefield into a gemstone landscape."],[l.SHADOW]:["The shadow boss tears the fabric of reality, transforming the battlefield into a void dimension with shadow creatures.","The dark overlord opens a portal to the shadow realm, filling the battlefield with creeping darkness and void entities.","The void titan warps reality itself, creating a battlefield of pure darkness with reality-warping anomalies."]},t=e[this.type]||e[l.SHADOW];return t[Math.min(this.warpsInitiated,t.length-1)]}takeDamage(e){const t=super.takeDamage(e);return this.onDamageTaken&&this.onDamageTaken(e,t),t}render(e,t=0){if(!this.visible)return;super.render(e,t);const s=this.position.add(this.velocity.multiply(t/1e3));e.save(),e.translate(s.x,s.y),e.rotate(this.rotation),this.renderBossEffects(e),this.renderBossHealthBar(e),this.isWarping&&this.renderWarpAnimation(e),e.restore()}renderBossEffects(e){const t=this.collisionRadius+Math.sin(this.pulseAnimation)*10;if(e.shadowColor=this.color,e.shadowBlur=15,e.strokeStyle=this.color,e.lineWidth=3,e.globalAlpha=.6,e.beginPath(),e.arc(0,0,t,0,Math.PI*2),e.stroke(),this.phaseTransitionAnimation>0){const s=this.collisionRadius+this.phaseTransitionAnimation/1e3*30;e.shadowColor="#ffffff",e.shadowBlur=20,e.strokeStyle="#ffffff",e.lineWidth=5,e.globalAlpha=this.phaseTransitionAnimation/1e3,e.beginPath(),e.arc(0,0,s,0,Math.PI*2),e.stroke()}e.globalAlpha=1,e.shadowBlur=0}renderBossHealthBar(e){const t=this.collisionRadius*3,s=8,a=-this.collisionRadius-25;e.fillStyle="#333333",e.fillRect(-t/2,a,t,s);const i=this.health/this.maxHealth;e.fillStyle=i>.5?"#00ff00":i>.25?"#ffff00":"#ff0000",e.fillRect(-t/2,a,t*i,s),e.strokeStyle="#ffffff",e.lineWidth=1,e.strokeRect(-t/2,a,t,s),e.fillStyle="#ffffff",e.font="12px Arial",e.textAlign="center",e.fillText(this.bossName,0,a-5),e.fillStyle="#ffff00",e.fillText(`Phase ${this.bossPhase}`,0,a+20)}renderWarpAnimation(e){const t=this.warpAnimation/this.warpDuration,s=this.collisionRadius+t*100;e.strokeStyle="#ff00ff",e.lineWidth=3,e.globalAlpha=1-t,e.beginPath(),e.arc(0,0,s,0,Math.PI*2),e.stroke(),e.strokeStyle="#00ffff",e.lineWidth=2,e.globalAlpha=(1-t)*.5,e.beginPath(),e.arc(0,0,s*.7,0,Math.PI*2),e.stroke(),e.globalAlpha=1}reset(){super.reset(),this.bossPhase=1,this.health=this.maxHealth,this.currentSpeed=this.baseSpeed,this.warpsInitiated=0,this.isWarping=!1,this.warpAnimation=0,this.lastWarpTime=0,this.lastAbilityTime=0,this.lastPatternChange=0,this.currentMovementPattern=0,this.pulseAnimation=0,this.phaseTransitionAnimation=0,this.encounterStartTime=Date.now();for(const e of this.abilities)this.abilityCooldowns[e.name]=0;this.setBossMovementPattern(this.movementPatterns[0])}}class Ke{constructor(e=800,t=600,s=null){this.canvasWidth=e,this.canvasHeight=t,this.gameObjectManager=s,this.activeProjectiles=[],this.projectilePool=[],this.maxProjectiles=100,this.projectileTypes=this.initializeProjectileTypes(),this.muzzleFlashes=[],this.impactEffects=[]}initializeProjectileTypes(){return{[l.AIR]:{speed:200,damage:15,color:"#FF69B4",trailColor:"#FF1493",size:{width:4,height:10},lifetime:4e3,pattern:"straight",sound:"air_shot"},[l.WATER]:{speed:150,damage:20,color:"#00BFFF",trailColor:"#87CEEB",size:{width:5,height:8},lifetime:5e3,pattern:"wave",sound:"water_shot"},[l.FIRE]:{speed:250,damage:25,color:"#FF4500",trailColor:"#FFD700",size:{width:5,height:12},lifetime:3e3,pattern:"straight",sound:"fire_shot"},[l.EARTH]:{speed:120,damage:35,color:"#8B4513",trailColor:"#A0522D",size:{width:8,height:10},lifetime:6e3,pattern:"heavy",sound:"earth_shot"},[l.CRYSTAL]:{speed:180,damage:20,color:"#9370DB",trailColor:"#DDA0DD",size:{width:4,height:14},lifetime:4500,pattern:"precise",sound:"crystal_shot"},[l.SHADOW]:{speed:220,damage:22,color:"#8B008B",trailColor:"#9932CC",size:{width:6,height:8},lifetime:3500,pattern:"stealth",sound:"shadow_shot"}}}update(e,t=null){this.updateActiveProjectiles(e,t),this.updateMuzzleFlashes(e),this.updateImpactEffects(e),this.cleanupProjectiles()}updateActiveProjectiles(e,t){for(const s of this.activeProjectiles)s.active&&(this.applyProjectilePattern(s,e,t),s.update(e),this.isProjectileOutOfBounds(s)&&s.destroy())}applyProjectilePattern(e,t,s){const a=t/1e3;switch(e.pattern){case"straight":break;case"wave":this.applyWavePattern(e,a);break;case"arc":this.applyArcPattern(e,a);break;case"homing":this.applyHomingPattern(e,a,s);break;case"phase":this.applyPhasePattern(e,a);break}}applyWavePattern(e,t){e.waveData||(e.waveData={amplitude:30,frequency:3,baseDirection:e.velocity.clone().normalize()});const s=Math.sin(e.age/1e3*e.waveData.frequency)*e.waveData.amplitude,i=e.waveData.baseDirection.perpendicular().multiply(s*t);e.velocity.addInPlace(i),e.velocity=e.velocity.normalize().multiply(e.speed)}applyArcPattern(e,t){e.arcData||(e.arcData={gravity:150}),e.velocity.y+=e.arcData.gravity*t}applyHomingPattern(e,t,s){if(!s||(e.homingData||(e.homingData={turnRate:2,homingDelay:500,maxTurnAngle:Math.PI/4}),e.age<e.homingData.homingDelay))return;const a=s.subtract(e.position).normalize(),i=e.velocity.normalize(),n=a.angle(),r=i.angle();let o=C.angleDifference(r,n);const c=e.homingData.turnRate*t;o=C.clamp(o,-c,c);const h=r+o,d=m.fromAngle(h);e.velocity=d.multiply(e.speed)}applyPhasePattern(e,t){if(e.phaseData||(e.phaseData={phaseInterval:800,phaseDistance:40,lastPhaseTime:0,isPhasing:!1,phaseAlpha:1}),e.age-e.phaseData.lastPhaseTime>=e.phaseData.phaseInterval&&!e.phaseData.isPhasing){e.phaseData.isPhasing=!0,e.phaseData.lastPhaseTime=e.age;const i=e.velocity.normalize().multiply(e.phaseData.phaseDistance);e.position.addInPlace(i)}if(e.phaseData.isPhasing){const a=(e.age-e.phaseData.lastPhaseTime)/200;a<1?e.phaseData.phaseAlpha=.3+.7*Math.abs(Math.sin(a*Math.PI*4)):(e.phaseData.isPhasing=!1,e.phaseData.phaseAlpha=1)}}fireProjectile(e,t,s,a={}){if(this.activeProjectiles.length>=this.maxProjectiles)return null;const i=this.projectileTypes[s]||this.projectileTypes[l.AIR];let n=this.getProjectileFromPool();n||(n=new Ye);const r=a.speed||i.speed,o=a.damage||i.damage;return n.initialize(e.clone(),t.normalize(),r,s,o,i),n.addTag("enemy_projectile"),this.activeProjectiles.push(n),this.gameObjectManager&&this.gameObjectManager.add(n),this.createMuzzleFlash(e,t,i),n}firePattern(e,t,s,a,i={}){const n=[];switch(a){case"spread":n.push(...this.fireSpreadPattern(e,t,s,i));break;case"burst":n.push(...this.fireBurstPattern(e,t,s,i));break;case"spiral":n.push(...this.fireSpiralPattern(e,t,s,i));break;case"ring":n.push(...this.fireRingPattern(e,s,i));break}return n}fireSpreadPattern(e,t,s,a){const i=[],n=a.count||3,r=a.spreadAngle||Math.PI/6;for(let o=0;o<n;o++){const c=(o-(n-1)/2)*(r/(n-1)),h=t.rotate(c),d=this.fireProjectile(e,h,s,a);d&&i.push(d)}return i}fireBurstPattern(e,t,s,a){const i=[],n=a.count||3;a.burstDelay;for(let r=0;r<n;r++){const o=t.multiply(r*-10),c=e.add(o),h=this.fireProjectile(c,t,s,a);h&&i.push(h)}return i}fireSpiralPattern(e,t,s,a){const i=[],n=a.count||6,r=a.spiralOffset||0;for(let o=0;o<n;o++){const c=o/n*Math.PI*2+r,h=m.fromAngle(c),d=this.fireProjectile(e,h,s,a);d&&i.push(d)}return i}fireRingPattern(e,t,s){const a=[],i=s.count||8;for(let n=0;n<i;n++){const r=n/i*Math.PI*2,o=m.fromAngle(r),c=this.fireProjectile(e,o,t,s);c&&a.push(c)}return a}createMuzzleFlash(e,t,s){const a={position:e.clone(),direction:t.clone(),color:s.color,size:8,lifetime:150,age:0,alpha:1};this.muzzleFlashes.push(a)}createImpactEffect(e,t){const s=this.projectileTypes[t]||this.projectileTypes[l.AIR],a={position:e.clone(),color:s.color,trailColor:s.trailColor,particles:[],lifetime:300,age:0};for(let i=0;i<6;i++){const n=i/6*Math.PI*2,r=C.random(50,150);a.particles.push({position:e.clone(),velocity:m.fromAngle(n).multiply(r),size:C.random(2,4),alpha:1})}this.impactEffects.push(a)}updateMuzzleFlashes(e){for(let t=this.muzzleFlashes.length-1;t>=0;t--){const s=this.muzzleFlashes[t];s.age+=e,s.alpha=1-s.age/s.lifetime,s.age>=s.lifetime&&this.muzzleFlashes.splice(t,1)}}updateImpactEffects(e){const t=e/1e3;for(let s=this.impactEffects.length-1;s>=0;s--){const a=this.impactEffects[s];a.age+=e;for(const i of a.particles)i.position.addInPlace(i.velocity.multiply(t)),i.velocity.multiplyInPlace(.95),i.alpha=1-a.age/a.lifetime;a.age>=a.lifetime&&this.impactEffects.splice(s,1)}}isProjectileOutOfBounds(e){return e.position.x<-50||e.position.x>this.canvasWidth+50||e.position.y<-50||e.position.y>this.canvasHeight+50}cleanupProjectiles(){for(let e=this.activeProjectiles.length-1;e>=0;e--){const t=this.activeProjectiles[e];(t.destroyed||!t.active)&&(this.returnProjectileToPool(t),this.activeProjectiles.splice(e,1))}}getProjectileFromPool(){return this.projectilePool.length>0?this.projectilePool.pop():null}returnProjectileToPool(e){this.projectilePool.length<this.maxProjectiles&&(e.reset(),this.projectilePool.push(e)),this.gameObjectManager&&this.gameObjectManager.remove(e)}checkPlayerCollisions(e){const t=[];if(!e.active||e.isInvulnerable)return t;for(const s of this.activeProjectiles)s.active&&s.collidesWith(e)&&t.push(s);return t}handlePlayerCollision(e,t){if(t.hasShield)return this.reflectProjectile(e),{damage:0,damageTaken:0,shieldReflection:!0};const s=t.takeDamage(e.damage);return this.createImpactEffect(e.position,e.enemyType),e.destroy(),s}reflectProjectile(e){const t=new m(0,-1),s=this.fireProjectile(e.position.clone(),t,"REFLECTED",{damage:e.damage,speed:e.speed*1.2,color:"#00ffff",trailColor:"#88ffff",size:{width:4,height:10},lifetime:3e3,pattern:"straight"});s&&(s.addTag("player-projectile"),s.isReflected=!0,this.createReflectionEffect(e.position)),e.destroy()}createReflectionEffect(e){const t={position:e.clone(),color:"#00ffff",trailColor:"#88ffff",size:20,lifetime:300,age:0,alpha:1,particles:[]};for(let s=0;s<8;s++){const a=s/8*Math.PI*2,i=50,n=e.clone().add(m.fromAngle(a).multiply(10));t.particles.push({position:n,velocity:m.fromAngle(a).multiply(i),size:3,alpha:1})}this.impactEffects.push(t)}render(e,t=0){for(const s of this.activeProjectiles)s.visible&&s.render(e,t);this.renderMuzzleFlashes(e),this.renderImpactEffects(e),window.DEBUG_MODE&&this.renderDebugInfo(e)}renderMuzzleFlashes(e){for(const t of this.muzzleFlashes){e.save(),e.globalAlpha=t.alpha,e.fillStyle=t.color,e.translate(t.position.x,t.position.y);const s=t.size*(1+(1-t.alpha));e.beginPath(),e.arc(0,0,s,0,Math.PI*2),e.fill(),e.fillStyle="#FFFFFF",e.beginPath(),e.arc(0,0,s*.5,0,Math.PI*2),e.fill(),e.restore()}}renderImpactEffects(e){for(const t of this.impactEffects){e.save();for(const s of t.particles)e.globalAlpha=s.alpha,e.fillStyle=t.color,e.beginPath(),e.arc(s.position.x,s.position.y,s.size,0,Math.PI*2),e.fill();e.restore()}}renderDebugInfo(e){e.fillStyle="#FFFFFF",e.font="12px Arial",e.textAlign="left";const t=[`Enemy Projectiles: ${this.activeProjectiles.length}`,`Muzzle Flashes: ${this.muzzleFlashes.length}`,`Impact Effects: ${this.impactEffects.length}`,`Projectile Pool: ${this.projectilePool.length}`];for(let s=0;s<t.length;s++)e.fillText(t[s],this.canvasWidth-200,20+s*15)}reset(){for(const e of this.activeProjectiles)e.destroy();this.activeProjectiles=[],this.muzzleFlashes=[],this.impactEffects=[]}updateCanvasDimensions(e,t){this.canvasWidth=e,this.canvasHeight=t}getStatistics(){return{activeProjectiles:this.activeProjectiles.length,pooledProjectiles:this.projectilePool.length,muzzleFlashes:this.muzzleFlashes.length,impactEffects:this.impactEffects.length,maxProjectiles:this.maxProjectiles}}}class Ye extends j{constructor(){super(),this.enemyType=l.AIR,this.pattern="straight",this.damage=15}initialize(e,t,s,a,i,n){return super.initialize(e,t,s,"enemy",null),this.enemyType=a,this.pattern=n.pattern,this.damage=i,this.lifetime=n.lifetime,this.color=n.color,this.trailColor=n.trailColor,this.width=n.size.width,this.height=n.size.height,this.collisionRadius=Math.max(this.width,this.height)/2,this.waveData=null,this.arcData=null,this.homingData=null,this.phaseData=null,this}render(e,t=0){if(!this.visible)return;const s=e.globalAlpha;this.phaseData&&this.phaseData.phaseAlpha<1&&(e.globalAlpha=this.phaseData.phaseAlpha),super.render(e,t),e.globalAlpha=s}renderProjectile(e,t){switch(e.translate(t.x,t.y),this.enemyType){case l.AIR:this.drawAirProjectile(e);break;case l.WATER:this.drawWaterProjectile(e);break;case l.FIRE:this.drawFireProjectile(e);break;case l.EARTH:this.drawEarthProjectile(e);break;case l.CRYSTAL:this.drawCrystalProjectile(e);break;case l.SHADOW:this.drawShadowProjectile(e);break;default:this.drawDefaultProjectile(e);break}}drawAirProjectile(e){e.fillStyle=this.color,e.strokeStyle=this.trailColor,e.lineWidth=1,e.beginPath(),e.ellipse(0,0,this.width/2,this.height/2,0,0,Math.PI*2),e.fill(),e.stroke(),e.strokeStyle="#FFFFFF",e.lineWidth=1,e.beginPath(),e.moveTo(-this.width/4,-2),e.lineTo(this.width/4,-2),e.moveTo(-this.width/4,2),e.lineTo(this.width/4,2),e.stroke()}drawWaterProjectile(e){e.fillStyle=this.color,e.strokeStyle=this.trailColor,e.lineWidth=1,e.beginPath(),e.moveTo(0,-this.height/2),e.quadraticCurveTo(this.width/2,0,0,this.height/2),e.quadraticCurveTo(-this.width/2,0,0,-this.height/2),e.fill(),e.stroke(),e.fillStyle="#FFFFFF",e.beginPath(),e.arc(-1,-2,1,0,Math.PI*2),e.fill()}drawFireProjectile(e){const t=Math.sin(this.age/100)*.2;e.fillStyle=this.color,e.beginPath(),e.moveTo(0,-this.height/2+t*2),e.quadraticCurveTo(this.width/2+t,0,0,this.height/2),e.quadraticCurveTo(-this.width/2-t,0,0,-this.height/2+t*2),e.fill(),e.fillStyle="#FFD700",e.beginPath(),e.ellipse(0,0,this.width/3,this.height/3,0,0,Math.PI*2),e.fill()}drawEarthProjectile(e){e.fillStyle=this.color,e.strokeStyle=this.trailColor,e.lineWidth=2,e.beginPath(),e.moveTo(0,-this.height/2),e.lineTo(this.width/3,-this.height/4),e.lineTo(this.width/2,this.height/4),e.lineTo(0,this.height/2),e.lineTo(-this.width/2,this.height/4),e.lineTo(-this.width/3,-this.height/4),e.closePath(),e.fill(),e.stroke()}drawCrystalProjectile(e){const t=.5+Math.sin(this.age/200)*.5,s=e.createRadialGradient(0,0,0,0,0,this.width);s.addColorStop(0,this.color+"80"),s.addColorStop(1,"transparent"),e.fillStyle=s,e.beginPath(),e.arc(0,0,this.width*t,0,Math.PI*2),e.fill(),e.fillStyle=this.color,e.strokeStyle="#FFFFFF",e.lineWidth=1,e.beginPath(),e.moveTo(0,-this.height/2),e.lineTo(this.width/4,-this.height/4),e.lineTo(this.width/2,0),e.lineTo(this.width/4,this.height/4),e.lineTo(0,this.height/2),e.lineTo(-this.width/4,this.height/4),e.lineTo(-this.width/2,0),e.lineTo(-this.width/4,-this.height/4),e.closePath(),e.fill(),e.stroke()}drawShadowProjectile(e){e.fillStyle=this.color,e.globalAlpha=.8,e.beginPath(),e.ellipse(0,0,this.width/2,this.height/2,0,0,Math.PI*2),e.fill();const t=Math.sin(this.age/150)*.3;e.strokeStyle=this.trailColor,e.lineWidth=1;for(let s=0;s<4;s++){const a=s/4*Math.PI*2,i=3+t*2,n=Math.cos(a)*this.width/3,r=Math.sin(a)*this.height/3;e.beginPath(),e.moveTo(n,r),e.lineTo(n+Math.cos(a)*i,r+Math.sin(a)*i),e.stroke()}e.globalAlpha=1}drawDefaultProjectile(e){this.drawValueBasedEnemyBullet(e)}drawValueBasedEnemyBullet(e){e.shadowColor="#000000",e.shadowBlur=3,e.fillStyle="#000000",e.beginPath(),e.ellipse(0,0,this.width/2+1,this.height/2+1,0,0,Math.PI*2),e.fill(),e.shadowBlur=0,e.fillStyle=this.color,e.beginPath(),e.ellipse(0,0,this.width/2,this.height/2,0,0,Math.PI*2),e.fill(),e.shadowColor=this.color,e.shadowBlur=8,e.fillStyle="#FFFFFF",e.beginPath(),e.ellipse(0,0,this.width/4,this.height/4,0,0,Math.PI*2),e.fill(),e.shadowColor=this.color,e.shadowBlur=6,e.fillStyle=this.color,e.globalAlpha=.9,e.beginPath(),e.ellipse(0,0,this.width/3,this.height/3,0,0,Math.PI*2),e.fill(),e.globalAlpha=1,e.shadowBlur=0}reset(){super.reset(),this.enemyType=l.AIR,this.pattern="straight",this.damage=15,this.waveData=null,this.arcData=null,this.homingData=null,this.phaseData=null}}class qe{constructor(e=E.CANVAS_WIDTH,t=E.CANVAS_HEIGHT,s=null,a=null){this.canvasWidth=e,this.canvasHeight=t,this.gameObjectManager=s,this.audioManager=a,this.activeEnemies=[],this.enemyPool=[],this.maxEnemies=50,this.activeBosses=[],this.bossPool=[],this.maxBosses=3,this.currentBoss=null,this.bossEncounterActive=!1,this.currentWave=0,this.waveInProgress=!1,this.waveStartTime=0,this.waveConfig=null,this.enemiesSpawnedInWave=0,this.enemiesKilledInWave=0,this.enemiesEscapedInWave=0,this.patternSpawnCounts={},this.bossEncounterCooldown=3e4,this.lastBossEncounterTime=0,this.bossDefeatCallback=null,this.bossWarpCallback=null,this.lastSpawnTime=0,this.spawnCooldown=1e3,this.spawnTimer=0,this.formations=[],this.formationSpawnQueue=[],this.nextDiveTime=2,this.diveAttackCooldown=2,this.divingEnemies=new Set,this.diveAttackTimer=0,this.currentEnvironment="space",this.environmentalEffects=null,this.totalEnemiesSpawned=0,this.totalEnemiesKilled=0,this.totalEnemiesEscaped=0,this.totalScore=0,this.collisionGrid=null,this.gridSize=64,this.gridWidth=Math.ceil(e/this.gridSize),this.gridHeight=Math.ceil(t/this.gridSize),this.projectileSystem=new Ke(e,t,s)}setBossWarpManager(e){this.bossWarpManager=e}setEnvironment(e){this.environmentalEffects=e;for(const t of this.activeEnemies)this.applyEnvironmentalEffects(t);for(const t of this.activeBosses)this.applyEnvironmentalEffects(t)}getLevelEnvironmentData(){return window.gameEngine&&window.gameEngine.levelManager&&window.gameEngine.levelManager.levelConfig&&window.gameEngine.levelManager.levelConfig.environmentData?window.gameEngine.levelManager.levelConfig.environmentData:null}update(e,t=null){this.spawnTimer+=e,this.updateWaveManagement(e),this.updateEnemySpawning(e,t),this.updateActiveEnemies(e,t),this.updateActiveBosses(e,t),this.cleanupDestroyedEnemies(),this.cleanupDestroyedBosses(),this.updateFormations(e),this.updateDiveAttacks(e,t),this.projectileSystem&&this.projectileSystem.update(e,t),this.updateEnemyAttacks(e,t),this.checkWaveCompletion(),this.checkBossEncounter()}updateWaveManagement(e){!this.waveInProgress&&this.activeEnemies.length===0&&this.startNextWave(),this.waveInProgress&&(this.waveStartTime+=e)}updateEnemySpawning(e,t){!this.waveInProgress||!this.waveConfig||this.enemiesSpawnedInWave===0&&this.spawnEntireWaveFormation()}spawnEntireWaveFormation(){var i;if(!this.waveConfig)return;const e=this.getOrCreateFormation("space_invaders_grid"),t=this.waveConfig.gridCols||8,s=this.waveConfig.gridRows||3,a=((i=this.waveConfig.spawnPatterns[0])==null?void 0:i.rowTypes)||[];for(let n=0;n<this.waveConfig.totalEnemies;n++){const r=Math.floor(n/t),o=n%t;let c;a.length>r?c=a[r]:c=this.selectEnemyType(this.waveConfig.enemyTypes);const h=60,d=50,g=(t-1)*h,v=e.center.x-g/2+o*h,p=e.center.y+r*d,f=this.spawnEnemy(v,p,c);if(f){const y=this.calculateFormationOffset("space_invaders_grid",n);f.setMovementPattern("formation"),f.setFormationTarget(e.center,y),e.enemies.push(f),f.gridRow=r,f.gridCol=o,this.enemiesSpawnedInWave++}}this.createFormationSpawnEffect(e.center,t,s)}updateActiveEnemies(e,t){for(let s=this.activeEnemies.length-1;s>=0;s--){const a=this.activeEnemies[s];a.active&&a.update(e,t)}}updateActiveBosses(e,t){for(let s=this.activeBosses.length-1;s>=0;s--){const a=this.activeBosses[s];a.active&&(a.update(e,t),this.checkBossWarpTriggers(a))}}cleanupDestroyedEnemies(){for(let e=this.activeEnemies.length-1;e>=0;e--){const t=this.activeEnemies[e];(t.destroyed||!t.active)&&(t.isDestroyed?(this.enemiesKilledInWave++,this.totalEnemiesKilled++,this.totalScore+=t.scoreValue):t.destroyed&&(this.enemiesEscapedInWave++,this.totalEnemiesEscaped++,this.onEnemyEscaped(t)),this.returnEnemyToPool(t),this.activeEnemies.splice(e,1))}}cleanupDestroyedBosses(){for(let e=this.activeBosses.length-1;e>=0;e--){const t=this.activeBosses[e];(t.destroyed||!t.active)&&(t.isDestroyed?(this.totalEnemiesKilled++,this.totalScore+=t.scoreValue,console.log(`Boss "${t.bossName}" defeated! Score: ${t.scoreValue}`),this.bossDefeatCallback&&this.bossDefeatCallback(t),this.endBossEncounter()):t.destroyed&&(console.log(`Boss "${t.bossName}" escaped`),this.endBossEncounter()),this.returnBossToPool(t),this.activeBosses.splice(e,1),this.currentBoss===t&&(this.currentBoss=null))}}startNextWave(){if(this.currentWave++,this.waveInProgress=!0,this.waveStartTime=0,this.enemiesSpawnedInWave=0,this.enemiesKilledInWave=0,this.enemiesEscapedInWave=0,this.patternSpawnCounts={},this.waveConfig=this.generateWaveConfig(this.currentWave),this.waveConfig.spawnPatterns)for(let e=0;e<this.waveConfig.spawnPatterns.length;e++)this.patternSpawnCounts[e]=0;this.spawnCooldown=Math.max(200,1500-this.currentWave*50),console.log(`Starting wave ${this.currentWave}:`,this.waveConfig)}generateWaveConfig(e){var c,h,d;const t=Math.min(10,6+e),s=Math.min(5,3+Math.floor(e/2));let a=t*s;const i=(d=(h=(c=window.gameEngine)==null?void 0:c.levelManager)==null?void 0:h.levelConfig)==null?void 0:d.environmentData,n=i==null?void 0:i.gameplayModifiers;n&&n.enemyTypeModifiers&&(a=this.applyEnemyTypeModifiersToSpawnCount(a,n.enemyTypeModifiers,e));const r=this.getSpaceInvadersEnemyTypes(e),o=this.getSpaceInvadersWavePattern(e,a,t,s);return{waveNumber:e,totalEnemies:a,gridCols:t,gridRows:s,enemyTypes:r,spawnPatterns:o,difficulty:Math.min(10,Math.floor(e/3)+1),hasFormation:!0,hasBoss:e%8===0,formationSpeed:40+e*6,descentAmount:25+e*2,fireRate:Math.max(.6,2-e*.1),diveFrequency:Math.max(.8,2.5-e*.15)}}applyEnemyTypeModifiersToSpawnCount(e,t,s){const a=this.getSpaceInvadersEnemyTypes(s);let i=0,n=0;for(const c of a){let h=1;const d=t[c.type];typeof d=="object"&&d!==null?h=d.value||d.modifier||d.spawnModifier||1:typeof d=="number"&&(h=d);const g=1+(h-1)*1.5;i+=c.weight,n+=c.weight*g}const r=i>0?n/i:1,o=Math.round(e*r);return console.log(`Applied enemy type modifiers to spawn count: ${e} -> ${o} (avg modifier: ${r.toFixed(2)})`),Math.max(1,o)}getSpaceInvadersEnemyTypes(e){const t=[];return e===1?(t.push({type:l.EARTH,weight:.3,row:0}),t.push({type:l.FIRE,weight:.4,row:1}),t.push({type:l.AIR,weight:.3,row:2})):e===2?(t.push({type:l.EARTH,weight:.25,row:0}),t.push({type:l.FIRE,weight:.3,row:1}),t.push({type:l.CRYSTAL,weight:.2,row:1}),t.push({type:l.AIR,weight:.15,row:2}),t.push({type:l.SHADOW,weight:.1,row:2})):e>=3&&e<=5?(t.push({type:l.EARTH,weight:.2,row:0}),t.push({type:l.FIRE,weight:.25,row:1}),t.push({type:l.CRYSTAL,weight:.2,row:1}),t.push({type:l.AIR,weight:.15,row:2}),t.push({type:l.WATER,weight:.1,row:2}),t.push({type:l.SHADOW,weight:.1,row:2})):(t.push({type:l.EARTH,weight:.15,row:0}),t.push({type:l.FIRE,weight:.2,row:1}),t.push({type:l.CRYSTAL,weight:.2,row:1}),t.push({type:l.AIR,weight:.15,row:2}),t.push({type:l.WATER,weight:.15,row:2}),t.push({type:l.SHADOW,weight:.15,row:2})),e>=6&&t.filter(a=>a.type===l.CRYSTAL||a.type===l.SHADOW).forEach(a=>{a.weight+=.02}),e>=10&&(t.forEach(i=>{i.weight=i.weight*.98}),t.filter(i=>i.type===l.SHADOW||i.type===l.CRYSTAL).forEach(i=>{i.weight+=.01})),t}getSpaceInvadersWavePattern(e,t,s,a){const i=[];return i.push({formation:"space_invaders_grid",enemyCount:t,spawnDelay:0,gridCols:s,gridRows:a,rowTypes:this.getRowTypeAssignment(e,a)}),i}getRowTypeAssignment(e,t){const s=[];for(let a=0;a<t;a++)if(a===0)s.push(l.EARTH);else if(a===1||a%2===1&&e>=3){const i=e%2===0?l.FIRE:l.CRYSTAL;s.push(i)}else{const i=[l.AIR,l.WATER];e>=2&&i.push(l.SHADOW),s.push(i[a%i.length])}return s}getWaveEnemyTypes(e){const t=[];if(e<=3)e===1?(t.push({type:l.WATER,weight:.6}),t.push({type:l.AIR,weight:.4})):e===2?(t.push({type:l.WATER,weight:.4}),t.push({type:l.AIR,weight:.3}),t.push({type:l.EARTH,weight:.3})):(t.push({type:l.WATER,weight:.3}),t.push({type:l.AIR,weight:.3}),t.push({type:l.EARTH,weight:.2}),t.push({type:l.FIRE,weight:.2}));else if(e<=7)e===4?(t.push({type:l.EARTH,weight:.4}),t.push({type:l.WATER,weight:.3}),t.push({type:l.FIRE,weight:.3})):e===5?(t.push({type:l.CRYSTAL,weight:.3}),t.push({type:l.WATER,weight:.3}),t.push({type:l.AIR,weight:.2}),t.push({type:l.EARTH,weight:.2})):e===6?(t.push({type:l.FIRE,weight:.4}),t.push({type:l.AIR,weight:.3}),t.push({type:l.SHADOW,weight:.2}),t.push({type:l.WATER,weight:.1})):(t.push({type:l.WATER,weight:.25}),t.push({type:l.FIRE,weight:.25}),t.push({type:l.AIR,weight:.2}),t.push({type:l.EARTH,weight:.2}),t.push({type:l.CRYSTAL,weight:.1}));else if(e===8)t.push({type:l.EARTH,weight:.4}),t.push({type:l.CRYSTAL,weight:.3}),t.push({type:l.WATER,weight:.2}),t.push({type:l.FIRE,weight:.1});else if(e===9)t.push({type:l.SHADOW,weight:.4}),t.push({type:l.FIRE,weight:.3}),t.push({type:l.AIR,weight:.2}),t.push({type:l.CRYSTAL,weight:.1});else if(e===10)t.push({type:l.WATER,weight:.2}),t.push({type:l.FIRE,weight:.2}),t.push({type:l.AIR,weight:.2}),t.push({type:l.EARTH,weight:.15}),t.push({type:l.CRYSTAL,weight:.15}),t.push({type:l.SHADOW,weight:.1});else{const s=Math.min(.3,.1+(e-10)*.02),a=Math.min(.25,.15+(e-10)*.015),i=1-s-a;t.push({type:l.SHADOW,weight:s}),t.push({type:l.CRYSTAL,weight:a}),t.push({type:l.WATER,weight:i*.3}),t.push({type:l.FIRE,weight:i*.3}),t.push({type:l.AIR,weight:i*.2}),t.push({type:l.EARTH,weight:i*.2})}return t}generateSpawnPatterns(e,t){return this.getPredefinedWavePattern(e,t)}getPredefinedWavePattern(e,t){const s=[];return s.push({type:"formation",count:t,formation:"space_invaders_grid",movementPattern:"space_invaders",spacing:800}),s}spawnEnemyFromWave(){if(!this.waveConfig||this.enemiesSpawnedInWave>=this.waveConfig.totalEnemies)return;if(this.waveConfig.hasBoss&&!this.bossEncounterActive&&this.enemiesSpawnedInWave===Math.floor(this.waveConfig.totalEnemies*.7)){this.spawnBossFromWave();return}const e=this.selectEnemyType(this.waveConfig.enemyTypes),s=this.selectSpawnPattern(this.waveConfig.spawnPatterns).pattern,a=this.generateSpawnPosition(s),i=this.spawnEnemy(a.x,a.y,e);if(i){const n=this.getTypeSpecificMovementPattern(e),r={amplitude:C.random(30,80),frequency:C.random(1,3)};s.predefinedPattern&&(r.predefinedPattern=s.predefinedPattern),s.type!=="formation"?i.setMovementPattern(n.pattern,n.options):i.setMovementPattern(s.movementPattern,r),s.type==="formation"&&this.addEnemyToFormation(i,s),this.enemiesSpawnedInWave++}}spawnBossFromWave(){if(!this.waveConfig||this.bossEncounterActive)return;const e=this.waveConfig.enemyTypes,t=e[Math.floor(Math.random()*e.length)].type,s=new m(this.canvasWidth/2,100),a=this.spawnBoss(s.x,s.y,t);a&&(a.onWarpInitiated=i=>this.handleBossWarpInitiated(i),a.onWarpCompleted=i=>this.handleBossWarpCompleted(i),a.onAbilityUsed=(i,n)=>this.handleBossAbilityUsed(i,n),a.onDamageTaken=(i,n)=>this.handleBossDamageTaken(i,n),this.enemiesSpawnedInWave++,console.log(`Boss "${a.bossName}" spawned for wave ${this.currentWave}`))}selectEnemyType(e){const t=Math.random();let s=0;for(const a of e)if(s+=a.weight,t<=s)return a.type;return e[0].type}selectSpawnPattern(e){const t=[];for(let a=0;a<e.length;a++){const i=e[a];(this.patternSpawnCounts[a]||0)<i.count&&t.push({pattern:i,index:a})}if(t.length===0)return console.warn("All spawn patterns exhausted but still trying to spawn enemies"),{pattern:e[0],index:0};const s=t[Math.floor(Math.random()*t.length)];return this.patternSpawnCounts[s.index]++,s}generateSpawnPosition(e){switch(e.type){case"linear":return new m(C.random(50,this.canvasWidth-50),-30);case"formation":const t=this.getOrCreateFormation(e.formation);return new m(t.center.x,t.center.y-100);case"scattered":return new m(C.random(30,this.canvasWidth-30),C.random(-50,-20));case"dive":const s=Math.random()<.5?"left":"right";return new m(s==="left"?-30:this.canvasWidth+30,C.random(50,150));default:return new m(C.random(50,this.canvasWidth-50),-30)}}getCurrentLevel(){return window.gameEngine&&window.gameEngine.levelManager?window.gameEngine.levelManager.currentLevel:1}spawnEnemy(e,t,s=l.AIR){const a=this.getCurrentLevel();let i=this.getEnemyFromPool();return i?(i.type=s,i.level=a,i.reset(),i.position.set(e,t)):i=new de(e,t,s,a),i.canvasWidth=this.canvasWidth,i.canvasHeight=this.canvasHeight,i.enemyManager=this,i.addTag("enemy"),this.activeEnemies.push(i),this.totalEnemiesSpawned++,this.gameObjectManager&&this.gameObjectManager.add(i),i}applyLevelScaling(e,t){console.warn("applyLevelScaling is deprecated. Scaling is now handled in Enemy constructor."),e.scalingInfo||(e.scalingInfo={level:t,tier:e.tier,coefficient:e.difficultyCoefficient,isElite:e.isElite,message:"Scaling handled by constructor"})}spawnBoss(e,t,s=l.SHADOW){let a=this.getBossFromPool();return a?(a.reset(),a.position.set(e,t),a.type=s):a=new ze(e,t,s),this.activeBosses.push(a),this.currentBoss=a,this.bossEncounterActive=!0,this.lastBossEncounterTime=Date.now(),this.gameObjectManager&&this.gameObjectManager.add(a),console.log(`Spawned ${s} boss "${a.bossName}" at (${e}, ${t}). Active bosses: ${this.activeBosses.length}`),a}getEnemyFromPool(){return this.enemyPool.length>0?this.enemyPool.pop():null}getBossFromPool(){return this.bossPool.length>0?this.bossPool.pop():null}returnEnemyToPool(e){this.enemyPool.length<this.maxEnemies&&(e.reset(),this.enemyPool.push(e)),this.gameObjectManager&&this.gameObjectManager.remove(e)}returnBossToPool(e){this.bossPool.length<this.maxBosses&&(e.reset(),this.bossPool.push(e)),this.gameObjectManager&&this.gameObjectManager.remove(e)}addEnemyToFormation(e,t){const s=this.getOrCreateFormation(t.formation),a=s.enemies.length,i=this.calculateFormationOffset(t.formation,a);e.setMovementPattern("formation"),e.setFormationTarget(s.center,i),s.enemies.push(e),console.log(`Added enemy to ${t.formation} formation. Formation size: ${s.enemies.length}`)}getOrCreateFormation(e){const t=e==="space_invaders_grid"?40:8;let s=this.formations.find(a=>a.type===e&&a.enemies.length<t);return s||(s={type:e,center:new m(this.canvasWidth/2,150),enemies:[],movementTimer:0},this.formations.push(s)),s}calculateFormationOffset(e,t){switch(e){case"space_invaders_grid":const a=Math.floor(t/8),i=t%8,n=60,r=50,o=-420/2;return new m(o+i*n,a*r);case"line":return new m((t-3.5)*50,0);case"v-formation":const c=t%2===0?-1:1,h=Math.floor(t/2);return new m(c*(h+1)*50,h*40);case"triangle":const d=Math.floor((-1+Math.sqrt(1+8*t))/2),g=t-d*(d+1)/2,w=d+1;return new m((g-w/2+.5)*50,d*40);case"diamond":const v=3;if(t<v)return new m((t-1)*60,-40);if(t<v*2-1){const S=t-v;return new m((S-1)*80,0)}else{const S=t-(v*2-1);return new m((S-1)*60,40)}case"circle":const p=t/8*Math.PI*2,f=80;return new m(Math.cos(p)*f,Math.sin(p)*f);case"wedge":const y=Math.floor(t/3),M=t%3;return new m((M-1)*(50+y*15),y*40);default:return new m((t-3.5)*50,0)}}updateFormations(e){var t,s;for(let a=this.formations.length-1;a>=0;a--){const i=this.formations[a];if(i.movementTimer+=e/1e3,i.enemies=i.enemies.filter(n=>n.active&&!n.destroyed),i.enemies.length===0){this.formations.splice(a,1);continue}i.direction||(i.direction=1,i.moveSpeed=((t=this.waveConfig)==null?void 0:t.formationSpeed)||40+this.currentWave*6,i.descentDistance=0,i.maxDescentDistance=40,i.horizontalDistance=0,i.maxHorizontalDistance=150,i.bobTimer=0,i.lastDirectionChange=0,i.descentAmount=((s=this.waveConfig)==null?void 0:s.descentAmount)||25+this.currentWave*2),this.updateSpaceInvadersFormation(i,e);for(let n=0;n<i.enemies.length;n++){const r=i.enemies[n];if(r.active&&r.setFormationTarget){const o=this.calculateFormationOffset(i.type,n),c=this.calculateBobbingOffset(i,r,n),h=o.add(c);r.setFormationTarget(i.center,h),r.formationState="formation",r.formationIndex=n}}}}updateSpaceInvadersFormation(e,t){const s=t/1e3;e.bobTimer+=s;const a=this.calculateFormationBounds(e),i=50,n=a.left<=i,r=a.right>=this.canvasWidth-i;if(e.center.x+=e.direction*e.moveSpeed*s,e.horizontalDistance+=Math.abs(e.direction*e.moveSpeed*s),n||r||e.horizontalDistance>=e.maxHorizontalDistance){e.direction*=-1,e.horizontalDistance=0,e.lastDirectionChange=e.movementTimer,this.playFormationDirectionChangeSound();const o=e.descentAmount||25+this.currentWave*2;e.center.y+=o,e.descentDistance+=o,e.center.x=C.clamp(e.center.x,i+100,this.canvasWidth-i-100),e.moveSpeed*=1.02}e.center.x=C.clamp(e.center.x,i+100,this.canvasWidth-i-100)}calculateFormationBounds(e){if(e.enemies.length===0)return{left:e.center.x,right:e.center.x,top:e.center.y,bottom:e.center.y};let t=1/0,s=-1/0,a=1/0,i=-1/0;for(let n=0;n<e.enemies.length;n++){const r=this.calculateFormationOffset(e.type,n),o=e.center.x+r.x,c=e.center.y+r.y;t=Math.min(t,o-20),s=Math.max(s,o+20),a=Math.min(a,c-15),i=Math.max(i,c+15)}return{left:t,right:s,top:a,bottom:i}}calculateBobbingOffset(e,t,s){const a=s*.5+e.bobTimer*2,i=4+s%3*2,n=Math.sin(a)*i;return new m(0,n)}updateDiveAttacks(e,t){if(!t)return;const s=e/1e3;if(this.nextDiveTime-=s,this.nextDiveTime<=0){const a=this.activeEnemies.filter(n=>n.state==="formation"),i=a[Math.floor(Math.random()*a.length)];i&&(i.state="dive",i.baseY=i.y||i.position.y,i.vy=220,i.vx=t.x-i.x>0?80:-80,this.divingEnemies.add(i)),this.nextDiveTime=2+Math.max(.8,4-this.currentWave*.2)*Math.random()}this.updateDivingEnemiesSimple(e,t)}triggerDiveAttack(e){const t=[];for(const s of this.activeEnemies)s.active&&!s.destroyed&&s.formationState==="formation"&&!this.divingEnemies.has(s)&&t.push(s);if(t.length>0){const s=t[Math.floor(Math.random()*t.length)];this.startEnemyDive(s,e)}}startEnemyDive(e,t){e.state="dive",e.baseY=e.position.y,e.vx=t.x-e.position.x>0?80:-80,e.vy=220,e.originalFormationCenter=e.formationCenter?e.formationCenter.clone():null,e.originalFormationOffset=e.formationOffset?e.formationOffset.clone():null,this.divingEnemies.add(e),this.createDiveAttackEffect(e.position),this.playDiveAttackSound()}updateDivingEnemiesSimple(e,t){const s=e/1e3,a=[],i=800,n=600;for(const r of this.divingEnemies){if(!r.active||r.destroyed){a.push(r);continue}if(r.state==="dive"){const o=t.x-r.x,c=t.y-r.y,h=Math.hypot(o,c)||1;r.vx+=o/h*180*s,r.vy+=c/h*180*s,r.x+=r.vx*s,r.y+=r.vy*s,r.position&&(r.position.x=r.x,r.position.y=r.y),(r.y>n+40||r.x<-40||r.x>i+40)&&(r.state="formation",r.baseY=80+Math.random()*60,r.y=r.baseY,r.vx=40+this.currentWave*6,r.vy=0,r.position&&(r.position.y=r.y),a.push(r))}}for(const r of a)this.divingEnemies.delete(r)}updateDivingEnemies(e,t){this.updateDivingEnemiesSimple(e,t)}returnEnemyToFormation(e){if(this.divingEnemies.delete(e),e.state="formation",e.position.y>this.canvasHeight+40||e.position.x<-40||e.position.x>this.canvasWidth+40){if(e.originalFormationCenter&&e.originalFormationOffset){const t=80+Math.random()*60,s=e.originalFormationCenter.x+e.originalFormationOffset.x;e.position=new m(s,t)}else e.position=new m(C.random(100,this.canvasWidth-100),80+Math.random()*60);e.velocity=new m(40+this.currentWave*6,0)}e.setMovementPattern("formation"),this.createReturnToFormationEffect(e.position)}createFormationSpawnEffect(e,t,s){if(this.effectsManager){const n=(t-1)*60,r=e.x-n/2;for(let o=0;o<s;o++)for(let c=0;c<t;c++){const h=r+c*60,d=e.y+o*50;this.effectsManager.createEffect("spawn_flash",new m(h,d),{color:"#00FFFF",duration:500,size:20})}}this.playFormationSpawnSound()}createDiveAttackEffect(e){this.effectsManager&&(this.effectsManager.createEffect("warning_flash",e.clone(),{color:"#FF4444",duration:300,size:30,pulseRate:10}),this.effectsManager.createEffect("dive_trail",e.clone(),{color:"#FFAA00",duration:2e3,followTarget:!0}))}createReturnToFormationEffect(e){this.effectsManager&&this.effectsManager.createEffect("return_flash",e.clone(),{color:"#00FF00",duration:400,size:15})}playFormationSpawnSound(){this.audioManager&&this.audioManager.playSound("formation_spawn",{volume:.6,pitch:1})}playDiveAttackSound(){this.audioManager&&this.audioManager.playSound("dive_attack",{volume:.7,pitch:1.2})}playFormationDirectionChangeSound(){this.audioManager&&this.audioManager.playSound("formation_turn",{volume:.5,pitch:.8+this.currentWave*.1})}processFormationSpawnQueue(e){}checkWaveCompletion(){if(this.waveInProgress&&this.waveConfig){const e=this.enemiesKilledInWave+this.enemiesEscapedInWave,t=this.enemiesSpawnedInWave>=this.waveConfig.totalEnemies,s=e>=this.waveConfig.totalEnemies,a=this.activeEnemies.length===0;t&&(s||a)&&this.completeWave()}}completeWave(){this.waveInProgress=!1,console.log(`Wave ${this.currentWave} completed! Enemies killed: ${this.enemiesKilledInWave}/${this.waveConfig.totalEnemies}, Enemies escaped: ${this.enemiesEscapedInWave}/${this.waveConfig.totalEnemies}`);const e=this.calculateWaveBonus();this.totalScore+=e,this.formations=[],this.onWaveComplete(this.currentWave,e)}calculateWaveBonus(){const t=this.currentWave,s=this.enemiesKilledInWave/this.waveConfig.totalEnemies;return Math.floor(100*t*s)}onWaveComplete(e,t){console.log(`Wave ${e} complete with bonus: ${t}`)}onEnemyEscaped(e){console.log(`Enemy ${e.id} escaped`)}render(e,t=0){for(const s of this.activeEnemies)s.visible&&s.render(e,t);for(const s of this.activeBosses)s.visible&&s.render(e,t);this.projectileSystem&&this.projectileSystem.render(e,t),window.DEBUG_MODE&&this.renderDebugInfo(e)}renderDebugInfo(e){var s;e.fillStyle="#FFFFFF",e.font="12px Arial",e.textAlign="left";const t=[`Wave: ${this.currentWave}`,`Active Enemies: ${this.activeEnemies.length}`,`Active Bosses: ${this.activeBosses.length}`,`Boss Encounter: ${this.bossEncounterActive?"Active":"Inactive"}`,`Spawned: ${this.enemiesSpawnedInWave}/${((s=this.waveConfig)==null?void 0:s.totalEnemies)||0}`,`Killed: ${this.enemiesKilledInWave}`,`Total Score: ${this.totalScore}`,`Formations: ${this.formations.length}`];for(let a=0;a<t.length;a++)e.fillText(t[a],10,20+a*15);e.strokeStyle="#FFFF00",e.lineWidth=2;for(const a of this.formations)e.beginPath(),e.arc(a.center.x,a.center.y,5,0,Math.PI*2),e.stroke();e.strokeStyle="#FF00FF",e.lineWidth=3;for(const a of this.activeBosses)e.beginPath(),e.arc(a.position.x,a.position.y,a.collisionRadius+10,0,Math.PI*2),e.stroke()}checkPlayerCollisions(e){const t=[];if(!e.active||e.isInvulnerable)return t;for(const s of this.activeEnemies)s.active&&!s.isDestroyed&&s.collidesWith(e)&&t.push(s);return t}checkProjectileCollisions(e){const t=[];for(const s of e)if(!(!s.active||s.hasTag("enemyProjectile"))){for(const a of this.activeEnemies)if(a.active&&!a.isDestroyed&&a.collidesWith(s)){let i=s.damage||25;s.calculateEffectiveDamage&&a.type&&(i=s.calculateEffectiveDamage(a.type)),t.push({projectile:s,enemy:a,damage:i});break}}return t}handlePlayerEnemyCollision(e,t){const s=e.takeDamage(t.damage);return t.takeDamage(t.health),console.log(`Player-Enemy collision: Player took ${s.damageTaken} damage from ${t.type} enemy, Enemy destroyed`),{playerDamage:s.damageTaken,enemyDestroyed:!0,scoreGained:t.scoreValue}}handleProjectileEnemyCollision(e,t,s){const a=t.takeDamage(s);e.destroy();let i=null;return a.destroyed&&(i=this.shouldDropPowerUp()),{enemyDestroyed:a.destroyed,scoreGained:a.scoreValue,damageDealt:a.damageTaken,powerUpDropType:i,enemyPosition:a.destroyed?t.position.clone():null}}shouldDropPowerUp(){if(Math.random()<.1){const e=["RAPID_FIRE","SHIELD"],t=Math.floor(Math.random()*e.length);return e[t]}return null}applyEnvironmentalEffects(e){if(!this.environmentalEffects||typeof this.environmentalEffects.applyEnvironmentEffects!="function")return;const t={type:e.type,speed:e.speed,health:e.maxHealth,projectileSpeed:1},s=this.environmentalEffects.applyEnvironmentEffects(t);e.currentSpeed=s.speed}applyEnvironmentalEffectsFromLevelConfig(e){var r;if(!window.gameEngine||!window.gameEngine.levelManager||!window.gameEngine.levelManager.levelConfig||!window.gameEngine.levelManager.levelConfig.environmentData)return;const s=window.gameEngine.levelManager.levelConfig.environmentData.gameplayModifiers;if(!s)return;const a=e.type,n=1+((((r=s.enemyTypeModifiers)==null?void 0:r[a])??1)-1)*1.5;e.currentSpeed=e.speed*n}calculateEnvironmentalEffectiveness(e,t){const s=this.environmentalEffects[t];return s&&s[e]||1}setEnvironment(e){this.currentEnvironment=e.getEnvironmentType(),this.environmentalEffects=e;for(const t of this.activeEnemies)this.applyEnvironmentalEffects(t);console.log(`Environment changed to: ${this.currentEnvironment}`)}getTypeSpecificMovementPattern(e){switch(e){case l.WATER:return{pattern:"sine",options:{amplitude:60,frequency:1.5,changeInterval:3e3}};case l.FIRE:return{pattern:"player_follow",options:{aggression:.7,dashCooldown:4e3,dashDuration:800}};case l.AIR:return{pattern:"sine",options:{amplitude:100,frequency:2.5,evasionMode:!0}};case l.EARTH:return{pattern:"straight",options:{pauseInterval:5e3,pauseDuration:1500,slowSteady:!0}};case l.CRYSTAL:return{pattern:"sine",options:{amplitude:80,frequency:1,geometric:!0,pulseInterval:6e3}};case l.SHADOW:return{pattern:"player_follow",options:{stealthMode:!0,teleportInterval:7e3,indirectApproach:!0}};default:return{pattern:"straight",options:{}}}}getDefaultEnvironmentalEffects(){return{water:{[l.WATER]:1.5,[l.FIRE]:.5,[l.AIR]:1,[l.EARTH]:1.8,[l.CRYSTAL]:1.2,[l.SHADOW]:.9},fire:{[l.WATER]:1.8,[l.FIRE]:1.5,[l.AIR]:1.8,[l.EARTH]:1,[l.CRYSTAL]:1.2,[l.SHADOW]:.8},air:{[l.WATER]:1,[l.FIRE]:1.8,[l.AIR]:1.5,[l.EARTH]:1.8,[l.CRYSTAL]:1.2,[l.SHADOW]:.9},earth:{[l.WATER]:1.8,[l.FIRE]:1,[l.AIR]:1.8,[l.EARTH]:1.5,[l.CRYSTAL]:1.2,[l.SHADOW]:.8},crystal:{[l.WATER]:1.1,[l.FIRE]:1.1,[l.AIR]:1.1,[l.EARTH]:1.1,[l.CRYSTAL]:1.8,[l.SHADOW]:1},shadow:{[l.WATER]:1.1,[l.FIRE]:1.1,[l.AIR]:1.1,[l.EARTH]:1.1,[l.CRYSTAL]:1.8,[l.SHADOW]:1.5}}}updateEnemyAttacks(e,t){if(t)for(const s of this.activeEnemies)s.active&&!s.isDestroyed&&this.updateEnemyFiring(s,e,t)}updateEnemyFiring(e,t,s){e.fireCooldown===void 0&&(e.fireCooldown=this.getEnemyFireCooldown(e.type),e.lastFireTime=0),e.fireCooldown-=t,this.canEnemyFire(e,s)&&e.fireCooldown<=0&&(this.fireEnemyProjectile(e,s),e.fireCooldown=this.getEnemyFireCooldown(e.type)+Math.random()*500)}canEnemyFire(e,t){const s=Date.now();if(e.empDisabled&&e.empDisableEndTime&&s<e.empDisableEndTime||(e.empDisabled&&e.empDisableEndTime&&s>=e.empDisableEndTime&&(e.empDisabled=!1,e.empDisableEndTime=null),e.timeDilationDisabled&&e.timeDilationEndTime&&s<e.timeDilationEndTime))return!1;e.timeDilationDisabled&&e.timeDilationEndTime&&s>=e.timeDilationEndTime&&(e.timeDilationDisabled=!1,e.timeDilationEndTime=null);const a=this.getEnemyFiringChance(e.type);if(Math.random()>a||e.formationState==="dive"||e.position.x<50||e.position.x>this.canvasWidth-50)return!1;const i=t.y>e.position.y,r=Math.abs(t.x-e.position.x)<200;return i&&r}fireEnemyProjectile(e,t){switch(e.type){case l.FIRE:this.fireFireEnemyPattern(e,t);break;case l.WATER:this.fireWaterEnemyPattern(e,t);break;case l.AIR:this.fireAirEnemyPattern(e,t);break;case l.EARTH:this.fireEarthEnemyPattern(e,t);break;case l.CRYSTAL:this.fireCrystalEnemyPattern(e,t);break;case l.SHADOW:this.fireShadowEnemyPattern(e,t);break;default:this.fireDefaultEnemyPattern(e,t);break}}fireEnemySpreadShot(e,t){if(!this.projectileSystem)return;const s=e.directionTo(t),a=Math.PI/8,i=s.rotate(-a),n=s.rotate(a);this.projectileSystem.fireProjectile(e.position.clone(),i,e.type,{damage:this.getEnemyProjectileDamage(e.type)*.8}),this.projectileSystem.fireProjectile(e.position.clone(),n,e.type,{damage:this.getEnemyProjectileDamage(e.type)*.8})}fireFireEnemyPattern(e,t){if(!this.projectileSystem)return;const s=e.directionTo(t),a=Math.random()<.3?3:2,i=Math.PI/12;for(let n=0;n<a;n++){const r=(n-(a-1)/2)*i,o=s.rotate(r);setTimeout(()=>{this.projectileSystem.fireProjectile(e.position.clone(),o,e.type,{damage:this.getEnemyProjectileDamage(e.type)})},n*100)}}fireWaterEnemyPattern(e,t){if(!this.projectileSystem)return;const s=e.directionTo(t),a=3,i=Math.PI/6;for(let n=0;n<a;n++){const o=(n/(a-1)-.5)*i,c=s.rotate(o);setTimeout(()=>{this.projectileSystem.fireProjectile(e.position.clone(),c,e.type,{damage:this.getEnemyProjectileDamage(e.type)})},n*150)}}fireAirEnemyPattern(e,t){if(!this.projectileSystem)return;const s=e.directionTo(t),a=(Math.random()-.5)*.6,i=new m(s.x+a,s.y+a*.3).normalize();this.projectileSystem.fireProjectile(e.position.clone(),i,e.type,{damage:this.getEnemyProjectileDamage(e.type)})}fireEarthEnemyPattern(e,t){if(!this.projectileSystem)return;const a=e.directionTo(t);this.projectileSystem.fireProjectile(e.position.clone(),a,e.type,{damage:this.getEnemyProjectileDamage(e.type)})}fireCrystalEnemyPattern(e,t){if(!this.projectileSystem)return;const s=e.directionTo(t);Math.random()<.4?[-Math.PI/8,0,Math.PI/8].forEach((i,n)=>{const r=s.rotate(i);setTimeout(()=>{this.projectileSystem.fireProjectile(e.position.clone(),r,e.type,{damage:this.getEnemyProjectileDamage(e.type)})},n*80)}):this.projectileSystem.fireProjectile(e.position.clone(),s,e.type,{damage:this.getEnemyProjectileDamage(e.type)})}fireShadowEnemyPattern(e,t){if(!this.projectileSystem)return;const s=e.directionTo(t),a=(Math.random()-.5)*.3,i=new m(s.x+a,s.y+a*.5).normalize(),n=Math.random()*200;setTimeout(()=>{this.projectileSystem.fireProjectile(e.position.clone(),i,e.type,{damage:this.getEnemyProjectileDamage(e.type)})},n)}fireDefaultEnemyPattern(e,t){if(!this.projectileSystem)return;const s=e.directionTo(t),a=(Math.random()-.5)*.4,i=new m(s.x+a,s.y+a*.5).normalize();this.projectileSystem.fireProjectile(e.position.clone(),i,e.type,{damage:this.getEnemyProjectileDamage(e.type)})}getEnemyFiringChance(e){return{[l.FIRE]:.8,[l.AIR]:.7,[l.CRYSTAL]:.6,[l.SHADOW]:.5,[l.WATER]:.4,[l.EARTH]:.3}[e]||.5}getEnemyFireCooldown(e){const s={[l.FIRE]:1200,[l.AIR]:1e3,[l.CRYSTAL]:1800,[l.SHADOW]:1500,[l.WATER]:2200,[l.EARTH]:2800}[e]||2e3,a=Math.min(400,this.currentWave*50);return Math.max(600,s-a)}getEnemyProjectileDamage(e){return{[l.FIRE]:15,[l.AIR]:8,[l.CRYSTAL]:20,[l.SHADOW]:18,[l.WATER]:10,[l.EARTH]:25}[e]||12}getEnemiesInAttackRange(e){return this.activeEnemies.filter(t=>t.canAttackPlayer(e))}checkEnemyProjectileCollisions(e){return this.projectileSystem.checkPlayerCollisions(e)}handleEnemyProjectileCollision(e,t){return this.projectileSystem.handlePlayerCollision(e,t)}triggerEnemyAttacks(e){const t=[],s=this.getEnemiesInAttackRange(e);for(const a of s){const i=a.attack(e);i&&t.push(i)}return t}getWaveStatus(){var e;return{currentWave:this.currentWave,waveInProgress:this.waveInProgress,enemiesSpawned:this.enemiesSpawnedInWave,enemiesKilled:this.enemiesKilledInWave,enemiesEscaped:this.enemiesEscapedInWave,totalEnemies:((e=this.waveConfig)==null?void 0:e.totalEnemies)||0,activeEnemies:this.activeEnemies.length,totalScore:this.totalScore,waveProgress:this.waveConfig?this.enemiesSpawnedInWave/this.waveConfig.totalEnemies:0}}reset(){for(const e of this.activeEnemies)e.destroy();this.activeEnemies=[];for(const e of this.activeBosses)e.destroy();this.activeBosses=[],this.currentBoss=null,this.bossEncounterActive=!1,this.currentWave=0,this.waveInProgress=!1,this.waveConfig=null,this.enemiesSpawnedInWave=0,this.enemiesKilledInWave=0,this.enemiesEscapedInWave=0,this.patternSpawnCounts={},this.spawnTimer=0,this.waveStartTime=0,this.lastBossEncounterTime=0,this.formations=[],this.formationSpawnQueue=[],this.nextDiveTime=2,this.diveAttackCooldown=2,this.diveAttackTimer=0,this.divingEnemies.clear(),this.totalEnemiesSpawned=0,this.totalEnemiesKilled=0,this.totalEnemiesEscaped=0,this.totalScore=0,this.currentEnvironment="space",this.environmentalEffects=null,typeof GameObject<"u"&&GameObject.idCounter&&(GameObject.idCounter=0),console.log("EnemyManager reset")}updateCanvasDimensions(e,t){this.canvasWidth=e,this.canvasHeight=t,this.gridWidth=Math.ceil(e/this.gridSize),this.gridHeight=Math.ceil(t/this.gridSize);for(const s of this.activeEnemies)s.canvasWidth=e,s.canvasHeight=t}getStatistics(){return{currentWave:this.currentWave,totalEnemiesSpawned:this.totalEnemiesSpawned,totalEnemiesKilled:this.totalEnemiesKilled,totalEnemiesEscaped:this.totalEnemiesEscaped,totalScore:this.totalScore,activeEnemies:this.activeEnemies.length,activeBosses:this.activeBosses.length,bossEncounterActive:this.bossEncounterActive,formations:this.formations.length,killRatio:this.totalEnemiesSpawned>0?this.totalEnemiesKilled/this.totalEnemiesSpawned:0}}checkBossEncounter(){this.bossEncounterActive||this.lastBossEncounterLevel&&this.levelManager&&this.levelManager.currentLevel<=this.lastBossEncounterLevel||this.waveConfig&&this.waveConfig.hasBoss&&this.enemiesSpawnedInWave>=Math.floor(this.waveConfig.totalEnemies*.7)&&this.spawnBossFromWave()}checkBossWarpTriggers(e){e.isWarping&&!e.warpInitiated&&(e.warpInitiated=!0,this.handleBossWarpInitiated(e))}handleBossWarpInitiated(e){console.log(`Boss "${e.bossName}" initiated reality warp`),this.bossWarpCallback&&this.bossWarpCallback(e,e.getBossWarpPrompt())}handleBossWarpCompleted(e){console.log(`Boss "${e.bossName}" completed reality warp`),e.baseSpeed*=1.1,e.currentSpeed=e.baseSpeed}handleBossAbilityUsed(e,t){console.log(`Boss used ability: ${e.name} at position ${t.x}, ${t.y}`)}handleBossDamageTaken(e,t){}endBossEncounter(){this.bossEncounterActive=!1,this.currentBoss=null,this.lastBossEncounterTime=Date.now(),this.lastBossEncounterLevel=this.levelManager?this.levelManager.currentLevel:0,console.log("Boss encounter ended")}setBossDefeatCallback(e){this.bossDefeatCallback=e}setBossWarpCallback(e){this.bossWarpCallback=e}getBossEncounterStatus(){return{isActive:this.bossEncounterActive,currentBoss:this.currentBoss,lastEncounterTime:this.lastBossEncounterTime,lastEncounterLevel:this.lastBossEncounterLevel,cooldownRemaining:"Until next level"}}getActiveEnemies(){return this.activeEnemies}handleEnvironmentChange(e){if(console.log("Handling environment change from boss warp:",e),this.currentEnvironment=e.primaryType,this.activeBosses.length>0){const t=this.activeBosses[0];this.spawnEnvironmentEnemies(e.primaryType,t.position,!0)}}}class Xe{constructor(e=null,t=null,s=null){this.realityWarpManager=e,this.enemyManager=t,this.environment=s,this.bossWarpState={isActive:!1,currentBoss:null,warpStartTime:0,warpDuration:0,warpPrompt:null,warpEffects:[],environmentChanges:[]},this.bossWarpConfigs={warpCost:0,cooldownTime:15e3,maxWarpsPerBoss:3,environmentChangeDuration:1e4},this.bossWarpPrompts={water:["The oceanic boss summons a massive tidal wave that transforms the battlefield into a submerged realm with crushing pressure and aquatic hazards. The water is deep and dark, with ancient ruins visible in the depths and bioluminescent creatures lighting up the shadows.","The abyssal overlord opens a portal to the deepest ocean trenches, filling the battlefield with ancient leviathans and crushing water pressure. The environment becomes a dark underwater abyss with thermal vents and strange coral formations.","The tidal titan unleashes a great flood that submerges the battlefield in a primordial ocean with ancient sea creatures. The water churns with mystical energy and prehistoric predators emerge from the depths."],fire:["The infernal boss erupts in volcanic fury, transforming the battlefield into a hellscape of flowing lava and fire storms. Rivers of lava crisscross the landscape while volcanic bombs rain down from above.","The burning overlord ignites the atmosphere, turning the battlefield into a volcanic wasteland with falling meteors and rivers of lava. The ground cracks open to reveal magma chambers below.","The blazing titan summons a solar flare that incinerates the battlefield into a fiery inferno with intense heat waves. The air shimmers with heat distortion and flames dance across the ruined landscape."],air:["The tempest boss summons a massive storm that transforms the battlefield into a chaotic sky realm with lightning strikes and tornadoes. Dark clouds swirl overhead and powerful winds create vortexes across the battlefield.","The zephyr overlord creates an eternal storm, filling the battlefield with hurricane winds and electrical discharges. The sky becomes a maelstrom of storm clouds with constant lightning strikes.","The aerial titan opens a portal to the sky realm, bringing forth thunderclouds and devastating wind patterns. The battlefield becomes a floating platform in the sky with cyclones and electrical storms."],earth:["The terran boss causes a massive earthquake that transforms the battlefield into a rocky wasteland with seismic hazards. The ground cracks and shifts, creating chasms and rockfalls that threaten the player.","The mountain overlord raises ancient mountains, turning the battlefield into a treacherous landscape with falling rocks and seismic activity. Massive stone pillars rise from the ground and the terrain becomes unstable.","The seismic titan awakens the earth's core, creating a battlefield of molten rock and geological instability. The ground itself becomes a hazard with lava fissures and earthquakes."],crystal:["The prismatic boss refracts reality, transforming the battlefield into a crystalline dimension with light-based hazards. Giant crystal formations grow from the ground and refract light into dangerous beams.","The crystal overlord grows massive crystal formations, turning the battlefield into a prismatic maze with refractive defenses. The crystals hum with energy and create complex light patterns.","The luminous titan summons a crystal wave that crystallizes the entire battlefield into a gemstone landscape. The ground becomes covered in precious crystals that create dazzling light effects."],shadow:["The shadow boss tears the fabric of reality, transforming the battlefield into a void dimension with shadow creatures. The environment becomes a dark realm where shadows have substance and reality itself is unstable.","The dark overlord opens a portal to the shadow realm, filling the battlefield with creeping darkness and void entities. The light dims and shadows come to life, attacking anything that moves.","The void titan warps reality itself, creating a battlefield of pure darkness with reality-warping anomalies. The laws of physics break down as the battlefield becomes a realm of pure shadow energy."]},this.environmentEnemyMapping={water:{primary:"water",secondary:["shadow","crystal"],modifiers:{water:1.5,fire:.5,air:.8,earth:1.2,crystal:1.1,shadow:1.3}},fire:{primary:"fire",secondary:["earth","air"],modifiers:{water:.5,fire:1.5,air:1.2,earth:.8,crystal:.9,shadow:1.1}},air:{primary:"air",secondary:["fire","shadow"],modifiers:{water:.8,fire:.7,air:1.5,earth:1.2,crystal:1,shadow:.9}},earth:{primary:"earth",secondary:["crystal","water"],modifiers:{water:1.2,fire:1.1,air:.8,earth:1.5,crystal:1.3,shadow:.7}},crystal:{primary:"crystal",secondary:["earth","air"],modifiers:{water:1.1,fire:.9,air:1,earth:1.2,crystal:1.5,shadow:1.4}},shadow:{primary:"shadow",secondary:["water","air"],modifiers:{water:1.3,fire:1.1,air:.9,earth:.7,crystal:1.4,shadow:1.5}}},this.bossWarpEffects={water:[{name:"crushing_pressure",effect:"slow_player",duration:5e3,strength:.5},{name:"aquatic_minions",effect:"spawn_enemies",duration:0,count:3,type:"water"},{name:"tidal_wave",effect:"screen_shake",duration:2e3,strength:.8}],fire:[{name:"heat_haze",effect:"distort_vision",duration:4e3,strength:.6},{name:"lava_bombs",effect:"projectile_rain",duration:6e3,type:"fire"},{name:"inferno",effect:"damage_over_time",duration:3e3,damage:5}],air:[{name:"tornado",effect:"pull_player",duration:4e3,strength:.7},{name:"lightning_strikes",effect:"random_damage",duration:5e3,chance:.1,damage:15},{name:"wind_barrier",effect:"boss_shield",duration:3e3,strength:.5}],earth:[{name:"earthquake",effect:"screen_shake",duration:3e3,strength:1},{name:"rockfall",effect:"projectile_rain",duration:4e3,type:"earth"},{name:"stone_skin",effect:"boss_defense",duration:5e3,multiplier:.7}],crystal:[{name:"prismatic_beam",effect:"laser_sweep",duration:4e3,damage:8},{name:"refraction",effect:"clone_boss",duration:3e3,count:2},{name:"crystallize",effect:"slow_projectiles",duration:5e3,multiplier:.4}],shadow:[{name:"void_portal",effect:"spawn_enemies",duration:0,count:4,type:"shadow"},{name:"reality_tear",effect:"teleport_boss",duration:0},{name:"darkness",effect:"reduce_vision",duration:6e3,radius:.3}]},this.onBossWarpStartCallback=null,this.onBossWarpEndCallback=null,this.onEnvironmentChangeCallback=null,this.debugMode=E.DEBUG_MODE,this.logEnabled=E.ENABLE_CONSOLE_LOGS}init(e={}){try{return e.bossWarpConfigs&&(this.bossWarpConfigs={...this.bossWarpConfigs,...e.bossWarpConfigs}),this.enemyManager&&this.enemyManager.setBossWarpCallback((t,s)=>this.handleBossWarpRequest(t,s)),!0}catch(t){return console.error("Failed to initialize BossWarpManager:",t),!1}}handleBossWarpRequest(e,t){if(!e||this.bossWarpState.isActive){console.warn("Cannot initiate boss warp - invalid boss or warp already active");return}this.initiateBossWarp(e,t)}initiateBossWarp(e,t=null){try{this.bossWarpState.isActive=!0,this.bossWarpState.currentBoss=e,this.bossWarpState.warpStartTime=Date.now(),this.bossWarpState.warpPrompt=t||this.getBossWarpPrompt(e.type),this.bossWarpState.warpEffects=this.getBossWarpEffects(e.type),this.onBossWarpStartCallback&&this.onBossWarpStartCallback(e,this.bossWarpState.warpPrompt),this.realityWarpManager&&this.initiateEnvironmentChange(e),this.applyImmediateWarpEffects(e)}catch(s){console.error("Error initiating boss warp:",s),this.bossWarpState.isActive=!1}}getBossWarpPrompt(e){const t=this.bossWarpPrompts[e]||this.bossWarpPrompts.shadow;return t[Math.floor(Math.random()*t.length)]}getBossWarpEffects(e){return this.bossWarpEffects[e]||this.bossWarpEffects.shadow}initiateEnvironmentChange(e){if(this.realityWarpManager)try{const t=this.createEnvironmentConfig(e.type),s=this.realityWarpManager.canWarp();s.canWarp||console.warn(`Boss warp cannot be executed: ${s.message}`);const a={prompt:this.bossWarpState.warpPrompt,environmentConfig:t,noCost:!0,bossInitiated:!0,bossType:e.type,bossName:e.bossName,duration:this.bossWarpState.warpDuration,immediate:!0},i=this.realityWarpManager.executeWarp(a);i.success?(this.bossWarpState.environmentChanges=t,this.enemyManager&&this.enemyManager.handleEnvironmentChange(t),this.onEnvironmentChangeCallback&&this.onEnvironmentChangeCallback(t),this.spawnBossSpecificEnemies(e,t)):(console.error(`Boss warp failed for "${e.bossName}":`,i.message),this.applyLocalEnvironmentChanges(t))}catch(t){console.error("Error initiating environment change:",t);const s=this.createEnvironmentConfig(e.type);this.applyLocalEnvironmentChanges(s)}}applyLocalEnvironmentChanges(e){this.bossWarpState.environmentChanges=e,this.enemyManager&&this.enemyManager.handleEnvironmentChange(e),this.onEnvironmentChangeCallback&&this.onEnvironmentChangeCallback(e),this.applyImmediateWarpEffects(this.bossWarpState.currentBoss)}spawnBossSpecificEnemies(e,t){this.enemyManager&&(this.enemyManager.handleEnvironmentChange(t),this.enemyManager.getBossEnemySpawnPattern(e.type),this.enemyManager.spawnEnvironmentEnemies(t.primaryType,e.position,!0),this.applyEnemyModifiers(t.enemyModifiers))}applyEnemyModifiers(e){if(!this.enemyManager||!e)return;const t=this.enemyManager.getActiveEnemies();for(const s of t){const i=1+((e[s.type]||1)-1)*1.5;s.maxHealth&&(s.maxHealth*=i,s.health=Math.min(s.health,s.maxHealth)),s.baseSpeed&&(s.baseSpeed*=i,s.currentSpeed=s.baseSpeed),s.damage&&(s.damage*=i)}}createEnvironmentConfig(e){const t=this.environmentEnemyMapping[e]||this.environmentEnemyMapping.shadow;return{primaryType:t.primary,secondaryTypes:t.secondary,enemyModifiers:t.modifiers,environmentEffects:this.bossWarpState.warpEffects,duration:this.bossWarpConfigs.environmentChangeDuration,bossInitiated:!0,bossType:e}}applyImmediateWarpEffects(e){for(const t of this.bossWarpState.warpEffects)this.applyWarpEffect(e,t)}applyWarpEffect(e,t){var s;switch(t.effect){case"boss_shield":e.maxHealth*=1+t.strength*.5,e.health=Math.min(e.health,e.maxHealth);break;case"boss_defense":e.damageMultiplier=t.multiplier;break;case"spawn_enemies":if(this.enemyManager)for(let a=0;a<t.count;a++){const i={x:e.position.x+(Math.random()-.5)*200,y:e.position.y+(Math.random()-.5)*200};this.enemyManager.spawnEnemy(i.x,i.y,t.type)}break;case"teleport_boss":e.position.x=Math.random()*(((s=this.enemyManager)==null?void 0:s.canvasWidth)||800),e.position.y=Math.random()*200+100;break}}endBossWarp(){if(!this.bossWarpState.isActive)return;this.bossWarpState.currentBoss,this.bossWarpState.isActive=!1;const e=this.bossWarpState.currentBoss;this.bossWarpState.currentBoss=null,this.bossWarpState.warpStartTime=0,this.bossWarpState.warpPrompt=null,this.bossWarpState.warpEffects=[],this.bossWarpState.environmentChanges=[],this.onBossWarpEndCallback&&e&&this.onBossWarpEndCallback(e),e&&e.onWarpCompleted&&e.onWarpCompleted(e)}update(e){this.bossWarpState.isActive&&this.updateWarpEffects(e)}updateWarpEffects(e){}getBossWarpState(){return{isActive:this.bossWarpState.isActive,currentBoss:this.bossWarpState.currentBoss,warpProgress:this.bossWarpState.isActive?1:0,warpPrompt:this.bossWarpState.warpPrompt,activeEffects:this.bossWarpState.warpEffects.length,environmentChanges:this.bossWarpState.environmentChanges}}getEnvironmentMapping(e){return this.environmentEnemyMapping[e]||this.environmentEnemyMapping.shadow}setBossWarpStartCallback(e){this.onBossWarpStartCallback=e}setBossWarpEndCallback(e){this.onBossWarpEndCallback=e}setEnvironmentChangeCallback(e){this.onEnvironmentChangeCallback=e}reset(){this.bossWarpState.isActive&&this.endBossWarp(),this.bossWarpState={isActive:!1,currentBoss:null,warpStartTime:0,warpDuration:5e3,warpPrompt:null,warpEffects:[],environmentChanges:[]}}}const oe=E.API_BASE_URL;class le{constructor(e="default"){this.userId=e,this.dailyData={date:this.getCurrentDate(),completedLevels:new Set,levelCompletionRates:{},dailyTokensEarned:0,maxDailyTokens:1e5,sessionTokensEarned:0,sessionStartTime:Date.now(),levelCompletionCount:{}},this.loadFromServer(),this.ensureCurrentDate()}getCurrentDate(){return new Date().toISOString().split("T")[0]}ensureCurrentDate(){const e=this.getCurrentDate();this.dailyData.date!==e&&(this.dailyData={date:e,completedLevels:new Set,levelCompletionRates:{},dailyTokensEarned:0,maxDailyTokens:1e5},this.saveToStorage())}canEarnReward(e){return this.ensureCurrentDate(),!this.dailyData.completedLevels.has(e)}async recordLevelCompletion(e,t,s){this.ensureCurrentDate(),this.dailyData.completedLevels.add(e),this.dailyData.levelCompletionRates[e]=t,this.dailyData.dailyTokensEarned+=s,this.dailyData.sessionTokensEarned+=s,this.dailyData.levelCompletionCount[e]||(this.dailyData.levelCompletionCount[e]=0),this.dailyData.levelCompletionCount[e]++,await this.saveToServer()}calculateMaxReward(e){const t=E.BASE_LEVEL_REWARD,s=Math.pow(1.6,Math.floor((e-1)/5));return Math.floor(t*s)}calculateCompletionReward(e,t,s=[]){const{totalEnemies:a,enemiesDefeated:i}=t,n=a>0?i/a:0,r=this.calculateMaxReward(e),o=this.calculateProbabilisticReward(r,n,s);return this.rollForReward(o)}calculateProbabilisticReward(e,t,s=[]){const a=E.REWARD_SYSTEM;let i=a.BASE_VARIANCE;t>=1?i=a.PERFECT_SCORE_VARIANCE:t<.5&&(i=a.POOR_SCORE_VARIANCE);const n=Math.min(s.length*a.POWER_UP_VARIANCE_REDUCTION,a.MAX_VARIANCE_REDUCTION),r=Math.max(.1,i-n),o=Math.max(e*a.MIN_REWARD_RATIO,e*(1-r));return{maxReward:e,minReward:o,maxPossibleReward:e,variance:r,expectedValue:e*(1-a.HOUSE_EDGE),completionPercentage:t,activePowerUps:s.length,maxRewardChance:a.MAX_REWARD_CHANCE,aboveAverageChance:a.ABOVE_AVERAGE_CHANCE,averageChance:a.AVERAGE_CHANCE,belowAverageChance:a.BELOW_AVERAGE_CHANCE,minRewardChance:a.MIN_REWARD_CHANCE}}rollForReward(e){const t=E.REWARD_SYSTEM,s=Math.random();if(s<t.MIN_REWARD_CHANCE)return Math.floor(e.minReward);if(s<t.MIN_REWARD_CHANCE+t.BELOW_AVERAGE_CHANCE){const a=e.maxReward*.5-e.minReward;return Math.floor(e.minReward+Math.random()*a)}else if(s<t.MIN_REWARD_CHANCE+t.BELOW_AVERAGE_CHANCE+t.AVERAGE_CHANCE){const a=e.maxReward*.5,i=e.maxReward*.75;return Math.floor(a+Math.random()*(i-a))}else if(s<t.MIN_REWARD_CHANCE+t.BELOW_AVERAGE_CHANCE+t.AVERAGE_CHANCE+t.ABOVE_AVERAGE_CHANCE){const a=e.maxReward*.75,i=e.maxReward*.99;return Math.floor(a+Math.random()*(i-a))}else return Math.floor(e.maxReward)}isDailyLimitReached(){return this.ensureCurrentDate(),this.dailyData.dailyTokensEarned>=this.dailyData.maxDailyTokens}getRemainingDailyCapacity(){return this.ensureCurrentDate(),Math.max(0,this.dailyData.maxDailyTokens-this.dailyData.dailyTokensEarned)}getDailyProgress(){return this.ensureCurrentDate(),{date:this.dailyData.date,completedLevels:Array.from(this.dailyData.completedLevels),levelCompletionRates:{...this.dailyData.levelCompletionRates},dailyTokensEarned:this.dailyData.dailyTokensEarned,maxDailyTokens:this.dailyData.maxDailyTokens,remainingCapacity:this.getRemainingDailyCapacity(),isLimitReached:this.isDailyLimitReached()}}async loadFromServer(){try{const e=await fetch(`${oe}/progress/${this.userId}`);if(e.ok){const t=await e.json();this.dailyData={date:t.date||this.getCurrentDate(),completedLevels:new Set(t.completed_levels||[]),levelCompletionRates:t.levelCompletionRates||{},dailyTokensEarned:t.daily_tokens_earned||0,maxDailyTokens:t.max_daily_tokens||1e5,sessionTokensEarned:t.tokens_earned_this_session||0,sessionStartTime:new Date(t.session_start_time).getTime()||Date.now(),levelCompletionCount:t.levelCompletionCount||{}}}else console.warn("Failed to load data from server, falling back to localStorage"),this.loadFromStorage()}catch(e){console.warn("Failed to load data from server, falling back to localStorage:",e),this.loadFromStorage()}}loadFromStorage(){try{const e=`dailyRewardTracker_${this.userId}`,t=localStorage.getItem(e);if(t){const s=JSON.parse(t);this.dailyData={date:s.date||this.getCurrentDate(),completedLevels:new Set(s.completedLevels||[]),levelCompletionRates:s.levelCompletionRates||{},dailyTokensEarned:s.dailyTokensEarned||0,maxDailyTokens:s.maxDailyTokens||1e5,sessionTokensEarned:s.sessionTokensEarned||0,sessionStartTime:s.sessionStartTime||Date.now(),levelCompletionCount:s.levelCompletionCount||{}}}}catch(e){console.warn("Failed to load daily reward data from localStorage:",e)}}async saveToServer(){try{(await fetch(`${oe}/progress/track`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:this.userId,...this.getDailyProgress()})})).ok||(console.warn("Failed to save data to server, falling back to localStorage"),this.saveToStorage())}catch(e){console.warn("Failed to save data to server, falling back to localStorage:",e),this.saveToStorage()}}saveToStorage(){try{const e={date:this.dailyData.date,completedLevels:Array.from(this.dailyData.completedLevels),levelCompletionRates:this.dailyData.levelCompletionRates,dailyTokensEarned:this.dailyData.dailyTokensEarned,maxDailyTokens:this.dailyData.maxDailyTokens,sessionTokensEarned:this.dailyData.sessionTokensEarned,sessionStartTime:this.dailyData.sessionStartTime,levelCompletionCount:this.dailyData.levelCompletionCount},t=`dailyRewardTracker_${this.userId}`;localStorage.setItem(t,JSON.stringify(e))}catch(e){console.warn("Failed to save daily reward data to localStorage:",e)}}reset(){this.dailyData={date:this.getCurrentDate(),completedLevels:new Set,levelCompletionRates:{},dailyTokensEarned:0,maxDailyTokens:1e5,sessionTokensEarned:0,sessionStartTime:Date.now(),levelCompletionCount:{}},this.saveToServer()}}class Je{constructor(){this.dailyRewardTracker=new le,this.playerBalance=0,this.totalEarned=0,this.totalSpent=0,this.transactionHistory=[],this.maxHistorySize=100,this.performanceMetrics={levelsCompleted:0,totalScore:0,averageCompletionTime:0,perfectCompletions:0,speedBonuses:0,accuracyBonuses:0},this.loadFromStorage(),this.rewardMultipliers={base:1,speed:1.125,accuracy:1.075,perfect:1.25,difficulty:1},this.pendingRewards=[],this.rewardAnimations=[],this.onBalanceUpdateCallback=null,this.onTransactionCallback=null,this.onRewardEarnedCallback=null,this.debugMode=!1,this.walletConnected=!1,this.walletAddress=null,this.walletBalance=0,this.walletProvider=null,this.hotWalletAddress="******************************************",this.wishTokenAddress="******************************************",this.apiBaseUrl=E.API_BASE_URL}async makeAuthenticatedApiCall(e,t="GET",s=null){try{const a=`${this.apiBaseUrl}${e}`,i={method:t,headers:{"Content-Type":"application/json",Authorization:"Bearer secure-token-for-development"}};s&&(i.body=JSON.stringify(s));const n=await fetch(a,i);if(!n.ok){const r=await n.json().catch(()=>({}));throw new Error(`API call failed: ${n.status} ${n.statusText} - ${r.error||"Unknown error"}`)}return await n.json()}catch(a){throw console.error(`API call to ${e} failed:`,a),a}}async makeAuthenticatedApiCall(e,t="GET",s=null){try{const a=`${this.apiBaseUrl}${e}`,i={method:t,headers:{"Content-Type":"application/json",Authorization:"Bearer secure-token-for-development"}};s&&(i.body=JSON.stringify(s));const n=await fetch(a,i);if(!n.ok){const r=await n.json().catch(()=>({}));throw new Error(`API call failed: ${n.status} ${n.statusText} - ${r.error||"Unknown error"}`)}return await n.json()}catch(a){throw console.error(`API call to ${e} failed:`,a),a}}initializeWallet(){if(console.log("🔧 Initializing wallet connection..."),console.log("🔧 Current wallet state before init:",{connected:this.walletConnected,address:this.walletAddress,provider:this.walletProvider}),this.checkOrangeIDWallet()){console.log("✅ Using OrangeID wallet address"),console.log("🔧 Wallet state after OrangeID init:",{connected:this.walletConnected,address:this.walletAddress,provider:this.walletProvider});return}typeof window<"u"&&typeof window.ethereum<"u"?(console.log("✅ MetaMask/Wallet detected (window.ethereum)"),this.walletProvider=window.ethereum,this.setupWalletEventListeners(),window.ethereum.selectedAddress&&(console.log("✅ Already connected to wallet:",window.ethereum.selectedAddress),this.walletAddress=window.ethereum.selectedAddress,this.walletConnected=!0,this.syncWalletBalance(),this.initializeDailyRewardTracker())):typeof window<"u"&&typeof window.web3<"u"?(console.log("✅ Legacy web3 provider detected"),this.walletProvider=window.web3.currentProvider):this.debugMode?(console.log("🐛 Debug mode: Using localStorage wallet simulation"),this.walletConnected=!0,this.walletAddress="0xDebugWalletAddress",this.syncWalletBalance()):console.warn("⚠️ No cryptocurrency wallet detected"),console.log("🔧 Wallet initialization complete:",{connected:this.walletConnected,address:this.walletAddress,provider:this.walletProvider?"detected":"none"})}checkOrangeIDWallet(){if(console.log("🔍 Checking for OrangeID wallet..."),typeof window<"u"&&window.AuthManager&&window.AuthManager.isAuthenticated){console.log("🔍 Found global AuthManager");const e=window.AuthManager.getUser();if(console.log("👤 Global AuthManager user:",e),e&&e.ethAddress)return console.log("✅ Found wallet address in global AuthManager:",e.ethAddress),this.walletConnected=!0,this.walletAddress=e.ethAddress,this.walletProvider="orangeid",this.syncWalletBalance(),this.checkAndShowDevDashboard(),!0;console.log("⚠️ Global AuthManager user missing ethAddress")}else console.log("⚠️ Global AuthManager not available or not authenticated");if(this.gameEngine&&this.gameEngine.mainMenu&&this.gameEngine.mainMenu.authManager&&this.gameEngine.mainMenu.authManager.isAuthenticated){console.log("🔍 Found game engine AuthManager");const e=this.gameEngine.mainMenu.authManager.getUser();if(console.log("👤 Game engine AuthManager user:",e),e&&e.ethAddress)return console.log("✅ Found wallet address in game engine AuthManager:",e.ethAddress),this.walletConnected=!0,this.walletAddress=e.ethAddress,this.walletProvider="orangeid",this.syncWalletBalance(),this.checkAndShowDevDashboard(),!0;console.log("⚠️ Game engine AuthManager user missing ethAddress")}else console.log("⚠️ Game engine AuthManager not available or not authenticated");return console.log("❌ No OrangeID wallet found"),!1}initializeDailyRewardTracker(){this.walletAddress&&(this.dailyRewardTracker=new le(this.walletAddress),console.log(`✅ Initialized daily reward tracker for user: ${this.walletAddress}`))}setupWalletEventListeners(){if(!this.walletProvider||!this.walletProvider.on)return;const e=this.walletProvider.request;this.walletProvider.request=async t=>{if(t.method==="wallet_switchEthereumChain"||t.method==="wallet_addEthereumChain"){const s=t.params[0];if(s&&s.chainId&&parseInt(s.chainId,16)===1)return console.log("🚨 BLOCKED ATTEMPT TO SWITCH TO MAINNET - FORCING CHAIN 31337"),e.call(this.walletProvider,{method:"wallet_switchEthereumChain",params:[{chainId:"0x7a69"}]})}return e.call(this.walletProvider,t)},this.walletProvider.on("accountsChanged",t=>{t.length===0?this.handleWalletDisconnect():this.handleAccountsChanged(t)}),this.walletProvider.on("chainChanged",t=>{this.handleChainChanged(t)}),this.walletProvider.on("disconnect",t=>{this.handleWalletDisconnect(t)})}handleAccountsChanged(e){e.length>0?(this.walletAddress=e[0],this.walletConnected=!0,this.syncWalletBalance(),this.triggerWalletUpdate()):this.handleWalletDisconnect()}handleChainChanged(e){console.log("Chain changed to:",e);const t="0x7a69",s=parseInt(e,16);if(s===1){console.log("🚨 ORANGEID TRYING TO SWITCH TO MAINNET - FORCING BACK TO CHAIN 31337!"),this.walletProvider&&this.walletProvider.request&&this.walletProvider.request({method:"wallet_switchEthereumChain",params:[{chainId:t}]}).then(()=>{console.log("✅ FORCED BACK TO CHAIN 31337 - ORANGEID BLOCKED")}).catch(a=>{console.error("❌ Failed to force back to Chain 31337:",a)});return}this.walletConnected&&s===31337&&this.syncWalletBalance()}handleWalletDisconnect(e=null){this.walletConnected=!1,this.walletAddress=null,this.walletBalance=0,console.log("Wallet disconnected",e),this.triggerWalletUpdate()}async connectWallet(){if(this.debugMode)return this.walletConnected=!0,this.walletAddress="0xDebugWalletAddress",await this.syncWalletBalance(),{success:!0,address:this.walletAddress,balance:this.walletBalance,isDebug:!0};if(!this.walletProvider)return{success:!1,error:"No wallet provider available",message:"Please install a cryptocurrency wallet like MetaMask"};try{console.log("🔌 ENSURING CONNECTION TO CHAIN ID 31337...");const e="0x7a69";console.log("🎯 Target Chain ID for ETH Test Mode:",e);try{const s=await this.walletProvider.request({method:"eth_chainId"});console.log("🔗 Current Chain ID:",s),s!==e?(console.log("🔄 Switching to Chain ID 31337 for ETH Test Mode..."),await this.walletProvider.request({method:"wallet_switchEthereumChain",params:[{chainId:e}]}),console.log("✅ Successfully switched to Chain ID 31337")):console.log("✅ Already connected to Chain ID 31337")}catch(s){if(console.error("❌ Failed to switch to Chain ID 31337:",s),s.code===4902){console.log("📝 Chain ID 31337 not in MetaMask, adding network...");try{await this.walletProvider.request({method:"wallet_addEthereumChain",params:[{chainId:e,chainName:"Local Hardhat Development",rpcUrls:["http://localhost:8545"],nativeCurrency:{name:"ETH",symbol:"ETH",decimals:18}}]}),console.log("✅ Added Chain ID 31337 to MetaMask"),await this.walletProvider.request({method:"wallet_switchEthereumChain",params:[{chainId:e}]}),console.log("✅ Successfully switched to Chain ID 31337")}catch(a){throw console.error("❌ Failed to add Chain ID 31337:",a),new Error("Could not add/switch to Chain ID 31337. Please manually add the local development network to MetaMask.")}}else throw s}const t=await this.walletProvider.request({method:"eth_requestAccounts"});return t&&t.length>0?(this.walletAddress=t[0],this.walletConnected=!0,console.log("✅ Wallet connected to Chain ID 31337:",this.walletAddress),await this.syncWalletBalance(),this.triggerWalletUpdate(),await this.checkAndShowDevDashboard(),{success:!0,address:this.walletAddress,balance:this.walletBalance,chainId:e}):{success:!1,error:"No accounts found",message:"Please unlock your wallet and try again"}}catch(e){return console.error("❌ Wallet connection to Chain ID 31337 failed:",e),console.error("Error details:",e.message,e.code),{success:!1,error:e.message,message:"Failed to connect wallet to Chain ID 31337 for ETH Test Mode",code:e.code}}}async syncWalletBalance(){if(this.debugMode){if(typeof localStorage<"u"){const e=localStorage.getItem("debugWalletBalance");this.walletBalance=e?parseInt(e,10):1e3,console.log(`🐛 Debug mode: Using balance ${this.walletBalance} from localStorage`)}else this.walletBalance=1e3,console.log(`🐛 Debug mode (Node.js): Using default balance ${this.walletBalance}`);return this.walletBalance}if(!this.walletConnected)return console.log("⚠️ Wallet not connected, returning 0 balance"),this.walletBalance=0,0;try{if(this.walletProvider==="orangeid")return console.log("📱 Using OrangeID for balance sync"),await this.syncOrangeIDWalletBalance();if(this.walletProvider&&typeof this.walletProvider.request=="function"){console.log("🔍 Requesting balance from wallet provider for address:",this.walletAddress),console.log("🔗 Provider type:",typeof this.walletProvider),console.log("🔗 Provider has request method:",typeof this.walletProvider.request);try{const s=await this.walletProvider.request({method:"eth_chainId"});console.log("🔗 Current chain ID:",s),console.log("🔗 Chain ID in decimal:",parseInt(s,16));const a=await this.walletProvider.request({method:"eth_blockNumber"});console.log("🔗 Current block number:",a),console.log("🔗 Block number in decimal:",parseInt(a,16));const i=parseInt(s,16);console.log(`🔍 CHECKING CHAIN ID: ${i} (expected: 31337 for Hardhat)`),i===31337?console.log("✅ CORRECT: Connected to Hardhat local development network (Chain ID: 31337)"):i===1337?console.log("✅ CORRECT: Connected to Ganache local development network (Chain ID: 1337)"):i===1?(console.warn("❌ WRONG: Connected to Ethereum Mainnet (Chain ID: 1) - SHOULD BE 31337!"),console.warn("❌ ETH Test Mode requires Chain ID 31337 for local development")):console.log(`⚠️ UNEXPECTED: Connected to network with Chain ID: ${i} (expected 31337)`)}catch(s){console.warn("⚠️ Could not get network info:",s)}const e=await this.walletProvider.request({method:"eth_getBalance",params:[this.walletAddress,"latest"]});console.log("📊 Raw balance from provider (wei):",e),console.log("📊 Balance type:",typeof e);let t;try{if(typeof e=="string"&&e.startsWith("0x")){const s=BigInt(e);t=Number(s)/1e18}else typeof e=="bigint"?t=Number(e)/1e18:typeof e=="string"?t=parseFloat(e)/1e18:t=parseFloat(e);this.walletBalance=t,console.log(`✅ Wallet balance synced: ${this.walletBalance} ETH for address: ${this.walletAddress}`),console.log(`✅ This equals ${t} ETH from raw balance: ${e}`)}catch(s){console.error("❌ Failed to convert balance:",s),console.log("🔄 Using raw balance as fallback:",e),this.walletBalance=parseFloat(e)||0}return this.walletBalance}else return console.warn("⚠️ Unsupported wallet provider:",this.walletProvider),this.walletBalance=0,0}catch(e){console.error("❌ Failed to sync wallet balance:",e),console.error("Error details:",e.message,e.code,e.stack),console.log("🔧 Attempting fallback balance check for local development...");try{const t=await this.walletProvider.request({method:"eth_chainId"});console.log("🔗 Chain ID available:",t),console.log("🔗 Chain ID in decimal:",parseInt(t,16));const s=parseInt(t,16);if(console.log(`🔍 Checking if chain ID ${s} is a local development network`),s===31337||s===1337||t==="0x7a69"||t==="0x539"){console.log("🧪 Local development network detected!");try{console.log("🔄 Retrying balance check with different parameters...");const a=await this.walletProvider.request({method:"eth_getBalance",params:[this.walletAddress,"latest"]});if(console.log("🔄 Retry balance result:",a),a&&a!=="0x0"){const i=BigInt(a),n=Number(i)/1e18;return this.walletBalance=n,console.log(`✅ Retry successful: ${this.walletBalance} ETH`),this.walletBalance}}catch(a){console.log("🔄 Retry failed, using test balance:",a)}return console.log("🧪 Using test balance for local development"),this.walletBalance=1e4,console.log(`🧪 Using test balance: ${this.walletBalance} ETH for local development`),this.walletBalance}else console.log(`🔍 Chain ID ${s} is not a recognized local development network`)}catch(t){console.error("❌ Fallback balance check also failed:",t)}return this.walletBalance=0,0}}async syncOrangeIDWalletBalance(){if(console.log("📱 Syncing OrangeID wallet balance..."),console.log("📱 Current wallet state:",{address:this.walletAddress,connected:this.walletConnected,provider:this.walletProvider}),this.debugMode){if(typeof localStorage<"u"){const e=localStorage.getItem("debugWalletBalance");this.walletBalance=e?parseInt(e,10):1e3}else this.walletBalance=1e3;return console.log("🐛 Debug mode balance:",this.walletBalance),this.walletBalance}if(!this.walletConnected||this.walletProvider!=="orangeid"||!this.walletAddress)return console.warn("OrangeID wallet not properly connected for balance sync"),console.log("Current wallet state:",{connected:this.walletConnected,provider:this.walletProvider,address:this.walletAddress}),this.walletBalance=0,0;console.log("📱 Using existing OrangeID wallet for balance sync");try{if(console.log("Syncing OrangeID wallet balance for address:",this.walletAddress),typeof window<"u"&&window.ethereum&&window.ethereum.request){console.log("📱 Using OrangeID + MetaMask for balance sync");const e=await window.ethereum.request({method:"eth_getBalance",params:[this.walletAddress,"latest"]});console.log("📊 Raw balance from MetaMask (wei):",e);let t;if(typeof e=="string"){const s=BigInt(e);t=Number(s)/1e18}else if(typeof e=="bigint")t=Number(e)/1e18;else{const s=BigInt(e);t=Number(s)/1e18}return console.log("💰 Balance in ETH:",t),this.walletBalance=t,this.walletBalance}else return console.warn("⚠️ MetaMask not available for balance sync with OrangeID"),this.walletBalance=0,0}catch(e){return console.error("Failed to sync OrangeID wallet balance:",e),this.walletBalance=0,0}}getWalletBalance(){return this.walletBalance}getBalanceDisplay(){var e,t;return typeof window<"u"&&((t=(e=window.gameEngine)==null?void 0:e.ethTestModeManager)!=null&&t.isTestMode)?`${this.walletBalance.toFixed(6)} ETH (Test Mode)`:`${this.playerBalance} WISH`}isWalletConnected(){return this.walletConnected}getWalletAddress(){return this.walletAddress}setWalletAddress(e){this.walletAddress=e}triggerWalletUpdate(){this.onWalletUpdateCallback&&this.onWalletUpdateCallback({connected:this.walletConnected,address:this.walletAddress,balance:this.walletBalance})}setOnWalletUpdate(e){this.onWalletUpdateCallback=e}async calculateLevelReward(e,t=null){if(!e.completed)return{totalReward:0,breakdown:{reason:"level_not_completed"}};const s=e.levelNumber,a=await this.checkServerSideTracking(s);if(!a.canEarnReward)return{totalReward:0,breakdown:{reason:a.reason}};const i=await this.checkAntiAbuse();if(!i.allowRewards)return{totalReward:0,breakdown:{reason:"suspicious_activity_detected",details:i.riskFactors}};const n=await this.getGlobalTreasuryStatus();if(n.rewardsPaused)return{totalReward:0,breakdown:{reason:"galaxy_mined_out",message:"Galaxy has been mined out! Restock via shop to spur economic activity!",treasuryRatio:n.currentRatio}};let r=this.calculateBaseReward(s);const o={totalEnemies:(t==null?void 0:t.totalEnemies)||e.totalEnemies||e.enemiesDefeated||0,enemiesDefeated:e.enemiesDefeated||0},c=o.totalEnemies>0?o.enemiesDefeated/o.totalEnemies:1;r=Math.floor(r*c);const h=await this.applyGlobalScaling(r,n);return await this.recordRewardOutflow(h,s),await this.updateServerSideTracking(s,h),{totalReward:h,breakdown:{baseReward:r,completionPercentage:c,globalScalingRatio:n.scalingRatio,finalReward:h,treasuryRatio:n.currentRatio,treasuryTier:n.tier}}}async checkServerSideTracking(e){try{const t=await this.makeAuthenticatedApiCall("/api/tracking/can-earn-reward","POST",{userId:this.walletAddress||"anonymous",levelNumber:e});return{canEarnReward:t.success,reason:t.reason||null}}catch(t){return console.error("Server-side tracking check failed:",t),this.checkLocalTracking(e)}}checkLocalTracking(e){return this.dailyRewardTracker.canEarnReward(e)?this.dailyRewardTracker.isDailyLimitReached()?{canEarnReward:!1,reason:"daily_limit_reached"}:{canEarnReward:!0,reason:null}:{canEarnReward:!1,reason:"level_already_completed_today"}}async checkAntiAbuse(){try{const e=this.collectDeviceInfo();return await this.makeAuthenticatedApiCall("/api/anti-abuse/check","POST",{userId:this.walletAddress||"anonymous",deviceInfo:e,userAgent:navigator.userAgent})}catch(e){return console.error("Anti-abuse check failed:",e),{allowRewards:!0}}}collectDeviceInfo(){return{screenResolution:`${screen.width}x${screen.height}`,colorDepth:screen.colorDepth,timezone:Intl.DateTimeFormat().resolvedOptions().timeZone,language:navigator.language,platform:navigator.platform,cookieEnabled:navigator.cookieEnabled,doNotTrack:navigator.doNotTrack}}async getGlobalTreasuryStatus(){try{return await this.makeAuthenticatedApiCall("/api/treasury/status","GET")}catch(e){return console.error("Global treasury status check failed:",e),{rewardsPaused:!1,currentRatio:1,scalingRatio:1,tier:"FULL_REWARDS"}}}async applyGlobalScaling(e,t){return Math.floor(e*(t.scalingRatio||1))}async recordRewardOutflow(e,t){try{await this.makeAuthenticatedApiCall("/api/treasury/record-outflow","POST",{amount:e,category:"levelRewards",userId:this.walletAddress||"anonymous",metadata:{levelNumber:t}})}catch(s){console.error("Failed to record reward outflow:",s)}}async updateServerSideTracking(e,t){try{await this.makeAuthenticatedApiCall("/api/tracking/record-completion","POST",{userId:this.walletAddress||"anonymous",levelNumber:e,rewardAmount:t})}catch(s){console.error("Failed to update server-side tracking:",s)}}calculateBaseReward(e){const t=E.BASE_LEVEL_REWARD,s=Math.floor((e-1)/5)+1;return t*s}calculateTimeBonus(e,t){const s=60+t*10,a=s*.5;return e<=a?Math.floor(750+t*125):e<=s*.75?Math.floor(500+t*75):e<=s?Math.floor(250+t*25):0}calculateScoreBonus(e,t){const a=Math.min(2,1+t*.05);return Math.floor(e*.25*a)}calculateSpeedBonus(e){return Math.floor(e*(this.rewardMultipliers.speed-1))}calculateAccuracyBonus(e){return Math.floor(e*(this.rewardMultipliers.accuracy-1))}calculatePerfectBonus(e){return Math.floor(e*(this.rewardMultipliers.perfect-1))}calculateDifficultyMultiplier(e){const i=1+Math.floor((e-1)/10)*.1;return Math.min(3,i)}async awardTokens(e,t,s={}){var i,n,r;if(e<=0)return{success:!1,reason:"invalid_amount"};if(t==="level_completion"&&typeof window<"u"&&window.OrangeSDKManager)try{const o=window.OrangeSDKManager.getCurrentSessionHighScore(),c=s.score||0;c>o?(s.sessionHighScore=o,s.isNewHighScore=!0):(s.sessionHighScore=o,s.isNewHighScore=!1,console.log("Score not high enough for token award. Current high:",o,"This score:",c))}catch(o){console.warn("Failed to get session high score from OrangeSDK:",o)}if(t==="level_completion"&&s.levelNumber){const o=s.levelNumber,c=s.completionPercentage||1;this.dailyRewardTracker.recordLevelCompletion(o,c,e)}if(typeof window<"u"?(n=(i=window.gameEngine)==null?void 0:i.ethTestModeManager)==null?void 0:n.isTestMode:((r=this.ethTestModeManager)==null?void 0:r.isTestMode)||!1)if(console.log(`🧪 ETH Test Mode: Processing ${t} reward of ${e} ETH`),t==="level_completion"){const o=this.walletAddress;console.log(`🧪 ETH Test Mode: Level completion reward - sending ${e} ETH from hot wallet to player ${o}`);let c=0;const h=3;let d;for(;c<=h;)try{const g=await fetch(`${this.apiBaseUrl}/wallet/send`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer secure-token-for-development"},body:JSON.stringify({toAddress:o,amount:e.toString(),reason:`Level completion reward for level ${(s==null?void 0:s.levelNumber)||"unknown"}`})});if(g.ok){const w=await g.json();return console.log("✅ ETH Test Mode: Hot wallet transaction successful:",w.transactionHash),{success:!0,transaction:{amount:e,reason:t,transactionHash:w.transactionHash,timestamp:Date.now()},newBalance:this.playerBalance+e}}else if(g.status===429&&c<h){const w=Math.pow(2,c)*1e3;console.log(`⏳ ETH Test Mode: Rate limited (429), retrying in ${w}ms (attempt ${c+1}/${h+1})`),await new Promise(v=>setTimeout(v,w)),c++;continue}else throw new Error(`Server responded with ${g.status}: ${g.statusText}`)}catch(g){if(d=g,c<h){const w=Math.pow(2,c)*1e3;console.log(`⏳ ETH Test Mode: Request failed, retrying in ${w}ms (attempt ${c+1}/${h+1}): ${g.message}`),await new Promise(v=>setTimeout(v,w)),c++}else break}return console.error("❌ ETH Test Mode: Hot wallet transaction failed after all retries:",d),{success:!1,reason:"hot_wallet_transaction_failed",message:`Failed to send ETH from hot wallet: ${d.message}`}}else{const o=(s==null?void 0:s.creatorUserId)||(s==null?void 0:s.recipientAddress);if(console.log(`🧪 ETH Test Mode: ${t} reward - sending ${e} ETH to ${o}`),!o)return console.error("❌ ETH Test Mode: No recipient address found"),{success:!1,reason:"no_recipient_address",message:"No recipient address found for ETH transfer"};try{const c=BigInt(Math.round(e*1e18)).toString();let h,d,g;this.walletProvider&&typeof this.walletProvider.request=="function"?(h=await this.walletProvider.request({method:"eth_gasPrice"}),d=await this.walletProvider.request({method:"eth_getTransactionCount",params:[this.walletAddress,"latest"]}),g=await this.walletProvider.request({method:"eth_chainId"})):(h="0x"+(await this.walletProvider.getFeeData()).gasPrice.toString(16),d="0x"+(await this.walletProvider.getTransactionCount(this.walletAddress,"latest")).toString(16),g="0x"+(await this.walletProvider.getNetwork()).chainId.toString(16));const w="0x5208",v={from:this.walletAddress,to:o,value:"0x"+BigInt(c).toString(16),gasPrice:h,gas:w,nonce:d,chainId:g};console.log("🚀 ETH Test Mode: Sending transaction with params:",v);let p;if(this.walletProvider&&typeof this.walletProvider.request=="function")p=await this.walletProvider.request({method:"eth_sendTransaction",params:[v]});else if(this.walletInstance){const y={to:o,value:c,gasPrice:h,gasLimit:w,nonce:d,chainId:g};p=(await this.walletInstance.sendTransaction(y)).hash,console.log("🚀 ETH Test Mode: Transaction sent via wallet instance! Hash:",p)}else throw new Error("No wallet provider or wallet instance available for ETH transactions");console.log("✅ ETH Test Mode: Transaction sent! Hash:",p),await this.syncWalletBalance();const f={id:this.generateTransactionId(),type:"earned",amount:e,reason:t,timestamp:Date.now(),balanceAfter:this.walletBalance,hash:p,metadata:{...s,testMode:!0,walletBalance:this.walletBalance,recipientAddress:o,transactionHash:p}};return this.addTransaction(f),this.updatePerformanceMetrics(t,s),this.addPendingReward(e,t),this.triggerBalanceUpdate(),this.triggerRewardEarned(e,t,s),{success:!0,transaction:f,newBalance:this.walletBalance,transactionHash:p,recipientAddress:o}}catch(c){return console.error("❌ ETH Test Mode: Transaction failed:",c),{success:!1,reason:"eth_transaction_failed",message:`ETH transaction failed: ${c.message}`}}}else{this.playerBalance+=e,this.totalEarned+=e;const o={id:this.generateTransactionId(),type:"earned",amount:e,reason:t,timestamp:Date.now(),balanceAfter:this.playerBalance,metadata:s};return this.addTransaction(o),this.updatePerformanceMetrics(t,s),this.addPendingReward(e,t),this.saveToStorage(),this.triggerBalanceUpdate(),this.triggerRewardEarned(e,t,s),{success:!0,transaction:o,newBalance:this.playerBalance}}}async verifyWalletBalance(){if(!this.walletConnected&&!this.debugMode)return console.warn("Wallet not connected - cannot verify balance"),!1;try{return await this.syncWalletBalance(),this.debugMode&&typeof localStorage<"u"&&localStorage.setItem("debugWalletBalance",this.walletBalance.toString()),!0}catch(e){return console.error("Failed to verify wallet balance:",e),!1}}async spendTokens(e,t,s={}){var a,i;if(e<=0)return{success:!1,reason:"invalid_amount"};if(typeof window<"u"&&((i=(a=window.gameEngine)==null?void 0:a.ethTestModeManager)!=null&&i.isTestMode)){if(await this.syncWalletBalance(),this.walletBalance<e)return{success:!1,reason:"insufficient_balance",required:e,available:this.walletBalance,hasWallet:this.walletConnected};const n=await this.spendFromWallet(e,t,s);if(!n.success)return n;const r=this.walletBalance;s.walletTransactionId=n.transactionId,s.walletAmount=e;const o={id:n.transactionId,type:"spent",amount:e,reason:t,timestamp:Date.now(),balanceAfter:r,metadata:s};return this.triggerBalanceUpdate(),this.triggerTransaction(o),{success:!0,transaction:o,newBalance:r}}else{if(this.playerBalance<e)return{success:!1,reason:"insufficient_balance",required:e,available:this.playerBalance};this.playerBalance-=e,this.totalSpent+=e;const n={id:this.generateTransactionId(),type:"spent",amount:e,reason:t,timestamp:Date.now(),balanceAfter:this.playerBalance,metadata:s};return this.addTransaction(n),this.saveToStorage(),this.triggerBalanceUpdate(),this.triggerTransaction(n),{success:!0,transaction:n,newBalance:this.playerBalance}}}async spendFromWallet(e,t,s={}){if(this.debugMode){if(this.walletBalance===0&&await this.syncWalletBalance(),this.walletBalance<e)return{success:!1,reason:"insufficient_wallet_balance",message:`Insufficient wallet balance. Required: ${e}, Available: ${this.walletBalance}`};const a=3937e-8,i=e+a;return this.walletBalance<i?{success:!1,reason:"insufficient_wallet_balance_for_gas",message:`Insufficient wallet balance for amount + gas. Required: ${i}, Available: ${this.walletBalance}`}:(this.walletBalance-=i,typeof localStorage<"u"&&localStorage.setItem("debugWalletBalance",this.walletBalance.toString()),{success:!0,transactionId:this.generateTransactionId(),amount:e,gasFee:a,totalCost:i,newWalletBalance:this.walletBalance})}if(!this.walletConnected)return{success:!1,reason:"wallet_not_connected",message:"Wallet not connected"};try{if(this.walletProvider==="orangeid")if(typeof window<"u"&&window.ethereum&&typeof window.ethereum.request=="function"){console.log("Processing real ETH transaction for OrangeID user:",e,"ETH");try{const a=BigInt(Math.round(e*1e18)).toString(),i=await window.ethereum.request({method:"eth_gasPrice"}),n="0x5208",r=await window.ethereum.request({method:"eth_getTransactionCount",params:[this.walletAddress,"latest"]});let o=(s==null?void 0:s.recipientAddress)||(s==null?void 0:s.creatorUserId)||this.hotWalletAddress;if(o.toLowerCase()===this.walletAddress.toLowerCase()&&(console.warn("⚠️ Recipient address is same as sender - routing to hot wallet instead"),o=this.hotWalletAddress),!o)return console.error("❌ ETH Test Mode: No recipient address found in metadata for spending"),{success:!1,reason:"no_recipient_address",message:"No recipient address found in metadata for ETH transaction"};console.log("📤 Transaction recipient:",o,"(from metadata or hot wallet fallback)");let c=await window.ethereum.request({method:"eth_chainId"});(this.debugMode||parseInt(c,16)===1&&window.location.hostname==="localhost")&&(c="0x7a69",console.log("🔧 Test mode: Forcing chainId to Hardhat local (31337)"));const h={from:this.walletAddress,to:o,value:"0x"+BigInt(a).toString(16),gasPrice:i,gas:n,nonce:r,chainId:c};console.log("🚀 Sending ETH transaction to recipient address with params:",h);const d=await window.ethereum.request({method:"eth_sendTransaction",params:[h]});return console.log("✅ ETH transaction sent! Hash:",d),console.log("⏳ Waiting for transaction confirmation..."),await this.syncWalletBalance(),{success:!0,transactionId:d,amount:e,newWalletBalance:this.walletBalance,transactionHash:d,toAddress:o}}catch(a){return console.error("❌ ETH transaction failed:",a),{success:!1,reason:"eth_transaction_failed",error:a.message,message:"Failed to process ETH transaction: "+a.message}}}else return await this.spendFromOrangeIDWallet(e,t,s);else if(this.walletProvider&&typeof this.walletProvider.request=="function"){console.log("Processing real ETH transaction for:",e,"ETH");try{const a=BigInt(Math.round(e*1e18)).toString(),i=await this.walletProvider.request({method:"eth_gasPrice"}),n="0x5208",r=await this.walletProvider.request({method:"eth_getTransactionCount",params:[this.walletAddress,"latest"]});let o=(s==null?void 0:s.recipientAddress)||(s==null?void 0:s.creatorUserId)||this.hotWalletAddress;if(o.toLowerCase()===this.walletAddress.toLowerCase()&&(console.warn("⚠️ Recipient address is same as sender - routing to hot wallet instead"),o=this.hotWalletAddress),!o)return console.error("❌ ETH Test Mode: No recipient address found in metadata for spending"),{success:!1,reason:"no_recipient_address",message:"No recipient address found in metadata for ETH transaction"};console.log("📤 Transaction recipient:",o,"(from metadata or hot wallet fallback)");let c=await this.walletProvider.request({method:"eth_chainId"});(this.debugMode||parseInt(c,16)===1&&window.location.hostname==="localhost")&&(c="0x7a69",console.log("🔧 Test mode: Forcing chainId to Hardhat local (31337)"));const h={from:this.walletAddress,to:o,value:"0x"+BigInt(a).toString(16),gasPrice:i,gas:n,nonce:r,chainId:c};console.log("🚀 Sending ETH transaction to recipient address with params:",h);const d=await this.walletProvider.request({method:"eth_sendTransaction",params:[h]});return console.log("✅ ETH transaction sent! Hash:",d),console.log("⏳ Waiting for transaction confirmation..."),await this.syncWalletBalance(),{success:!0,transactionId:d,amount:e,newWalletBalance:this.walletBalance,transactionHash:d,toAddress:o}}catch(a){return console.error("❌ ETH transaction failed:",a),{success:!1,reason:"eth_transaction_failed",error:a.message,message:"Failed to process ETH transaction: "+a.message}}}else return{success:!1,reason:"unsupported_wallet_provider",message:"Unsupported wallet provider: "+this.walletProvider}}catch(a){return console.error("Wallet transaction failed:",a),{success:!1,reason:"wallet_transaction_failed",error:a.message,message:"Failed to process wallet transaction"}}}async spendFromOrangeIDWallet(e,t,s={}){if(typeof window>"u"||!window.AuthManager||!window.AuthManager.token)return{success:!1,reason:"orangeid_not_available",message:"OrangeID authentication not available"};try{return console.log("Processing OrangeID wallet transaction:",e,"for reason:",t),await new Promise(i=>setTimeout(i,500)),await this.syncWalletBalance(),this.walletBalance<e?{success:!1,reason:"insufficient_wallet_balance",message:`Insufficient OrangeID wallet balance. Required: ${e}, Available: ${this.walletBalance}`}:(this.walletBalance-=e,this.debugMode&&typeof localStorage<"u"&&localStorage.setItem("debugWalletBalance",this.walletBalance.toString()),{success:!0,transactionId:this.generateTransactionId(),amount:e,newWalletBalance:this.walletBalance,provider:"orangeid"})}catch(a){return console.error("OrangeID wallet transaction failed:",a),{success:!1,reason:"orangeid_transaction_failed",error:a.message,message:"Failed to process OrangeID wallet transaction"}}}canAfford(e){var t,s;return typeof window<"u"&&((s=(t=window.gameEngine)==null?void 0:t.ethTestModeManager)!=null&&s.isTestMode)?this.getBalance()>=e:this.playerBalance>=e}getBalance(){var e,t;return typeof window<"u"&&((t=(e=window.gameEngine)==null?void 0:e.ethTestModeManager)!=null&&t.isTestMode)?this.walletBalance:this.playerBalance}createFallbackLevelManager(){const e={BASE_LEVEL_REWARD:1250,REWARD_SYSTEM:{BASE_VARIANCE:.4,PERFECT_SCORE_VARIANCE:.2,POOR_SCORE_VARIANCE:.6,POWER_UP_VARIANCE_REDUCTION:.1,MAX_VARIANCE_REDUCTION:.3,HOUSE_EDGE:.15,MIN_REWARD_RATIO:.3,MAX_REWARD_CHANCE:.1,ABOVE_AVERAGE_CHANCE:.25,AVERAGE_CHANCE:.4,BELOW_AVERAGE_CHANCE:.2,MIN_REWARD_CHANCE:.05}};return{calculateBaseLevelReward:t=>e.BASE_LEVEL_REWARD*Math.pow(1.1,t-1),calculateProbabilisticReward:(t,s=[],a=1)=>{const i=e.REWARD_SYSTEM,n=e.BASE_LEVEL_REWARD*Math.pow(1.1,t-1);let r=i.BASE_VARIANCE;a>=1?r=i.PERFECT_SCORE_VARIANCE:a<.5&&(r=i.POOR_SCORE_VARIANCE);const o=Math.min(s.length*i.POWER_UP_VARIANCE_REDUCTION,i.MAX_VARIANCE_REDUCTION),c=Math.max(.1,r-o),h=Math.max(n*i.MIN_REWARD_RATIO,n*(1-c));return{maxReward:n,minReward:h,maxPossibleReward:n,variance:c,activePowerUps:s.length,completionPercentage:a,expectedValue:n*(1-i.HOUSE_EDGE),maxRewardChance:i.MAX_REWARD_CHANCE,aboveAverageChance:i.ABOVE_AVERAGE_CHANCE,averageChance:i.AVERAGE_CHANCE,belowAverageChance:i.BELOW_AVERAGE_CHANCE,minRewardChance:i.MIN_REWARD_CHANCE}},rollForReward:t=>{const s=e.REWARD_SYSTEM,a=Math.random();if(a<s.MIN_REWARD_CHANCE)return Math.floor(t.minReward);if(a<s.MIN_REWARD_CHANCE+s.BELOW_AVERAGE_CHANCE){const i=t.maxReward*.5-t.minReward;return Math.floor(t.minReward+Math.random()*i)}else if(a<s.MIN_REWARD_CHANCE+s.BELOW_AVERAGE_CHANCE+s.AVERAGE_CHANCE){const i=t.maxReward*.5,n=t.maxReward*.75;return Math.floor(i+Math.random()*(n-i))}else if(a<s.MIN_REWARD_CHANCE+s.BELOW_AVERAGE_CHANCE+s.AVERAGE_CHANCE+s.ABOVE_AVERAGE_CHANCE){const i=t.maxReward*.75,n=t.maxReward*.99;return Math.floor(i+Math.random()*(n-i))}else return Math.floor(t.maxReward)}}}async handleLevelCompletion(e,t,s=null,a=[]){var v,p,f,y;if(!this.dailyRewardTracker.canEarnReward(e.levelNumber))return{success:!1,reason:"level_already_completed_today",message:"Level already completed today",tokensAwarded:0};if(this.dailyRewardTracker.isDailyLimitReached())return{success:!1,reason:"daily_limit_reached",message:"Daily earning limit reached",tokensAwarded:0};const i=(s==null?void 0:s.totalEnemies)>0?e.enemiesDefeated/s.totalEnemies:1;let n=null;typeof window<"u"&&((v=window.gameEngine)!=null&&v.levelManager)&&(n=window.gameEngine.levelManager),n||(n=this.createFallbackLevelManager());const r=n.calculateProbabilisticReward(e.levelNumber,a,i),o=Math.random();let c=0,h="none";if(o<r.jackpotChance)c=r.jackpotReward,h="jackpot";else if(o<r.successChance)c=r.maxPossibleReward,h="success";else if(o<r.successChance+r.bustChance)c=0,h="bust";else{const M=.3+Math.random()*.4;c=Math.floor(r.maxPossibleReward*M),h="partial"}const d=this.dailyRewardTracker.getRemainingDailyCapacity();if(c=Math.min(c,d),c<=0)return{success:!1,reason:h==="bust"?"bad_luck":"no_reward_due_to_limits",message:h==="bust"?"Bad luck! No reward this time.":"No reward due to daily limits",tokensAwarded:0,rewardType:h,rewardCalculation:r};const g=typeof window<"u"?(f=(p=window.gameEngine)==null?void 0:p.ethTestModeManager)==null?void 0:f.isTestMode:((y=this.ethTestModeManager)==null?void 0:y.isTestMode)||!1;g&&this.ethTestModeManager&&(c=this.ethTestModeManager.applyTestModeDiscount(c),console.log(`🧪 ETH Test Mode: Applied 90% discount to reward: ${c} ETH`));const w=await this.awardTokens(c,"level_completion",{score:t,levelNumber:e.levelNumber,completionPercentage:i,rewardType:h,powerUpsUsed:a.length,roll:o,rewardCalculation:r});if(w.success){this.dailyRewardTracker.recordLevelCompletion(e.levelNumber,i,c);const M=g?"ETH":"tokens";return console.log(`✅ Level ${e.levelNumber} completed! ${h.toUpperCase()}: ${c} ${M}`),{success:!0,tokensAwarded:c,newBalance:this.playerBalance,rewardType:h,rewardCalculation:r,transaction:w.transaction}}else return console.error("Failed to award level completion tokens:",w),{success:!1,reason:w.reason,message:"Failed to process token award"}}async syncToWallet(e){if(e<=0)return{success:!1,reason:"invalid_amount"};if(e>this.playerBalance)return{success:!1,reason:"insufficient_local_balance",required:e,available:this.playerBalance};if(!this.walletConnected&&!this.debugMode)return{success:!1,reason:"wallet_not_connected",message:"Connect wallet to sync tokens"};try{if(this.playerBalance-=e,this.totalSpent+=e,this.debugMode){await this.syncWalletBalance(),this.walletBalance+=e,typeof localStorage<"u"&&localStorage.setItem("debugWalletBalance",this.walletBalance.toString());const a={id:this.generateTransactionId(),type:"wallet_sync",amount:e,reason:"local_to_wallet_sync",timestamp:Date.now(),balanceAfter:this.playerBalance,metadata:{walletBalanceAfter:this.walletBalance,source:"local_sync"}};return this.addTransaction(a),this.saveToStorage(),this.triggerBalanceUpdate(),{success:!0,amount:e,newLocalBalance:this.playerBalance,newWalletBalance:this.walletBalance}}console.log("Simulating blockchain transfer of",e,"tokens to wallet");const t=this.generateTransactionId(),s={id:t,type:"wallet_sync",amount:e,reason:"local_to_wallet_sync",timestamp:Date.now(),balanceAfter:this.playerBalance,metadata:{transactionId:t,source:"blockchain_sync"}};return this.addTransaction(s),this.saveToStorage(),this.triggerBalanceUpdate(),await this.syncWalletBalance(),{success:!0,amount:e,newLocalBalance:this.playerBalance,newWalletBalance:this.walletBalance,transactionId:t}}catch(t){return console.error("Failed to sync tokens to wallet:",t),this.playerBalance+=e,this.totalSpent-=e,{success:!1,reason:"sync_failed",error:t.message,message:"Failed to sync tokens to wallet"}}}getStatistics(){const e=this.totalEarned-this.totalSpent,t=this.performanceMetrics.levelsCompleted>0?this.totalEarned/this.performanceMetrics.levelsCompleted:0;return{currentBalance:this.playerBalance,totalEarned:this.totalEarned,totalSpent:this.totalSpent,netProfit:e,transactionCount:this.transactionHistory.length,averageEarningPerLevel:Math.floor(t),performanceMetrics:{...this.performanceMetrics},dailyProgress:this.dailyRewardTracker.getDailyProgress()}}getRecentTransactions(e=10){return this.transactionHistory.slice(-e).reverse()}addTransaction(e){this.transactionHistory.push(e),this.transactionHistory.length>this.maxHistorySize&&this.transactionHistory.shift()}generateTransactionId(){return`tx_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}updatePerformanceMetrics(e,t){if(e==="level_completion"){if(this.performanceMetrics.levelsCompleted++,t.score&&(this.performanceMetrics.totalScore+=t.score),t.completionTime){const s=this.performanceMetrics.averageCompletionTime,a=this.performanceMetrics.levelsCompleted;this.performanceMetrics.averageCompletionTime=(s*(a-1)+t.completionTime)/a}t.bonuses&&(t.bonuses.perfect&&this.performanceMetrics.perfectCompletions++,t.bonuses.speed&&this.performanceMetrics.speedBonuses++,t.bonuses.accuracy&&this.performanceMetrics.accuracyBonuses++)}}addPendingReward(e,t){const s={id:this.generateTransactionId(),amount:e,reason:t,timestamp:Date.now(),displayed:!1};this.pendingRewards.push(s)}getPendingRewards(){return this.pendingRewards.filter(e=>!e.displayed)}markRewardDisplayed(e){const t=this.pendingRewards.find(a=>a.id===e);t&&(t.displayed=!0);const s=Date.now()-1e4;this.pendingRewards=this.pendingRewards.filter(a=>!a.displayed||a.timestamp>s)}updateRewardAnimations(e){for(let s=this.rewardAnimations.length-1;s>=0;s--){const a=this.rewardAnimations[s];a.elapsed+=e,a.elapsed>=a.duration&&this.rewardAnimations.splice(s,1)}const t=this.getPendingRewards();for(const s of t)this.rewardAnimations.push({id:s.id,amount:s.amount,reason:s.reason,startTime:Date.now(),elapsed:0,duration:3e3,startY:100,endY:50,alpha:1}),this.markRewardDisplayed(s.id)}render(e,t){this.updateRewardAnimations(t)}renderTokenBalance(e){const t=e.canvas.width-20,s=80,a=160,i=40,n=t-a-10,r=s-i/2,o=e.createLinearGradient(n,r,n+a,r+i);o.addColorStop(0,"rgba(30, 0, 46, 0.8)"),o.addColorStop(1,"rgba(10, 0, 30, 0.8)"),e.fillStyle=o,e.fillRect(n,r,a,i),e.strokeStyle="#ff00ff",e.lineWidth=2,e.shadowColor="#ff00ff",e.shadowBlur=15,e.strokeRect(n,r,a,i),e.strokeStyle="rgba(255, 0, 255, 0.3)",e.lineWidth=1,e.strokeRect(n+5,r+5,a-10,i-10),e.shadowBlur=0;const c=12,h=n+15,d=r+i/2;e.fillStyle="#ff00ff",e.shadowColor="#ff00ff",e.shadowBlur=10,e.beginPath();for(let w=0;w<6;w++){const v=Math.PI/3*w,p=h+c*Math.cos(v),f=d+c*Math.sin(v);w===0?e.moveTo(p,f):e.lineTo(p,f)}e.closePath(),e.fill(),e.shadowBlur=0,e.fillStyle="#ff00ff",e.font='bold 16px "Courier New", monospace',e.textAlign="right",e.shadowColor="#ff00ff",e.shadowBlur=8;const g=this.getBalanceDisplay?this.getBalanceDisplay():`WISH: ${this.playerBalance}`;e.fillText(g,t-5,s+5),e.shadowBlur=0}formatRewardReason(e){return{level_completion:"Level Complete",speed_bonus:"Speed Bonus",accuracy_bonus:"Accuracy Bonus",perfect_bonus:"Perfect Clear",daily_bonus:"Daily Bonus",achievement:"Achievement"}[e]||e}reset(){this.playerBalance=0,this.totalEarned=0,this.totalSpent=0,this.transactionHistory=[],this.pendingRewards=[],this.rewardAnimations=[],this.performanceMetrics={levelsCompleted:0,totalScore:0,averageCompletionTime:0,perfectCompletions:0,speedBonuses:0,accuracyBonuses:0}}triggerBalanceUpdate(){this.onBalanceUpdateCallback&&this.onBalanceUpdateCallback(this.playerBalance)}triggerTransaction(e){this.onTransactionCallback&&this.onTransactionCallback(e)}triggerRewardEarned(e,t,s){this.onRewardEarnedCallback&&this.onRewardEarnedCallback(e,t,s)}setOnBalanceUpdate(e){this.onBalanceUpdateCallback=e}setOnTransaction(e){this.onTransactionCallback=e}setOnRewardEarned(e){this.onRewardEarnedCallback=e}logCostCalculation(e,t={}){this.debugMode&&this.playerBalance}trackTransaction(e){this.debugMode}logValidation(e,t){this.debugMode}getCostCalculationMetrics(){return{recentTransactions:this.getRecentTransactions(5).map(e=>({type:e.type,amount:e.amount,reason:e.reason,timestamp:e.timestamp})),currentBalance:this.playerBalance,performanceMetrics:{...this.performanceMetrics}}}getTransactionMetrics(){const e=this.transactionHistory.filter(s=>s.type==="earned"),t=this.transactionHistory.filter(s=>s.type==="spent");return{totalTransactions:this.transactionHistory.length,earnedTransactions:e.length,spentTransactions:t.length,averageEarnAmount:e.length>0?e.reduce((s,a)=>s+a.amount,0)/e.length:0,averageSpendAmount:t.length>0?t.reduce((s,a)=>s+a.amount,0)/t.length:0}}getAllPerformanceMetrics(){return{tokenMetrics:this.getStatistics(),transactionMetrics:this.getTransactionMetrics(),costCalculationMetrics:this.getCostCalculationMetrics(),systemState:{debugMode:this.debugMode,pendingRewards:this.pendingRewards.length,activeAnimations:this.rewardAnimations.length}}}setDebugMode(e){this.debugMode=e}async displayHouseRatioForDev(){if(this.walletAddress==="******************************************")try{const e=await this.getGlobalTreasuryStatus();this.showDevDashboard({currentRatio:e.currentRatio,tier:e.tier,inflow:e.inflow,outflow:e.outflow,rewardsPaused:e.rewardsPaused,scalingRatio:e.scalingRatio,inflowBreakdown:e.inflowBreakdown,outflowBreakdown:e.outflowBreakdown})}catch(e){console.error("Failed to display dev dashboard:",e)}}showDevDashboard(e){var a,i;let t=document.getElementById("dev-treasury-dashboard");t||(t=document.createElement("div"),t.id="dev-treasury-dashboard",t.style.cssText=`
                position: fixed;
                top: 10px;
                right: 10px;
                background: rgba(0, 0, 0, 0.9);
                color: #00ff00;
                padding: 15px;
                border-radius: 8px;
                font-family: 'Courier New', monospace;
                font-size: 12px;
                z-index: 10000;
                min-width: 300px;
                border: 2px solid #00ff00;
                box-shadow: 0 0 20px rgba(0, 255, 0, 0.3);
            `,document.body.appendChild(t));const s=e.rewardsPaused?"#ff0000":e.currentRatio>=1.25?"#00ff00":e.currentRatio>=1.15?"#ffff00":e.currentRatio>=1.05?"#ff8800":"#ff0000";t.innerHTML=`
            <div style="text-align: center; margin-bottom: 10px; color: #00ffff;">
                <strong>🏦 TREASURY MONITOR 🏦</strong>
            </div>
            <div style="margin-bottom: 8px;">
                <strong>Ratio:</strong> <span style="color: ${s}; font-weight: bold;">
                    ${e.currentRatio.toFixed(3)}
                </span>
            </div>
            <div style="margin-bottom: 8px;">
                <strong>Tier:</strong> <span style="color: ${s};">
                    ${e.tier} (${Math.round(e.scalingRatio*100)}%)
                </span>
            </div>
            <div style="margin-bottom: 8px;">
                <strong>Status:</strong> <span style="color: ${e.rewardsPaused?"#ff0000":"#00ff00"};">
                    ${e.rewardsPaused?"🚫 GALAXY MINED OUT":"✅ ACTIVE"}
                </span>
            </div>
            <div style="margin-bottom: 8px;">
                <strong>Inflow:</strong> <span style="color: #00ff00;">${e.inflow.toLocaleString()}</span>
            </div>
            <div style="margin-bottom: 8px;">
                <strong>Outflow:</strong> <span style="color: #ff8800;">${e.outflow.toLocaleString()}</span>
            </div>
            <div style="margin-bottom: 8px; font-size: 10px; color: #888;">
                Reality Warps: ${((a=e.inflowBreakdown)==null?void 0:a.realityWarps)||0} |
                Level Rewards: ${((i=e.outflowBreakdown)==null?void 0:i.levelRewards)||0}
            </div>
            <div style="text-align: center; margin-top: 10px; font-size: 10px; color: #666;">
                Dev Wallet Active
            </div>
        `}hideDevDashboard(){const e=document.getElementById("dev-treasury-dashboard");e&&e.remove()}async checkAndShowDevDashboard(){this.walletAddress==="******************************************"?(await this.displayHouseRatioForDev(),this.devDashboardInterval||(this.devDashboardInterval=setInterval(()=>{this.displayHouseRatioForDev()},3e4))):(this.hideDevDashboard(),this.devDashboardInterval&&(clearInterval(this.devDashboardInterval),this.devDashboardInterval=null))}loadFromStorage(){try{if(typeof localStorage>"u")return;const e=localStorage.getItem("tokenEconomyManager");if(!e)return;const t=JSON.parse(e);this.playerBalance=t.playerBalance||0,this.totalEarned=t.totalEarned||0,this.totalSpent=t.totalSpent||0,this.transactionHistory=t.transactionHistory||[],this.performanceMetrics={...this.performanceMetrics,...t.performanceMetrics||{}},console.log(`Loaded token data: ${this.playerBalance} WISH tokens`)}catch(e){console.warn("Failed to load token data from localStorage:",e)}}saveToStorage(){try{if(typeof localStorage>"u")return;const e={playerBalance:this.playerBalance,totalEarned:this.totalEarned,totalSpent:this.totalSpent,transactionHistory:this.transactionHistory.slice(-this.maxHistorySize),performanceMetrics:this.performanceMetrics};localStorage.setItem("tokenEconomyManager",JSON.stringify(e))}catch(e){console.warn("Failed to save token data to localStorage:",e)}}exportDebugData(){return{balance:this.playerBalance,totals:{earned:this.totalEarned,spent:this.totalSpent},recentTransactions:this.getRecentTransactions(10),performanceMetrics:{...this.performanceMetrics},pendingRewards:[...this.pendingRewards],activeAnimations:[...this.rewardAnimations]}}logSystemState(){this.debugMode}getRealityWarpCost(){return 25e3}applySessionDiscount(e,t=0){const s=Math.min(.5,t*.1),a=e*(1-s);return this.debugMode,Math.floor(a)}applyPerformanceMultiplier(e,t){const s=Math.max(.5,1-(t.accuracy-.5)*.5),a=Math.max(.7,1-t.score/5e4*.3),i=Math.max(.8,1-t.enemiesDefeated/100*.2),n=s*a*i,r=e*n;return this.debugMode,Math.floor(r)}getRealityWarpFinalCost(){const e=this.getRealityWarpCost(),t={cost:e,reason:"fixed_cost",note:"Reality Warp has fixed cost of 25000 WISH Tokens"};return this.logCostCalculation("reality_warp",e,t),{cost:e,details:t}}validateTokenBalance(e){const t=this.canAfford(e),s={isValid:t,reason:t?"valid":"insufficient_balance",message:t?"Sufficient tokens for operation":`Insufficient tokens. Required: ${e}, Available: ${this.playerBalance}`,required:e,available:this.playerBalance};return this.logValidation("Token Balance",s),s}processWarpTransaction(e){const t=this.validateTokenBalance(e);if(!t.isValid)return{success:!1,reason:t.reason,message:t.message,validation:t};const s={balance:this.playerBalance,totalSpent:this.totalSpent},a=this.spendTokens(e,"reality_warp",{warpType:"reality_warp",costBreakdown:e,note:"Reality Warp transaction"});if(!a.success)return{success:!1,reason:a.reason,message:"Failed to process token transaction"};const i={id:a.transaction.id,type:"warp",warpType:"reality_warp",cost:e,timestamp:Date.now(),balanceAfter:this.playerBalance,metadata:{preTransactionBalance:s.balance,validation:t}};return this.debugMode,{success:!0,transaction:i,newBalance:this.playerBalance}}refundWarpTransaction(e,t="warp_refund"){if(e<=0)return{success:!1,reason:"invalid_amount",message:"Refund amount must be positive"};const s=this.awardTokens(e,"warp_refund_reality_warp",{originalCost:e,refundReason:t,warpType:"reality_warp"});return s.success?{success:!0,transaction:s.transaction,newBalance:this.playerBalance,refundedAmount:e}:{success:!1,reason:s.reason,message:"Failed to process token refund"}}rollbackTransaction(e){if(e.type==="spent")return this.refundWarpTransaction(e.amount,"transaction_rollback");if(e.type==="earned"){const t=this.spendTokens(e.amount,"rollback_earned",{originalTransactionId:e.id});return t.success?{success:!0,transaction:t.transaction,newBalance:this.playerBalance}:{success:!1,reason:t.reason,message:"Failed to rollback earned transaction"}}return{success:!1,reason:"invalid_transaction_type",message:"Cannot rollback transaction type: "+e.type}}getWarpTransactionHistory(){return this.transactionHistory.filter(e=>e.type==="spent"&&e.reason==="reality_warp").reverse()}getWarpCostStatistics(){const e=this.getWarpTransactionHistory();if(e.length===0)return{totalWarps:0,averageCost:0,totalSpent:0,mostExpensive:0,leastExpensive:0};const t=e.map(a=>a.amount),s=t.reduce((a,i)=>a+i,0);return{totalWarps:e.length,averageCost:Math.floor(s/e.length),totalSpent:s,mostExpensive:Math.max(...t),leastExpensive:Math.min(...t)}}getTransactionHistory(e=20){return this.transactionHistory.slice(-e).reverse()}deductTokens(e){return this.spendTokens(e,"reality_warp",{warpType:"reality_warp"})}async sendFromHotWallet(e,t){if(e<=0)return{success:!1,reason:"invalid_amount"};if(!this.walletConnected&&!this.debugMode)return{success:!1,reason:"wallet_not_connected",message:"Please connect your wallet to receive tokens"};try{if(this.walletProvider&&this.walletProvider.request&&!this.debugMode){console.log(`💰 Sending ${e} ETH from hot wallet ${this.hotWalletAddress} to user ${this.walletAddress}`);const a="0x"+BigInt(Math.floor(e*1e18)).toString(16),i=await this.sendTransactionFromHotWallet(this.walletAddress,e);if(i){console.log(`✅ Successfully sent ${e} ETH from hot wallet. Transaction: ${i}`),this.playerBalance+=e,this.totalEarned+=e;const n=this.generateTransactionId(),r={id:n,type:"hot_wallet_transfer",amount:e,reason:"level_completion_from_hot_wallet",timestamp:Date.now(),balanceAfter:this.playerBalance,hash:i,metadata:{sessionId:t,transactionType:"hot_wallet_to_user",source:this.hotWalletAddress,destination:this.walletAddress,blockchain:!0}};return this.addTransaction(r),this.saveToStorage(),this.triggerBalanceUpdate(),this.triggerTransaction(r),{success:!0,amount:e,newBalance:this.playerBalance,transactionId:n,blockchainHash:i}}else throw new Error("Failed to send transaction from hot wallet")}else{console.log(`🐛 Debug mode: Simulating ${e} ETH transfer from hot wallet`),this.playerBalance+=e,this.totalEarned+=e;const s=this.generateTransactionId(),a={id:s,type:"hot_wallet_transfer",amount:e,reason:"level_completion_from_hot_wallet",timestamp:Date.now(),balanceAfter:this.playerBalance,metadata:{sessionId:t,transactionType:"hot_wallet_to_user",source:this.hotWalletAddress,destination:this.walletAddress||"user_wallet",blockchain:!1,debug:!0}};return this.addTransaction(a),this.saveToStorage(),this.triggerBalanceUpdate(),this.triggerTransaction(a),{success:!0,amount:e,newBalance:this.playerBalance,transactionId:s}}}catch(s){return console.error("Failed to send tokens from hot wallet to user:",s),this.playerBalance-=e,this.totalEarned-=e,{success:!1,reason:"transfer_failed",error:s.message,message:"Failed to send tokens from hot wallet to user"}}}async sendTransactionFromHotWallet(e,t){try{console.log(`🔄 Requesting transaction from hot wallet ${this.hotWalletAddress} to ${e} via secure server API`);const s=await fetch(`${this.apiBaseUrl}/wallet/send`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer secure-token-for-development"},body:JSON.stringify({toAddress:e,amount:t.toString(),reason:"hot_wallet_transaction"})});if(!s.ok){const i=await s.json().catch(()=>({}));throw new Error(`Server API call failed: ${s.status} ${s.statusText} - ${i.error||"Unknown error"}`)}const a=await s.json();return console.log(`✅ Transaction sent from hot wallet via server: ${a.transactionHash}`),a.transactionHash}catch(s){return console.error("❌ Failed to send transaction from hot wallet via server API:",s),null}}async receiveToHotWallet(e,t,s="purchase",a={}){try{console.log(`💰 Receiving ${t} ETH to hot wallet ${this.hotWalletAddress} from ${e}`);const i=this.generateTransactionId(),n={id:i,type:"hot_wallet_receive",amount:t,reason:s,timestamp:Date.now(),balanceAfter:this.playerBalance,metadata:{fromAddress:e,toAddress:this.hotWalletAddress,reason:s,...a}};return this.addTransaction(n),this.saveToStorage(),this.triggerTransaction(n),console.log(`✅ Recorded ${t} ETH received to hot wallet from ${e}`),{success:!0,amount:t,transactionId:i,hotWalletAddress:this.hotWalletAddress}}catch(i){return console.error("Failed to record hot wallet receive:",i),{success:!1,reason:"receive_failed",error:i.message,message:"Failed to record hot wallet receive"}}}async makeAuthenticatedApiCall(e,t="GET",s=null){try{const a=`${this.apiBaseUrl}${e}`,i={method:t,headers:{"Content-Type":"application/json",Authorization:"Bearer secure-token-for-development"}};s&&(i.body=JSON.stringify(s));const n=await fetch(a,i);if(!n.ok){const r=await n.json().catch(()=>({}));throw new Error(`API call failed: ${n.status} ${n.statusText} - ${r.error||"Unknown error"}`)}return await n.json()}catch(a){throw console.error(`API call to ${e} failed:`,a),a}}}class Qe{constructor(e){this.tokenManager=e,this.isTestMode=!1,this.originalGetBalance=null,this.originalSpendTokens=null,this.originalAwardTokens=null,this.originalCanAfford=null,this.originalGetBalanceDisplay=null}async enableTestMode(){if(!this.isTestMode){console.log("🧪 Enabling ETH Test Mode...");try{const e=window.ethereum;if(e&&e.request){console.log("🔍 Checking current chain for ETH Test Mode...");const t=await e.request({method:"eth_chainId"}),s=parseInt(t,16);console.log(`🔗 Current Chain ID: ${s}`),s===31337?console.log("✅ Connected to Hardhat local development network (Chain ID: 31337)"):s===1337?console.log("✅ Connected to Ganache local development network (Chain ID: 1337)"):s===1?(console.warn("⚠️ Connected to Ethereum Mainnet (Chain ID: 1) - ETH Test Mode expects local development network"),console.warn("⚠️ Please switch to Chain ID 31337 (Hardhat) or 1337 (Ganache) for proper testing")):console.log(`⚠️ Connected to network with Chain ID: ${s} (expected 31337 or 1337 for local development)`),this.tokenManager.walletConnected||(console.log("🔄 Wallet not connected, attempting to initialize..."),this.tokenManager.initializeWallet()),this.tokenManager.walletConnected?(console.log("🔄 Syncing wallet balance for ETH Test Mode..."),await this.tokenManager.syncWalletBalance(),console.log("✅ Wallet balance synced:",this.tokenManager.getWalletBalance(),"ETH")):console.warn("⚠️ Wallet still not connected after initialization attempt")}else if(this.tokenManager.debugMode)console.log("🐛 Debug mode: Enabling ETH Test Mode without MetaMask"),console.log("🐛 Using debug wallet balance for ETH Test Mode"),this.tokenManager.walletConnected||(console.log("🔄 Debug wallet not connected, attempting to initialize..."),this.tokenManager.initializeWallet()),this.tokenManager.walletConnected?(console.log("🔄 Syncing debug wallet balance for ETH Test Mode..."),await this.tokenManager.syncWalletBalance(),console.log("✅ Debug wallet balance synced:",this.tokenManager.getWalletBalance(),"ETH")):console.warn("⚠️ Debug wallet still not connected after initialization attempt");else{console.warn("⚠️ MetaMask not detected - ETH Test Mode requires MetaMask connection via OrangeID"),console.warn("⚠️ Please connect your wallet using OrangeID first");return}}catch(e){console.error("❌ Failed to verify chain for ETH Test Mode:",e),console.log("❌ Not enabling ETH Test Mode due to verification failure");return}this.isTestMode=!0,console.log("✅ ETH Test Mode enabled"),this.originalGetBalance=this.tokenManager.getBalance,this.originalCanAfford=this.tokenManager.canAfford,this.originalGetBalanceDisplay=this.tokenManager.getBalanceDisplay,this.tokenManager.getBalance=()=>{const e=this.tokenManager.getWalletBalance();return console.log(`🧪 Test Mode: Returning ETH balance ${e} instead of WISH balance`),e},this.tokenManager.getBalanceDisplay=()=>{const t=`${this.tokenManager.getWalletBalance().toFixed(10)} ETH (Test Mode)`;return console.log(`🧪 Test Mode Balance Display: ${t}`),t},this.tokenManager.canAfford=async e=>this.tokenManager.getWalletBalance()>=e,console.log("✅ ETH Test Mode: Balance checks now use ETH instead of WISH tokens"),console.log("✅ ETH Test Mode: Transactions will process real ETH via wallet provider"),console.log("✅ ETH Test Mode enabled - using ETH balance as WISH tokens")}}disableTestMode(){this.isTestMode&&(this.isTestMode=!1,this.originalGetBalance&&(this.tokenManager.getBalance=this.originalGetBalance),this.originalCanAfford&&(this.tokenManager.canAfford=this.originalCanAfford),this.originalGetBalanceDisplay&&(this.tokenManager.getBalanceDisplay=this.originalGetBalanceDisplay),console.log("ETH Test Mode disabled - restored WISH token system"))}applyTestModeDiscount(e){return Math.round(e*.1)}toggleTestMode(){return this.isTestMode?this.disableTestMode():this.enableTestMode(),this.isTestMode}getTestModeStatus(){return this.isTestMode}}class Ze{constructor(e=null,t=null,s=null){this.tokenEconomyManager=e,this.llmClient=null,this.levelManager=s,this.pendingEnvironment=null,this.warpState={status:"ready",cooldownEndTime:0,lastWarpTime:0,warpCount:0,maxWarpsPerSession:3},this.debugMode=E.DEBUG_MODE,this.logEnabled=E.ENABLE_CONSOLE_LOGS,this.performanceMetricsEnabled=!0,this.costCalculationLogging=!0,this.transactionLogging=!0,this.costCalculationMetrics={totalCalculations:0,averageCalculationTime:0,calculationTimes:[],lastCalculationTime:0},this.transactionMetrics={totalTransactions:0,successfulTransactions:0,failedTransactions:0,lastTransactionTime:0},this.warpConfiguration={id:"reality_warp",name:"Reality Warp",description:"Transform the next level with your imagination and reshape the battlefield",cost:25e3,complexity:2,cooldown:0,effects:["enemy_freeze","weapon_enhance","shield_regenerate"]},this.currentWarp={type:null,startTime:0,effects:[],cost:0,generatedPrompt:null},this.warpHistory=[],this.maxHistorySize=50,this.performanceMetrics={totalWarps:0,successfulWarps:0,failedWarps:0,totalCost:0,averageEffectiveness:0,effectivenessScores:[]},this.onWarpStartCallback=null,this.onWarpEndCallback=null,this.onWarpCooldownStartCallback=null,this.onWarpCooldownEndCallback=null,this.onWarpCostCalculatedCallback=null,this.onPromptGeneratedCallback=null,this.debugMode=E.DEBUG_MODE,this.logEnabled=E.ENABLE_CONSOLE_LOGS}init(e={}){try{return e.warpConfiguration&&(this.warpConfiguration={...this.warpConfiguration,...e.warpConfiguration}),e.maxWarpsPerSession&&(this.warpState.maxWarpsPerSession=e.maxWarpsPerSession),this.tokenEconomyManager||console.warn("TokenEconomyManager not provided - cost calculations will be limited"),this.levelManager||console.warn("LevelManager not provided - level coordination will be limited"),this.warpState.status="ready",this.warpState.cooldownEndTime=0,!0}catch(t){return console.error("Failed to initialize RealityWarpManager:",t),!1}}canWarp(){try{const e=this.warpConfiguration;if(!e)return{canWarp:!1,reason:"invalid_warp_configuration",message:"Invalid warp configuration"};if(this.warpState.status==="disabled")return{canWarp:!1,reason:"system_disabled",message:"Reality warp system is currently disabled"};if(this.warpState.status==="cooldown")if(this.levelManager&&this.levelManager.currentLevel>this.warpState.cooldownLevel)this.warpState.status="ready",this.warpState.cooldownLevel=0;else return{canWarp:!1,reason:"on_cooldown",message:"On cooldown until next level"};if(this.warpState.status==="active")return{canWarp:!1,reason:"already_active",message:"Reality warp is already active"};if(this.warpState.warpCount>=this.warpState.maxWarpsPerSession)return{canWarp:!1,reason:"session_limit_reached",message:`Maximum warps per session reached (${this.warpState.maxWarpsPerSession})`};const t=this.warpConfiguration.cost;return this.tokenEconomyManager&&!this.tokenEconomyManager.canAfford(t)?{canWarp:!1,reason:"insufficient_tokens",message:"Insufficient tokens",required:t,available:this.tokenEconomyManager.getBalance()}:{canWarp:!0,reason:"eligible",message:"Can perform warp",cost:t,warpConfig:e}}catch(e){return console.error("Error checking warp eligibility:",e),{canWarp:!1,reason:"error",message:"Error checking warp eligibility"}}}getFixedCost(){return this.warpConfiguration.cost}getFallbackCost(){return this.warpConfiguration.cost}applySessionDiscount(e,t=0){return e}applyPerformanceMultiplier(e,t={}){return e}getFixedCost(){return this.warpConfiguration.cost}calculateCost(e={}){const t=this.warpConfiguration.cost,s={cost:t,reason:"fixed_cost",complexity:this.warpConfiguration.complexity};return this.logCostCalculation(t,s),this.onWarpCostCalculatedCallback&&this.onWarpCostCalculatedCallback(t,e),t}validateTokenBalance(e){try{return this.tokenEconomyManager?this.tokenEconomyManager.validateTokenBalance(e):{isValid:!1,reason:"token_manager_unavailable",message:"Token economy manager not available"}}catch(t){return console.error("Error validating token balance:",t),{isValid:!1,reason:"validation_error",message:"Error validating token balance"}}}deductTokens(e){try{return this.tokenEconomyManager?this.tokenEconomyManager.deductTokens(e,"reality_warp"):{success:!1,reason:"token_manager_unavailable",message:"Token economy manager not available"}}catch(t){return console.error("Error deducting tokens:",t),{success:!1,reason:"deduction_error",message:"Error deducting tokens"}}}refundTokens(e){try{return this.tokenEconomyManager?this.tokenEconomyManager.refundTokens(e,"reality_warp"):{success:!1,reason:"token_manager_unavailable",message:"Token economy manager not available"}}catch(t){return console.error("Error refunding tokens:",t),{success:!1,reason:"refund_error",message:"Error refunding tokens"}}}getTransactionHistory(e=20){return this.tokenEconomyManager?this.tokenEconomyManager.getTransactionHistory(e):[]}async executeWarp(e={}){try{const t=this.canWarp();if(!t.canWarp)return{success:!1,reason:t.reason,message:t.message};const s=this.warpConfiguration;if(!s)return{success:!1,reason:"invalid_warp_configuration",message:"Invalid warp configuration"};const a=t.cost||this.calculateCost(),i=this.validateTokenBalance(a);if(!i.isValid)return{success:!1,reason:i.reason,message:i.message};const n=this.deductTokens(a);if(!n.success)return{success:!1,reason:n.reason,message:n.message};await this.recordPurchaseInflow(a,"realityWarps"),this.warpState.status="active",this.warpState.warpCount++,this.warpState.lastWarpTime=Date.now();const r=e.userIdea||null;if(this.currentWarp={type:"reality_warp",startTime:Date.now(),effects:s.effects,cost:a,generatedPrompt:null,userIdea:r},r)this.currentWarp.generatedPrompt=r;else throw new Error("User idea is required for reality warp generation");if(this.logEnabled,this.onWarpStartCallback&&this.onWarpStartCallback("reality_warp",{cost:a,effects:s.effects,userIdea:r,generatedPrompt:this.currentWarp.generatedPrompt}),r)try{const o=await fetch(D.getApiUrl("/api/generate-environment"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({environmentDescription:r})});if(!o.ok)throw new Error(`HTTP error! status: ${o.status}`);const c=o.headers.get("content-type");let h;if(c&&c.includes("text/event-stream")){const g=o.body.getReader(),w=new TextDecoder;let v="";for(;;){const{done:p,value:f}=await g.read();if(p)break;v+=w.decode(f,{stream:!0});const y=v.split(`
`);v=y.pop();for(const M of y)if(M.startsWith("data: "))try{const S=JSON.parse(M.slice(6));if(S.status!=="retrying"){if(S.status==="complete"){h=S.result;break}}}catch{}if(h)break}g.releaseLock()}else h=await o.json();const d=await this.applyGeneratedEnvironment(h);if(!d.success)throw new Error(`Failed to apply environment: ${d.message}`)}catch(o){throw console.error("❌ [REALITY WARP DEBUG] Failed to generate environment from user idea:",o),new Error(`Environment generation failed: ${o.message}`)}return this.recordWarp("reality_warp",a,Date.now(),"success"),this.performanceMetrics.totalWarps++,this.performanceMetrics.successfulWarps++,this.performanceMetrics.totalCost+=a,{success:!0,warpType:"reality_warp",cost:a,effects:s.effects,userIdea:r,warpData:{type:"reality_warp",cost:a,startTime:this.currentWarp.startTime,effects:s.effects,generatedPrompt:this.currentWarp.generatedPrompt,userIdea:r}}}catch(t){return console.error("Error executing warp:",t),this.recordWarp("reality_warp",0,Date.now(),"failed"),this.performanceMetrics.totalWarps++,this.performanceMetrics.failedWarps++,{success:!1,reason:"execution_error",message:"Error executing warp",error:t.message}}}async applyGeneratedEnvironment(e){try{const t={...e,imageData:e.imageData||{images:[]}};if(t.imageData&&t.imageData.images&&t.imageData.images[0]){const a=t.imageData.images[0],i=a.localUrl||a.url,n=D.getImageUrl(i);t.imageData.images[0]={...a,url:i,localUrl:n}}const s={...t,type:e.type||"custom",name:e.name||this.currentWarp.userIdea||"Custom Environment",description:e.description||this.currentWarp.userIdea||"Custom Environment",gameplayModifiers:e.gameplayModifiers||{},imageData:t.imageData||null};if(s.imageData&&s.imageData.images&&s.imageData.images[0]){const a=s.imageData.images[0],i=a.localUrl||a.url;s.imageUrl=D.getImageUrl(i)}if(this.pendingEnvironment=s,this.gameEngine&&this.gameEngine.environmentTracker)try{const a=this.getCurrentUserId(),i=s.imageFileName||`env_${Date.now()}.jpg`,n=await this.gameEngine.environmentTracker.trackEnvironment(s,a,i);console.log(`Environment tracked for marketplace: ${n}`)}catch(a){console.warn("Failed to track environment for marketplace:",a)}return{success:!0,message:"Environment data stored for next level",willApplyOnLevelStart:!0}}catch(t){return console.error("❌ [REALITY WARP DEBUG] Error storing generated environment:",t),{success:!1,reason:"storage_error",message:"Error storing generated environment",error:t.message}}}getAndClearPendingEnvironment(){try{if(this.pendingEnvironment){const e={...this.pendingEnvironment};return this.pendingEnvironment=null,e}else return null}catch(e){return console.error("❌ [REALITY WARP DEBUG] Error retrieving pending environment:",e),null}}endWarp(){try{if(this.warpState.status!=="active")return{success:!1,reason:"not_active",message:"No active warp to end"};if(!this.warpConfiguration)return{success:!1,reason:"invalid_warp_configuration",message:"Invalid warp configuration"};const t=this.calculateWarpEffectiveness();return this.updatePerformanceMetrics(t),this.warpState.status="cooldown",this.warpState.cooldownLevel=this.levelManager?this.levelManager.currentLevel:0,this.logEnabled,this.onWarpEndCallback&&this.onWarpEndCallback("reality_warp",{effectiveness:t}),this.onWarpCooldownStartCallback&&this.onWarpCooldownStartCallback("reality_warp"),this.currentWarp={type:null,startTime:0,effects:[],cost:0,generatedPrompt:null},{success:!0,warpType:"reality_warp",effectiveness:t}}catch(e){return console.error("Error ending warp:",e),{success:!1,reason:"end_error",message:"Error ending warp",error:e.message}}}endCooldown(){try{return this.warpState.status!=="cooldown"?{success:!1,reason:"not_on_cooldown",message:"Not currently on cooldown"}:(this.warpState.status="ready",this.logEnabled,this.onWarpCooldownEndCallback&&this.onWarpCooldownEndCallback(),{success:!0,message:"Cooldown ended"})}catch(e){return console.error("Error ending cooldown:",e),{success:!1,reason:"cooldown_end_error",message:"Error ending cooldown",error:e.message}}}generateWarpPrompt(e={}){try{throw new Error("generateWarpPrompt is deprecated. Use backend API for prompt generation.")}catch(t){throw console.error("Error in deprecated generateWarpPrompt method:",t),t}}getCurrentDifficulty(){if(!this.levelManager)return"normal";const e=this.levelManager.currentLevel||1;return e<=3?"easy":e<=7?"normal":e<=12?"hard":"extreme"}getCurrentUserId(){return this.gameEngine&&this.gameEngine.orangeSDKManager&&this.gameEngine.orangeSDKManager.currentUser?this.gameEngine.orangeSDKManager.currentUser.id:(localStorage.getItem("sessionUserId")||localStorage.setItem("sessionUserId",`user_${Date.now()}_${Math.random().toString(36).substr(2,9)}`),localStorage.getItem("sessionUserId"))}logCostCalculation(e,t={}){this.costCalculationLogging&&(this.warpState.warpCount,this.warpState.status,this.costCalculationMetrics.totalCalculations++,this.costCalculationMetrics.lastCalculationTime=Date.now())}trackTransaction(e){this.transactionLogging&&(this.transactionMetrics.totalTransactions++,this.transactionMetrics.lastTransactionTime=Date.now(),e.success?this.transactionMetrics.successfulTransactions++:this.transactionMetrics.failedTransactions++,this.debugMode)}getCostCalculationMetrics(){const e=this.costCalculationMetrics.calculationTimes,t=e.length>0?e.reduce((s,a)=>s+a,0)/e.length:0;return{totalCalculations:this.costCalculationMetrics.totalCalculations,averageCalculationTime:Math.round(t),lastCalculationTime:this.costCalculationMetrics.lastCalculationTime,calculationTimes:e.slice(-10)}}getTransactionMetrics(){const e=this.transactionMetrics.totalTransactions>0?this.transactionMetrics.successfulTransactions/this.transactionMetrics.totalTransactions*100:0;return{totalTransactions:this.transactionMetrics.totalTransactions,successfulTransactions:this.transactionMetrics.successfulTransactions,failedTransactions:this.transactionMetrics.failedTransactions,successRate:Math.round(e),lastTransactionTime:this.transactionMetrics.lastTransactionTime}}getAllPerformanceMetrics(){return{costCalculations:this.getCostCalculationMetrics(),transactions:this.getTransactionMetrics(),warpPerformance:this.getPerformanceStatistics(),systemState:{debugMode:this.debugMode,loggingEnabled:this.logEnabled,performanceTracking:this.performanceMetricsEnabled}}}setDebugMode(e){this.debugMode=e}setCostCalculationLogging(e){this.costCalculationLogging=e}setTransactionLogging(e){this.transactionLogging=e}setPerformanceMetricsTracking(e){this.performanceMetricsEnabled=e}getWarpState(){return{status:this.warpState.status,warpCount:this.warpState.warpCount,maxWarpsPerSession:this.warpState.maxWarpsPerSession,currentWarp:this.currentWarp.type?{...this.currentWarp}:null,timeRemaining:this.getTimeRemaining(),performanceMetrics:{...this.performanceMetrics}}}getTimeRemaining(){return this.warpState.status==="active"?{type:"active",remaining:1/0,percentage:100}:this.warpState.status==="cooldown"?{type:"cooldown",remaining:"Until next level",percentage:0}:{type:"ready",remaining:0,percentage:0}}reset(){try{this.warpState={status:"ready",cooldownEndTime:0,cooldownLevel:0,lastWarpTime:0,warpCount:0,maxWarpsPerSession:3},this.currentWarp={type:null,startTime:0,effects:[],cost:0,generatedPrompt:null},this.warpHistory=[],this.performanceMetrics={totalWarps:0,successfulWarps:0,failedWarps:0,totalCost:0,averageEffectiveness:0,effectivenessScores:[]}}catch(e){console.error("Error resetting RealityWarpManager:",e)}}recordWarp(e,t,s){const a={id:`warp_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,type:"reality_warp",cost:e,timestamp:t,status:s,level:this.levelManager?this.levelManager.currentLevel:1};this.warpHistory.push(a),this.warpHistory.length>this.maxHistorySize&&this.warpHistory.shift()}calculateWarpEffectiveness(){const t=this.levelManager?Math.min(25,(this.levelManager.currentLevel-1)*2):0,s=this.warpState.warpCount===1?10:0;return Math.min(100,75+t+s)}updatePerformanceMetrics(e){this.performanceMetrics.effectivenessScores.push(e),this.performanceMetrics.effectivenessScores.length>10&&this.performanceMetrics.effectivenessScores.shift();const t=this.performanceMetrics.effectivenessScores.reduce((s,a)=>s+a,0);this.performanceMetrics.averageEffectiveness=t/this.performanceMetrics.effectivenessScores.length}getPlayerPerformanceContext(){return this.levelManager?{score:this.levelManager.currentScore,accuracy:this.levelManager.getAccuracy?this.levelManager.getAccuracy():0,enemiesDefeated:this.levelManager.enemiesDefeated||0,levelProgress:this.levelManager.getLevelProgress?this.levelManager.getLevelProgress():0}:{score:0,accuracy:0,enemiesDefeated:0}}getWarpConfiguration(){return this.warpConfiguration}getAllWarpConfigurations(){return{...this.warpConfigurations}}getWarpHistory(e=10){return this.warpHistory.slice(-e).reverse()}getPerformanceStatistics(){const e=this.performanceMetrics.totalWarps>0?this.performanceMetrics.successfulWarps/this.performanceMetrics.totalWarps*100:0,t=this.performanceMetrics.successfulWarps>0?this.performanceMetrics.totalCost/this.performanceMetrics.successfulWarps:0;return{successRate:Math.round(e),totalWarps:this.performanceMetrics.totalWarps,successfulWarps:this.performanceMetrics.successfulWarps,failedWarps:this.performanceMetrics.failedWarps,totalCost:this.performanceMetrics.totalCost,averageCost:Math.round(t),averageEffectiveness:Math.round(this.performanceMetrics.averageEffectiveness),sessionWarps:this.warpState.warpCount,maxSessionWarps:this.warpState.maxWarpsPerSession}}setCallback(e,t){switch(e){case"warpStart":this.onWarpStartCallback=t;break;case"warpEnd":this.onWarpEndCallback=t;break;case"cooldownStart":this.onWarpCooldownStartCallback=t;break;case"cooldownEnd":this.onWarpCooldownEndCallback=t;break;case"costCalculated":this.onWarpCostCalculatedCallback=t;break;case"promptGenerated":this.onPromptGeneratedCallback=t;break;default:console.warn(`Unknown event type: ${e}`)}}setDebugMode(e){this.debugMode=e}setLogging(e){this.logEnabled=e}async recordPurchaseInflow(e,t){var s,a;try{const i=((s=this.tokenEconomyManager)==null?void 0:s.walletAddress)||"anonymous",n=await fetch(`${((a=this.tokenEconomyManager)==null?void 0:a.apiBaseUrl)||E.API_BASE_URL}/treasury/record-inflow`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({amount:e,category:t,userId:i,metadata:{timestamp:Date.now(),warpType:"reality_warp"}})});if(!n.ok)throw new Error(`HTTP error! status: ${n.status}`);const r=await n.json();console.log(`[RealityWarp] Recorded treasury inflow: ${e} tokens, new ratio: ${r.currentRatio}`)}catch(i){console.error("Failed to record purchase inflow:",i)}}}class et{constructor(e=null){this.environments=new Map,this.userEnvironments=new Map,this.environmentStats=new Map,e&&e.apiBaseUrl?this.serverUrl=e.apiBaseUrl.replace(/\/api$/,""):this.serverUrl=D.getApiBaseUrl(),this.loadFromServer()}calculateEnvironmentPrice(e){const s={1:5e3,2:7500,3:1e4,4:15e3,5:2e4}[e.difficulty]||1e4,a=1+e.timesUsed*.01;return Math.round(s*a)}async loadFromServer(){try{const e=await fetch(`${this.serverUrl}/api/environments`);if(e.ok){const t=await e.json();this.environments.clear(),this.userEnvironments.clear(),this.environmentStats.clear(),t.forEach(s=>{this.environments.set(s.id,s),this.userEnvironments.has(s.creatorUserId)||this.userEnvironments.set(s.creatorUserId,[]),this.userEnvironments.get(s.creatorUserId).push(s.id),this.environmentStats.set(s.id,{views:s.timesUsed||0,purchases:s.totalPurchases||0,revenue:s.totalRevenue||0,ratings:[],averageRating:0})}),console.log(`Loaded ${t.length} environments from server`)}else console.error("Failed to load environments from server:",e.statusText)}catch(e){console.error("Error loading environments from server:",e)}}async trackEnvironment(e,t,s){try{const a=await fetch(`${this.serverUrl}/api/environments`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({environmentData:e,creatorUserId:t,imageFileName:s})});if(!a.ok)return console.error("Failed to save environment to server:",a.statusText),null;const i=await a.json();return i.success?(this.environments.set(i.environmentId,i.environment),this.userEnvironments.has(t)||this.userEnvironments.set(t,[]),this.userEnvironments.get(t).push(i.environmentId),this.environmentStats.set(i.environmentId,{views:0,purchases:0,revenue:0,ratings:[],averageRating:0}),console.log(`Environment tracked: ${i.environmentId} by user ${t}`),i.environmentId):(console.error("Failed to track environment:",i.error),null)}catch(a){return console.error("Error tracking environment:",a),null}}async getEnvironment(e){try{const t=await fetch(`${this.serverUrl}/api/environments/${e}`);if(t.ok){const s=await t.json();return s.price||(s.price=this.calculateEnvironmentPrice(s)),s}else return t.status===404||console.error("Failed to get environment from server:",t.statusText),null}catch(t){return console.error("Error getting environment:",t),null}}async getUserEnvironments(e){try{const t=await fetch(`${this.serverUrl}/api/environments/user/${e}`);return t.ok?await t.json():(console.error("Failed to get user environments from server:",t.statusText),[])}catch(t){return console.error("Error getting user environments:",t),[]}}async getRandomEnvironments(e=5,t=null){try{const s=await fetch(`${this.serverUrl}/api/environments/random?count=${e}`);if(s.ok){const a=await s.json();return a.forEach(i=>{i.price||(i.price=this.calculateEnvironmentPrice(i))}),a}else return console.error("Failed to get random environments from server:",s.statusText),[]}catch(s){return console.error("Error getting random environments:",s),s instanceof TypeError&&s.message.includes("fetch")&&console.error("Network error: Could not connect to environment server. Please check if the server is running on "+this.serverUrl),[]}}async recordEnvironmentUsage(e,t){try{const s=await fetch(`${this.serverUrl}/api/environments/${e}/usage`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:t})});if(s.ok){const a=this.environments.get(e);a&&(a.timesUsed++,a.lastUsed=Date.now());const i=this.environmentStats.get(e);i&&i.views++}else console.error("Failed to record environment usage:",s.statusText)}catch(s){console.error("Error recording environment usage:",s)}}async recordEnvironmentPurchase(e,t,s,a){try{const i=Number(s);if(isNaN(i)||i<=0)return console.error(`Invalid cost for environment purchase: ${s}`),!1;if(!a||typeof a!="string"||a.length<10)return console.error(`Invalid purchaserAddress for environment purchase: ${a}`),!1;const n=await fetch(`${this.serverUrl}/api/environments/${e}/purchase`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({purchaserUserId:t,cost:i,purchaserAddress:a})});if(n.ok){const r=this.environments.get(e);r&&(r.totalPurchases++,r.totalRevenue+=i);const o=this.environmentStats.get(e);return o&&(o.purchases++,o.revenue+=i),console.log(`Environment ${e} purchased by ${t} (${a}) for ${i} tokens`),!0}else return console.error("Failed to record environment purchase:",n.statusText),!1}catch(i){return console.error("Error recording environment purchase:",i),!1}}getEnvironmentStats(e){return this.environmentStats.get(e)||null}getTopEnvironments(e=10){return Array.from(this.environments.values()).filter(t=>t.isActive).sort((t,s)=>s.timesUsed-t.timesUsed).slice(0,e)}generateEnvironmentId(){return`env_${this.nextEnvironmentId++}_${Date.now()}`}calculateDifficulty(e){let t=3;const s=e.enemyTypeModifiers||{},a=Object.values(s).filter(n=>typeof n=="number"),i=a.length>0?a.reduce((n,r)=>n+r,0)/a.length:1;return t+=(i-1)*3,Math.max(1,Math.min(5,Math.round(t)))}shuffleArray(e){for(let t=e.length-1;t>0;t--){const s=Math.floor(Math.random()*(t+1));[e[t],e[s]]=[e[s],e[t]]}return e}cleanupOldEnvironments(){if(this.environments.size<=this.maxStoredEnvironments)return;const t=Array.from(this.environments.values()).sort((s,a)=>(s.lastUsed||0)-(a.lastUsed||0)).slice(0,this.environments.size-this.maxStoredEnvironments);t.forEach(s=>{this.environments.delete(s.id),this.environmentStats.delete(s.id);const a=this.userEnvironments.get(s.creatorUserId);if(a){const i=a.indexOf(s.id);i>-1&&a.splice(i,1)}}),console.log(`Cleaned up ${t.length} old environments`)}saveToStorage(){try{const e={environments:Array.from(this.environments.entries()),userEnvironments:Array.from(this.userEnvironments.entries()),environmentStats:Array.from(this.environmentStats.entries()),nextEnvironmentId:this.nextEnvironmentId};localStorage.setItem("environmentTracker",JSON.stringify(e))}catch(e){console.warn("Failed to save environment tracker data to localStorage:",e)}}loadFromStorage(){try{const e=localStorage.getItem("environmentTracker");if(!e)return;const t=JSON.parse(e);this.environments=new Map(t.environments||[]),this.userEnvironments=new Map(t.userEnvironments||[]),this.environmentStats=new Map(t.environmentStats||[]),this.nextEnvironmentId=t.nextEnvironmentId||1,console.log(`Loaded ${this.environments.size} environments from storage`)}catch(e){console.warn("Failed to load environment tracker data from localStorage:",e)}}getSummaryStats(){const e=this.environments.size,t=this.userEnvironments.size,s=Array.from(this.environments.values()).reduce((i,n)=>i+n.timesUsed,0),a=Array.from(this.environments.values()).reduce((i,n)=>i+n.totalRevenue,0);return{totalEnvironments:e,totalUsers:t,totalUsage:s,totalRevenue:a,averageUsagePerEnvironment:e>0?s/e:0}}}class tt{constructor(e){this.tokenManager=e,this.creatorRewardPercentage=.5,this.platformFeePercentage=.1,this.remainingPercentage=.4,this.serverUrl=E.API_BASE_URL,this.pendingRewards=new Map,this.rewardHistory=new Map,this.totalRewardsDistributed=0,this.totalPlatformFees=0,this.loadFromStorage()}processCreatorReward(e,t,s,a,i={}){try{const n=Math.floor(a*this.creatorRewardPercentage),r=Math.floor(a*this.platformFeePercentage),o=a-n-r,c={id:this.generateRewardId(),creatorUserId:e,purchaserUserId:t,environmentId:s,environmentName:i.name||"Unknown Environment",purchaseAmount:a,creatorReward:n,platformFee:r,remainingAmount:o,timestamp:Date.now(),status:"pending",processedAt:null};return this.pendingRewards.has(e)||this.pendingRewards.set(e,0),this.pendingRewards.set(e,this.pendingRewards.get(e)+n),this.rewardHistory.has(e)||this.rewardHistory.set(e,[]),this.rewardHistory.get(e).push(c),this.totalPlatformFees+=r,this.saveToStorage(),console.log(`Creator reward processed: ${n} tokens for ${e}`),{success:!0,transaction:c,creatorReward:n,platformFee:r,remainingAmount:o}}catch(n){return console.error("Error processing creator reward:",n),{success:!1,error:n.message}}}async distributePendingRewards(e){try{const t=this.pendingRewards.get(e)||0;if(t<=0)return{success:!1,reason:"no_pending_rewards",message:"No pending rewards to distribute"};console.log(`💰 Requesting distribution of ${t} ETH to creator ${e} via secure server API`);const s=await fetch(`${this.serverUrl}/rewards/distribute`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer secure-token-for-development"},body:JSON.stringify({creatorUserId:e,amount:t.toString(),reason:`creator_reward_${e}`})});if(!s.ok){const i=await s.json().catch(()=>({}));throw new Error(`Server API call failed: ${s.status} ${s.statusText} - ${i.error||"Unknown error"}`)}const a=await s.json();if(a&&a.success){const i=await fetch(`${this.serverUrl}/tokens/award`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer secure-token-for-development"},body:JSON.stringify({userId:e,amount:t.toString(),reason:"creator_reward",metadata:{creatorUserId:e,rewardAmount:t,blockchainHash:a.walletTransaction.transactionHash,fromHotWallet:!0}})});let n=null;return i.ok&&(n=await i.json()),this.pendingRewards.set(e,0),(this.rewardHistory.get(e)||[]).forEach(o=>{o.status==="pending"&&(o.status="distributed",o.processedAt=Date.now(),o.blockchainHash=a.walletTransaction.transactionHash,o.distributedVia="server_api")}),this.totalRewardsDistributed+=t,this.saveToStorage(),console.log(`✅ Distributed ${t} ETH to creator ${e} via secure server API`),{success:!0,amount:t,newBalance:n?n.newBalance:this.tokenManager.getBalance(),blockchainHash:a.walletTransaction.transactionHash,transactionId:a.distributionId,serverTransactionId:a.walletTransaction.transactionId}}else throw new Error((a==null?void 0:a.message)||"Failed to distribute rewards via server API")}catch(t){return console.error("Error distributing pending rewards:",t),{success:!1,reason:"distribution_error",message:t.message}}}getPendingRewards(e){return this.pendingRewards.get(e)||0}getRewardHistory(e){return this.rewardHistory.get(e)||[]}getCreatorEarnings(e){const t=this.getRewardHistory(e),s=this.getPendingRewards(e),a=t.reduce((r,o)=>r+(o.status==="distributed"?o.creatorReward:0),0),i=s,n=t.length;return{totalEarned:a,totalPending:i,totalLifetime:a+i,totalTransactions:n,averageReward:n>0?(a+i)/n:0}}getTopCreators(e=10){const t=[];for(const s of this.rewardHistory.keys()){const a=this.getCreatorEarnings(s);t.push({creatorUserId:s,...a})}return t.sort((s,a)=>a.totalLifetime-s.totalLifetime).slice(0,e)}autoDistributeRewards(e=100){const t=[];for(const[s,a]of this.pendingRewards.entries())if(a>=e){const i=this.distributePendingRewards(s);t.push({creatorUserId:s,...i})}return t}generateRewardId(){return`reward_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}getSystemStats(){const e=this.rewardHistory.size,t=Array.from(this.pendingRewards.values()).reduce((a,i)=>a+i,0),s=Array.from(this.rewardHistory.values()).reduce((a,i)=>a+i.length,0);return{totalCreators:e,totalRewardsDistributed:this.totalRewardsDistributed,totalPendingRewards:t,totalPlatformFees:this.totalPlatformFees,totalTransactions:s,averageRewardPerTransaction:s>0?(this.totalRewardsDistributed+t)/s:0,creatorRewardPercentage:this.creatorRewardPercentage,platformFeePercentage:this.platformFeePercentage}}saveToStorage(){try{const e={pendingRewards:Array.from(this.pendingRewards.entries()),rewardHistory:Array.from(this.rewardHistory.entries()),totalRewardsDistributed:this.totalRewardsDistributed,totalPlatformFees:this.totalPlatformFees};localStorage.setItem("rewardManager",JSON.stringify(e))}catch(e){console.warn("Failed to save reward manager data to localStorage:",e)}}loadFromStorage(){try{const e=localStorage.getItem("rewardManager");if(!e)return;const t=JSON.parse(e);this.pendingRewards=new Map(t.pendingRewards||[]),this.rewardHistory=new Map(t.rewardHistory||[]),this.totalRewardsDistributed=t.totalRewardsDistributed||0,this.totalPlatformFees=t.totalPlatformFees||0,console.log(`Loaded reward data for ${this.rewardHistory.size} creators`)}catch(e){console.warn("Failed to load reward manager data from localStorage:",e)}}}const F=class F{constructor(e,t,s="",a=0){this.type=e,this.cost=t,this.description=s,this.cooldown=a,this.id=F.generateId(),this.icon=null,this.color="#00ffff",this.glowColor="#ffffff",this.name=e.replace(/_/g," ").toLowerCase().replace(/\b\w/g,i=>i.toUpperCase()),this.isUsed=!1,this.usedAt=null}static generateId(){return`consumable_${++F.idCounter}`}async activate(e){if(this.isUsed)return console.warn(`Consumable ${this.type} is already used`),!1;this.isUsed=!0,this.usedAt=Date.now();const t=await this.applyEffect(e);return t||(this.isUsed=!1,this.usedAt=null),t}async applyEffect(e){return!0}canPurchase(e){return{canPurchase:e>=this.cost,reason:e<this.cost?"insufficient_tokens":"available"}}getDisplayInfo(){return{type:this.type,name:this.name,description:this.description,cost:this.cost,icon:this.icon,color:this.color,glowColor:this.glowColor,isUsed:this.isUsed}}clone(){const e=this.constructor;return e===F?new e(this.type,this.cost,this.description,this.cooldown):new e}};$(F,"idCounter",0);let N=F;class ce extends N{constructor(){super("EMP_BLAST",1750,"Detonates an electromagnetic pulse, destroying all enemy projectiles and temporarily disabling enemy fire within the level.",0),this.icon="⚡",this.color="#00aaff",this.glowColor="#88ccff",this.disableDuration=5e3}async applyEffect(e){try{const t=e.gameObjectManager.findByTag("enemy_projectile");for(const a of t)a.active&&!a.isDestroyed&&a.destroy();const s=e.gameObjectManager.findByTag("enemy");for(const a of s)a.active&&!a.isDestroyed&&(a.fireCooldown=this.disableDuration,a.empDisabled=!0,a.empDisableEndTime=Date.now()+this.disableDuration);return!0}catch(t){return console.error("Failed to apply EMP Blast effect:",t),!1}}}class he extends N{constructor(){super("TIME_DILATION",1750,"Creates a time local distortion field allowing the player to fight faster than enemies can respond.",0),this.icon="⏰",this.color="#aa00ff",this.glowColor="#cc88ff",this.freezeDuration=5e3}async applyEffect(e){try{const t=e.gameObjectManager.findByTag("enemy");for(const s of t)s.active&&!s.isDestroyed&&(s.originalVelocity||(s.originalVelocity=s.velocity.clone()),s.velocity.set(0,0),s.isFrozen=!0,s.freezeEndTime=Date.now()+this.freezeDuration,s.fireCooldown=this.freezeDuration,s.timeDilationDisabled=!0,s.timeDilationEndTime=Date.now()+this.freezeDuration);return!0}catch(t){return console.error("Failed to apply Time Dilation effect:",t),!1}}}class K{static createConsumable(e){switch(e){case"EMP_BLAST":return new ce;case"TIME_DILATION":return new he;default:return console.warn(`Unknown consumable type: ${e}`),null}}static createAllConsumables(){return[new ce,new he]}static getConsumableTypes(){return["EMP_BLAST","TIME_DILATION"]}}class st{constructor(e,t){this.tokenManager=e,this.gameEngine=t,this.isVisible=!1,this.isInitialized=!1,this.container=null,this.shoppingCart=new Map,this.availablePowerUps=V.createAllPowerUps(),this.activePowerUps=new Map,this.currentWeaponVariant="standard",this.addHangarUpgradesToPowerUps(),this.availableEnvironments=[],this.selectedEnvironments=[],this.availableConsumables=K.createAllConsumables(),this.onPowerUpPurchased=null,this.onWarpPurchased=null,this.onEnvironmentPurchased=null,this.onConsumablePurchased=null,this.onItemsPurchased=null,this.isProcessingCart=!1,this.onItemsPurchased=null,this.onClose=null,this.animationFrame=null,this.glowAnimation=0,this.feedbackMessages=[],this.feedbackTimeout=null,this.boundHandleTokenTransaction=this.handleTokenTransaction.bind(this)}async initialize(){if(!this.isInitialized)try{this.container=document.createElement("div"),this.container.id="genie-interface",this.container.className="genie-interface hidden",document.body.appendChild(this.container),this.setupEventListeners(),this.isInitialized=!0}catch(e){throw console.error("GenieInterface initialization error:",e),e}}async show(){var t;if(!this.isInitialized)return;this.isVisible=!0,this.clearPerLevelPowerUps(),await this.checkWalletConnection(),this.tokenManager&&await this.tokenManager.verifyWalletBalance();const e=(t=this.gameEngine)==null?void 0:t.hangarManager;e&&e.reloadUpgradeData(),this.updatePowerUpAvailability(),await this.updateAvailableEnvironments(),this.render(),this.container.classList.remove("hidden"),this.container.classList.add("visible"),this.startGlowAnimation()}clearPerLevelPowerUps(){if(!this.gameEngine||!this.gameEngine.playerShip)return;const e=this.gameEngine.playerShip,t=["SPREAD_AMMO","EXTRA_LIFE","EXTRA_WINGMAN","REALITY_WARP"];if(e.activePowerUps){for(const s of t)if(e.activePowerUps.has(s)){const a=e.activePowerUps.get(s);a&&typeof a.remove=="function"&&a.remove(e),e.activePowerUps.delete(s),console.log(`Cleared per-level power-up: ${s} from player ship`)}}if(this.activePowerUps)for(const s of t)this.activePowerUps.has(s)&&(this.activePowerUps.delete(s),console.log(`Cleared per-level power-up: ${s} from Genie interface`))}async checkWalletConnection(){const e=this.gameEngine.mainMenu.authManager,t=e.isAuthenticated&&e.user&&e.user.ethAddress,s=await this.getWalletAddressFromOrangeID(),a=!!s,i=!!this.tokenManager.getWalletAddress();if(!t&&!a&&!i){if(confirm("Please connect your wallet to purchase items. Connect now?"))try{await this.tokenManager.connectWallet(),this.showFeedback("Wallet connected successfully!","success",2e3)}catch(r){console.error("Failed to connect wallet:",r),this.showFeedback("Failed to connect wallet. Please try again.","error",3e3)}}else{let n=null;t?(n=e.user.ethAddress,console.log("Wallet already connected via AuthManager:",n),this.showFeedback("Wallet connected via AuthManager!","success",1500)):a?(n=s,console.log("Wallet already connected via OrangeID:",n),this.showFeedback("Wallet connected via OrangeID!","success",1500)):i&&(n=this.tokenManager.getWalletAddress(),console.log("Wallet already connected directly:",n),this.showFeedback("Wallet connected!","success",1500)),n&&!this.tokenManager.getWalletAddress()&&this.tokenManager.setWalletAddress(n);try{await this.tokenManager.syncWalletBalance(),console.log("Wallet balance synced:",this.tokenManager.getWalletBalance())}catch(r){console.warn("Failed to sync wallet balance:",r)}}}async getWalletAddressFromOrangeID(){if(this.gameEngine&&this.gameEngine.mainMenu&&this.gameEngine.mainMenu.authManager){const e=this.gameEngine.mainMenu.authManager;if(e.isAuthenticated&&e.user&&e.user.ethAddress)return this.tokenManager.setWalletAddress(e.user.ethAddress),e.user.ethAddress}if(window.OrangeID&&typeof window.OrangeID.getProfile=="function")try{const e=await window.OrangeID.getProfile();if(e&&e.walletAddress)return this.tokenManager.setWalletAddress(e.walletAddress),e.walletAddress}catch(e){console.warn("Failed to get wallet address from OrangeID:",e)}return null}hide(){this.isVisible&&(this.isVisible=!1,this.container.classList.remove("visible"),this.container.classList.add("hidden"),this.stopGlowAnimation())}updatePowerUpAvailability(){const e=this.tokenManager.getBalance(),t=this.gameEngine.playerShip;this.availablePowerUps.forEach(s=>{var i;!s.originalCost&&s.cost&&(s.originalCost=s.cost),(i=this.gameEngine.ethTestModeManager)!=null&&i.isTestMode&&s.originalCost?s.cost=this.gameEngine.ethTestModeManager.applyTestModeDiscount(s.originalCost):s.originalCost&&(s.cost=s.originalCost);const a=s.canPurchase(t,e);s.availability=a})}addHangarUpgradesToPowerUps(){var s;const e=(s=this.gameEngine)==null?void 0:s.hangarManager;if(!e)return;e.getUpgradeInfo().forEach(a=>{const i={type:`hangar_${a.id}`,name:a.name,description:a.description,cost:a.nextCost,icon:"🚀",duration:null,isHangarUpgrade:!0,upgradeId:a.id,currentLevel:a.currentLevel,maxLevel:a.maxLevel,isMaxLevel:a.isMaxLevel,canPurchase:(n,r)=>{const o=e.getUpgradeInfo().find(c=>c.id===a.id);return o?o.isMaxLevel?{canPurchase:!1,reason:"max_level"}:r<o.nextCost?{canPurchase:!1,reason:"insufficient_tokens"}:{canPurchase:!0}:{canPurchase:!1,reason:"upgrade_not_found"}}};this.availablePowerUps.push(i)})}refreshHangarUpgrades(){this.availablePowerUps=this.availablePowerUps.filter(e=>!e.isHangarUpgrade),this.addHangarUpgradesToPowerUps()}async updateAvailableEnvironments(){if(!this.gameEngine.environmentTracker){this.selectedEnvironments=[],this.availableEnvironments=[];return}if(this.gameEngine.realityWarpManager&&this.gameEngine.realityWarpManager.pendingEnvironment){this.availableEnvironments=[];return}this.selectedEnvironments=await this.gameEngine.environmentTracker.getRandomEnvironments(5),this.availableEnvironments=[...this.selectedEnvironments],this.selectedEnvironments.forEach(e=>{e.price=this.calculateEnvironmentPrice(e),e.canAfford=this.tokenManager.canAfford(e.price)}),this.availableEnvironments.forEach(e=>{e.price=this.calculateEnvironmentPrice(e),e.canAfford=this.tokenManager.canAfford(e.price)}),this.availableConsumables.forEach(e=>{var t;!e.originalCost&&e.cost&&(e.originalCost=e.cost),(t=this.gameEngine.ethTestModeManager)!=null&&t.isTestMode&&e.originalCost?e.cost=this.gameEngine.ethTestModeManager.applyTestModeDiscount(e.originalCost):e.originalCost&&(e.cost=e.originalCost)})}getCurrentUserId(){if(this.gameEngine.orangeSDKManager&&this.gameEngine.orangeSDKManager.getCurrentUser){const e=this.gameEngine.orangeSDKManager.getCurrentUser();return e?e.id:null}return localStorage.getItem("sessionUserId")||localStorage.setItem("sessionUserId",`user_${Date.now()}_${Math.random().toString(36).substr(2,9)}`),localStorage.getItem("sessionUserId")}addToCart(e,t,s={}){const a={type:e,id:t,...s};this.shoppingCart.set(`${e}-${t}`,a),console.log(`Added ${e} ${t} to shopping cart`)}removeFromCart(e,t){const s=e==="hangarUpgrade"?t:`${e}-${t}`;this.shoppingCart.has(s)&&(this.shoppingCart.delete(s),console.log(`Removed ${e} ${t} from shopping cart`))}getCartItems(){return new Map(this.shoppingCart)}clearCart(){this.shoppingCart.clear(),console.log("Shopping cart cleared")}calculateCartTotal(){var t;let e=0;for(const s of this.shoppingCart.values()){let a=0;switch(s.type){case"powerUp":const i=this.availablePowerUps.find(h=>h.type===s.id);a=i?i.cost:0;break;case"consumable":const r=this.gameEngine.consumableManager.getAvailableConsumables().find(h=>h.type===s.id);a=r?r.cost:0;break;case"hangarUpgrade":const o=(t=this.gameEngine)==null?void 0:t.hangarManager;if(o){const d=o.getUpgradeInfo().find(g=>g.id===s.upgradeId);a=d?d.nextCost:0}break;case"environment":const c=this.availableEnvironments.find(h=>h.id===s.id);a=c?this.calculateEnvironmentPrice(c):0;break}e+=a}return e}calculateEnvironmentPrice(e){var i;let s={1:5e3,2:7500,3:1e4,4:15e3,5:2e4}[e.difficulty]||1e4;(i=this.gameEngine.ethTestModeManager)!=null&&i.isTestMode&&(s=this.gameEngine.ethTestModeManager.applyTestModeDiscount(s));const a=1+e.timesUsed*.01;return Math.round(s*a)}renderCart(){if(this.shoppingCart.size===0)return"";const e=Array.from(this.shoppingCart.values()),t=this.calculateCartTotal();return`
            <div class="cart-section">
                <h2 class="section-title">Your Cart (${e.length} items)</h2>
                <div class="cart-items">
                    ${e.map(s=>this.renderCartItem(s)).join("")}
                </div>
                <div class="cart-summary">
                    <div class="cart-total">Total: ${t} ✨ WISH Tokens</div>
                    <button class="genie-button cart-clear-btn" id="cart-clear-btn">
                        Clear Cart
                    </button>
                </div>
            </div>
        `}renderCartItem(e){var a;let t="",s=0;switch(e.type){case"powerUp":t=this.formatPowerUpName(e.id);const i=this.availablePowerUps.find(h=>h.type===e.id);s=i?i.cost:0;break;case"consumable":const r=this.gameEngine.consumableManager.getAvailableConsumables().find(h=>h.type===e.id);t=r?r.name:e.id,s=r?r.cost:0;break;case"hangarUpgrade":const o=(a=this.gameEngine)==null?void 0:a.hangarManager;if(o){const d=o.getUpgradeInfo().find(g=>g.id===e.upgradeId);t=d?d.name:e.upgradeId,s=d?d.nextCost:0}else t=e.upgradeId;break;case"environment":const c=this.availableEnvironments.find(h=>h.id===e.id);t=c?c.name:e.id,s=c?this.calculateEnvironmentPrice(c):0;break}return`
            <div class="cart-item" data-cart-key="${e.key}">
                <div class="cart-item-info">
                    <span class="cart-item-name">${t}</span>
                    <span class="cart-item-cost">${s} ✨</span>
                </div>
                <button class="cart-item-remove" data-cart-key="${e.key}">✕</button>
            </div>
        `}render(){if(!this.container)return;this.refreshHangarUpgrades(),this.updatePowerUpAvailability();const e=this.tokenManager.getBalance(),t=this.tokenManager.getBalanceDisplay?this.tokenManager.getBalanceDisplay():`${e} WISH`;this.container.innerHTML=`
            <div class="genie-modal">
                <div class="genie-backdrop" id="genie-backdrop"></div>
                <div class="genie-content">
                    <div class="genie-header">
                        <div class="genie-character">
                            <div class="genie-lamp">🪔</div>
                            <div class="genie-smoke"></div>
                        </div>
                        <h1 class="genie-title">The Cosmic Genie</h1>
                        <p class="genie-subtitle">Your wishes are my command, traveler...</p>
                        <div class="token-display">
                            <span class="token-icon">✨</span>
                            <span class="token-amount">${t}</span>
                        </div>
                    </div>
                    
                    <div class="genie-body">
                        ${this.renderCart()}
                        
                        <div class="power-ups-section">
                            <h2 class="section-title">Power-Ups & Ship Upgrades</h2>
                            <div class="power-ups-grid">
                                ${this.renderPowerUps()}
                            </div>
                        </div>

                        <div class="consumables-section">
                            <h2 class="section-title">Tactical Consumables</h2>
                            <div class="consumables-grid">
                                ${this.renderConsumables()}
                            </div>
                        </div>

                        <div class="environments-section">
                            <h2 class="section-title">Mystical Environments</h2>
                            <div class="environments-grid">
                                ${this.renderEnvironments()}
                            </div>
                        </div>
                    </div>
                    
                    <div class="genie-footer">
                        <button id="genie-close-btn" class="genie-button secondary">
                            Continue Journey
                        </button>
                    </div>
                </div>
            </div>
        `,this.setupButtonEventListeners()}renderPowerUps(){return this.availablePowerUps.map(e=>{var h,d;const t=e.availability||{canPurchase:!1,reason:"unknown"},s=e.weaponVariant!==void 0,a=s?((d=(h=this.gameEngine.playerShip)==null?void 0:h.weaponSystem)==null?void 0:d.currentVariant)===e.weaponVariant:this.activePowerUps.has(e.type),i=t.canPurchase&&!a;let n="",r="",o=`Purchase (${e.cost} ✨)`;a?(n=s?"equipped":"active",r=s?"Equipped":"Active",o=s?"Equipped":"Already Active"):t.canPurchase||(n="unavailable",t.reason==="insufficient_tokens"?(r="Insufficient Tokens",o=`Need ${e.cost-this.tokenManager.getBalance()} more ✨`):t.reason==="already_equipped"?(r="Already Equipped",o="Already Equipped"):(r="Unavailable",o="Cannot Purchase"));const c=s?this.renderEffectiveness(e.weaponVariant):"";if(e.isHangarUpgrade){const g=e.isMaxLevel?`<div class="upgrade-level max-level">MAX LEVEL (${e.currentLevel}/${e.maxLevel})</div>`:`<div class="upgrade-level">Level ${e.currentLevel}/${e.maxLevel}</div>`;return`
                    <div class="power-up-card hangar-upgrade ${n}" data-power-up="${e.type}">
                        <div class="power-up-icon">${e.icon}</div>
                        <h3 class="power-up-name">${e.name}</h3>
                        <p class="power-up-description">${e.description}</p>
                        ${g}
                        <div class="power-up-details">
                            <div class="power-up-cost">
                                <span class="cost-amount">${e.isMaxLevel?"MAXED":e.cost}</span>
                                ${e.isMaxLevel?"":'<span class="cost-icon">✨</span>'}
                            </div>
                            <div class="power-up-duration">
                                Permanent Upgrade
                            </div>
                        </div>
                        <div class="power-up-status">${r}</div>
                        <button class="power-up-button hangar-upgrade-btn ${i?"primary":"disabled"}"
                                data-power-up="${e.type}"
                                data-upgrade-id="${e.upgradeId}"
                                ${i?"":"disabled"}>
                            ${o}
                        </button>
                    </div>
                `}return`
                <div class="power-up-card ${n} ${s?"weapon-variant":""}" data-power-up="${e.type}">
                    <div class="power-up-icon">${e.icon}</div>
                    <h3 class="power-up-name">${this.formatPowerUpName(e.type)}</h3>
                    <p class="power-up-description">${e.description}</p>
                    ${c?`<div class="power-up-effectiveness">${c}</div>`:""}
                    <div class="power-up-details">
                        <div class="power-up-cost">
                            <span class="cost-amount">${e.cost}</span>
                            <span class="cost-icon">✨</span>
                        </div>
                        <div class="power-up-duration">
                            ${e.duration?`${Math.ceil(e.duration/1e3)}s`:"Lasts for this Level"}
                        </div>
                    </div>
                    <div class="power-up-status">${r}</div>
                    <button class="power-up-button ${i?"primary":"disabled"}"
                            data-power-up="${e.type}"
                            ${i?"":"disabled"}>
                        ${o}
                    </button>
                </div>
            `}).join("")}renderConsumables(){const e=this.tokenManager.getBalance();this.tokenManager.getBalanceDisplay?this.tokenManager.getBalanceDisplay():`${e}`;const t=this.gameEngine.consumableManager,s=t&&t.hasConsumable();return this.availableConsumables.map(a=>{const i=a.canPurchase(e),n=i.canPurchase&&!s;let r="",o="";s?(r="Inventory Full",o="status-warning"):i.canPurchase?(r="Available",o="status-available"):(r=i.reason==="insufficient_tokens"?"Insufficient Tokens":"Unavailable",o="status-error");const c=a.getDisplayInfo();return`
                <div class="consumable-card ${n?"purchasable":"disabled"}">
                    <div class="consumable-header">
                        <div class="consumable-icon" style="
                            background: linear-gradient(135deg, ${c.color}, ${c.glowColor});
                            color: white;
                            font-size: 24px;
                        ">
                            ${c.icon}
                        </div>
                        <div class="consumable-info">
                            <h3 class="consumable-name">${c.name}</h3>
                            <div class="consumable-cost">
                                <span class="cost-amount">${c.cost}</span>
                                <span class="cost-currency">✨ WISH</span>
                            </div>
                        </div>
                    </div>

                    <div class="consumable-description">
                        ${c.description}
                    </div>

                    <div class="consumable-usage">
                        <div class="usage-info">
                            <span class="usage-icon">⚡</span>
                            <span class="usage-text">Single-use tactical advantage</span>
                        </div>
                        <div class="usage-info">
                            <span class="usage-icon">⌨️</span>
                            <span class="usage-text">Press 'E' to activate in-level</span>
                        </div>
                    </div>

                    <div class="consumable-status ${o}">
                        ${r}
                    </div>

                    <button class="consumable-button ${n?"primary":"disabled"}"
                            data-consumable="${a.type}"
                            ${n?"":"disabled"}>
                        ${n?`Purchase for ${c.cost} ✨`:"Unavailable"}
                    </button>
                </div>
            `}).join("")}renderHangarUpgrades(){var s;const e=(s=this.gameEngine)==null?void 0:s.hangarManager;if(!e)return`
                <div class="hangar-unavailable">
                    <div class="hangar-icon">🔧</div>
                    <p>Hangar system not available</p>
                </div>
            `;const t=e.getUpgradeInfo();return!t||t.length===0?`
                <div class="no-upgrades">
                    <div class="no-upgrades-icon">⚙️</div>
                    <p>No upgrades available</p>
                </div>
            `:t.map(a=>{const i=a.isMaxLevel,n=a.canAfford&&!i;this.tokenManager.getBalance();let r="",o="";return i?(r="max-level",o="MAXED"):n?(r="available",o=`Upgrade (${a.nextCost} ✨)`):(r="insufficient-tokens",o=`Need ${a.nextCost} ✨`),`
                <div class="hangar-upgrade-card ${r}">
                    <div class="upgrade-header">
                        <h3 class="upgrade-name">${a.name}</h3>
                        <div class="upgrade-level">Level ${a.currentLevel}/${a.maxLevel}</div>
                    </div>
                    <div class="upgrade-body">
                        <p class="upgrade-description">${a.description}</p>
                        <div class="upgrade-effects">
                            Affects: ${a.affects.join(", ")}
                        </div>
                    </div>
                    <div class="upgrade-footer">
                        <div class="upgrade-cost">
                            ${i?"✨ MAXED":`✨ ${a.nextCost}`}
                        </div>
                        <button class="genie-button hangar-upgrade-btn ${n&&!i?"primary":"disabled"}"
                                data-upgrade-id="${a.id}"
                                ${!n||i?"disabled":""}>
                            ${o}
                        </button>
                    </div>
                </div>
            `}).join("")}renderEnvironments(){return!this.selectedEnvironments||this.selectedEnvironments.length===0?`
                <div class="no-environments">
                    <div class="no-environments-icon">🌌</div>
                    <p>No mystical environments available at this time.</p>
                    <p class="hint">Create your own environment using Reality Warp!</p>
                </div>
            `:this.selectedEnvironments.map(e=>{const t=e.canAfford,s=e.timesUsed>0?`Used ${e.timesUsed} times`:"New environment";let a="",i="",n=`Purchase (${e.price} ✨)`;t?(a="available",i="Available"):(a="insufficient-tokens",i="Insufficient tokens",n=`Need ${e.price} ✨`);const r=e.name||"Custom Environment",o=e.description||"A mystical environment created by a player.",c=e.tags||[],h=e.creatorUserId||"unknown";return`
                <div class="environment-card ${a}">
                    <div class="environment-header">
                        <h3 class="environment-name">${r}</h3>
                    </div>
                    <div class="environment-body">
                        <p class="environment-description">${o}</p>
                        <div class="environment-tags">
                            ${c.map(d=>`<span class="tag">${d}</span>`).join("")}
                        </div>
                        <div class="environment-stats">
                            <span class="popularity">${s}</span>
                            <span class="creator">by ${this.formatCreatorName(h)}</span>
                        </div>
                    </div>
                    <div class="environment-footer">
                        <div class="environment-status ${a}">${i}</div>
                        <button class="genie-button environment-purchase-btn ${t?"primary":"disabled"}"
                                data-environment-id="${e.id}"
                                ${t?"":"disabled"}>
                            ${n}
                        </button>
                    </div>
                </div>
            `}).join("")}formatCreatorName(e){if(e.startsWith("user_")){const t=e.split("_");if(t.length>=3)return`Player ${t[2].substring(0,6)}`}return"Anonymous"}formatPowerUpName(e){switch(e){case"EXTRA_LIFE":return"Extra Life";case"SPREAD_AMMO":return"Spread Ammo";case"EXTRA_WINGMAN":return"Wingman";case"REALITY_WARP":return"Reality Warp";case"KINETIC_BOOST":return"Kinetic Boost";case"LASER_ROUNDS":return"Laser Rounds";case"FLAME_ROUNDS":return"Flame Rounds";case"ICE_ROUNDS":return"Ice Rounds";case"PLASMA_ROUNDS":return"Plasma Rounds";case"SONIC_ROUNDS":return"Sonic Rounds";default:return e.replace(/_/g," ").toLowerCase().replace(/\b\w/g,t=>t.toUpperCase())}}renderEffectiveness(e){var i;const t=(i=this.gameEngine.playerShip)==null?void 0:i.weaponSystem;if(!t)return"";const s=t.variantProperties[e];if(!s||!s.effectivenessMultipliers)return"";const a=Object.entries(s.effectivenessMultipliers).map(([n,r])=>{const o=r>=2?"Very Effective":"Effective",c=this.getEnemyTypeIcon(n);return`<span class="effectiveness-item" title="${o} vs ${n}">
                    ${c} ${r}x
                </span>`}).join("");return a?`<div class="effectiveness-list">${a}</div>`:""}getEnemyTypeIcon(e){return{water:"💧",fire:"🔥",air:"💨",earth:"🌍",crystal:"💎",shadow:"🌑"}[e]||"⚔️"}setupEventListeners(){document.addEventListener("keydown",e=>{e.key==="Escape"&&this.isVisible&&this.close()}),document.addEventListener("token-transaction",this.boundHandleTokenTransaction)}handleTokenTransaction(e){console.log("[GenieInterface] Token transaction detected, re-rendering..."),this.isVisible&&this.render()}async processCartItems(){var e,t;if(!this.isProcessingCart){this.isProcessingCart=!0,this.showFeedback("Processing cart items...","info",2e3);try{const s=this.calculateCartTotal();if(s<=0){this.showFeedback("Cart is empty or invalid","warning",2e3),this.isProcessingCart=!1;return}const a=this.getCurrentUserId(),i=this.tokenManager.hotWalletAddress||"******************************************",n={recipientAddress:i,batchPurchase:!0,userId:a,cartItems:Array.from(this.shoppingCart.values()).map(p=>({type:p.type,id:p.id,cost:p.cost||0})),totalAmount:s},r=await this.tokenManager.spendTokens(s,"batch_purchase",n);if(!r.success){console.error("Batch payment failed:",r),this.showFeedback(`Payment failed: ${r.reason}`,"error"),this.isProcessingCart=!1;return}console.log("✅ Batch payment successful:",r),this.showFeedback(`Payment successful! Hot wallet received ${s} ETH`,"success"),console.log("💰 Receiving",s,"ETH to hot wallet",i,"from",this.tokenManager.walletAddress);const o=[],c=Array.from(this.shoppingCart.values()).filter(p=>p.type==="powerUp");for(const p of c)try{if(p.id==="REALITY_WARP")console.log("[DEBUG] Processing REALITY_WARP power-up, calling collectRealityWarpIdea()"),await this.collectRealityWarpIdea()?(o.push({success:!0,type:"powerUp",id:p.id}),console.log("[DEBUG] REALITY_WARP successfully processed via collectRealityWarpIdea()")):(o.push({success:!1,type:"powerUp",id:p.id,error:"Reality warp cancelled or failed"}),console.log("[DEBUG] REALITY_WARP failed or was cancelled"));else{const f=V.createPowerUp(p.id);this.gameEngine.playerShip?await f.apply(this.gameEngine.playerShip)?(this.gameEngine.playerShip.activePowerUps||(this.gameEngine.playerShip.activePowerUps=new Map),this.gameEngine.playerShip.activePowerUps.set(f.type,f),o.push({success:!0,type:"powerUp",id:p.id})):o.push({success:!1,type:"powerUp",id:p.id,error:"Failed to apply power-up"}):o.push({success:!1,type:"powerUp",id:p.id,error:"Player ship not available"})}}catch(f){console.error(`Failed to apply power-up ${p.id}:`,f),o.push({success:!1,type:"powerUp",id:p.id,error:f.message})}const h=Array.from(this.shoppingCart.values()).filter(p=>p.type==="consumable");for(const p of h)try{const f=this.gameEngine.consumableManager;if(f){const y=f.addConsumableToInventory(p.id);y.success?o.push({success:!0,type:"consumable",id:p.id}):o.push({success:!1,type:"consumable",id:p.id,error:y.reason})}else o.push({success:!1,type:"consumable",id:p.id,error:"Consumable manager not available"})}catch(f){console.error(`Failed to add consumable ${p.id} to inventory:`,f),o.push({success:!1,type:"consumable",id:p.id,error:f.message})}const d=Array.from(this.shoppingCart.values()).filter(p=>p.type==="hangarUpgrade");for(const p of d)try{const f=(e=this.gameEngine)==null?void 0:e.hangarManager;if(f){let y=f.purchasePlayerUpgrade(p.data.upgradeId);y.success?o.push({success:!0,type:"hangarUpgrade",id:p.data.upgradeId}):o.push({success:!1,type:"hangarUpgrade",id:p.data.upgradeId,error:y.reason})}else o.push({success:!1,type:"hangarUpgrade",id:p.data.upgradeId,error:"Hangar manager not available"})}catch(f){console.error(`Failed to apply hangar upgrade ${p.data.upgradeId}:`,f),o.push({success:!1,type:"hangarUpgrade",id:p.data.upgradeId,error:f.message})}const g=Array.from(this.shoppingCart.values()).filter(p=>p.type==="environment");for(const p of g)try{if(this.gameEngine.environmentTracker){const f=this.getCurrentUserId(),y=await this.gameEngine.environmentTracker.getEnvironment(p.id);if(y){const M=y.price,S=Number(M),I=((t=this.tokenManager)==null?void 0:t.getWalletAddress())||await this.getWalletAddressFromOrangeID()||"";let B=!1;if(I)try{B=await this.gameEngine.environmentTracker.recordEnvironmentPurchase(p.id,f,S,I)}catch(_){console.warn(`Failed to record environment purchase on server: ${_.message}. Proceeding with application.`)}this.gameEngine.rewardManager&&this.gameEngine.rewardManager.processCreatorReward(y.creatorUserId,f,p.id,M,y);try{await this.applyEnvironmentToNextLevel(y),o.push({success:!0,type:"environment",id:p.id}),B||console.warn(`Environment ${p.id} applied but purchase not recorded on server`)}catch(_){console.error(`Failed to apply environment ${p.id} to next level:`,_),o.push({success:!1,type:"environment",id:p.id,error:"Failed to apply environment to next level"})}}else o.push({success:!1,type:"environment",id:p.id,error:"Environment not found"})}else o.push({success:!1,type:"environment",id:p.id,error:"Environment tracker not available"})}catch(f){console.error(`Failed to apply environment ${p.id}:`,f),o.push({success:!1,type:"environment",id:p.id,error:f.message})}this.clearCart();const w=o.filter(p=>p.success).length,v=o.filter(p=>!p.success).length;v>0?this.showFeedback(`Applied ${w} items, ${v} failed. Check console for details.`,"warning",3e3):w>0&&this.showFeedback(`Successfully applied ${w} items!`,"success",2e3),this.onItemsPurchased&&this.onItemsPurchased(o)}catch(s){console.error("Error processing cart items:",s),this.showFeedback("Error processing cart items. Please try again.","error",3e3)}finally{this.isProcessingCart=!1}}}getConsumableName(e){const s=this.gameEngine.consumableManager.getAvailableConsumables().find(a=>a.type===e);return s?s.name:e}getHangarUpgradeName(e){var i;const t=(i=this.gameEngine)==null?void 0:i.hangarManager;if(!t)return`${e}`;const a=t.getUpgradeInfo().find(n=>n.id===e);return a?a.name:e}setupButtonEventListeners(){const e=this.container.querySelector("#genie-close-btn");e&&e.addEventListener("click",()=>this.close());const t=this.container.querySelector("#genie-backdrop");t&&t.addEventListener("click",()=>this.close()),this.container.querySelectorAll(".power-up-button:not(.disabled):not(.hangar-upgrade-btn)").forEach(h=>{h.addEventListener("click",d=>{const g=d.target.dataset.powerUp;this.addToCart("powerUp",g),this.showFeedback(`${this.formatPowerUpName(g)} added to cart!`,"success",1500),this.render()})}),this.container.querySelectorAll(".consumable-button:not(.disabled)").forEach(h=>{h.addEventListener("click",d=>{const g=d.target.dataset.consumable;this.addToCart("consumable",g),this.showFeedback(`${this.getConsumableName(g)} added to cart!`,"success",1500),this.render()})}),this.container.querySelectorAll(".hangar-upgrade-btn:not(.disabled)").forEach(h=>{h.addEventListener("click",d=>{const g=d.target.dataset.upgradeId;this.addToCart("hangarUpgrade",g,{upgradeId:g});const w=this.getHangarUpgradeName(g);this.showFeedback(`${w} added to cart!`,"success",1500),this.render()})}),this.container.querySelectorAll(".environment-purchase-btn:not(.disabled)").forEach(h=>{h.addEventListener("click",d=>{const g=d.target.dataset.environmentId;this.addToCart("environment",g);const w=this.availableEnvironments.find(p=>p.id===g),v=w?w.name:g;this.showFeedback(`${v} added to cart!`,"success",1500),this.render()})}),this.container.querySelectorAll(".warp-button:not(.disabled)").forEach(h=>{h.addEventListener("click",d=>{const g=d.target.dataset.powerUp;this.addToCart("powerUp",g),this.showFeedback(`${this.formatPowerUpName(g)} added to cart!`,"success",1500),this.render()})});const o=this.container.querySelector("#cart-clear-btn");o&&o.addEventListener("click",()=>{this.clearCart(),this.render(),this.showFeedback("Cart cleared!","info",1500)}),this.container.querySelectorAll(".cart-item-remove").forEach(h=>{h.addEventListener("click",d=>{const g=d.target.dataset.cartKey;this.removeFromCart(g),this.render(),this.showFeedback("Item removed from cart!","info",1500)})})}async applyPowerUp(e){const t=this.availablePowerUps.find(i=>i.type===e);if(!t)return this.showFeedback("Power-up not found","error"),!1;if(t.isHangarUpgrade)return this.showFeedback("Hangar upgrades are handled separately","error"),!1;const s=this.gameEngine.playerShip;if(!s)return this.showFeedback("Player ship not available","error"),!1;const a=t.weaponVariant!==void 0;try{if(!await t.apply(s))return this.showFeedback(a?"Failed to equip weapon variant":"Power-up activation failed","error"),!1;if(a?(["KINETIC_BOOST","LASER_ROUNDS","FLAME_ROUNDS","ICE_ROUNDS","PLASMA_ROUNDS","SONIC_ROUNDS"].forEach(r=>{r!==e&&this.activePowerUps.has(r)&&this.activePowerUps.delete(r)}),this.currentWeaponVariant=t.weaponVariant,this.activePowerUps.set(e,t)):this.activePowerUps.set(e,t),e==="REALITY_WARP"){if(!await this.collectRealityWarpIdea())return this.showFeedback("Reality Warp cancelled.","info"),this.activePowerUps.has(e)&&(this.activePowerUps.delete(e),t.remove(s)),!1;this.showFeedback("Reality Warp activated with your idea!","success")}else{const n=this.formatPowerUpName(e),r=a?"equipped":"activated";this.showFeedback(`${n} ${r}!`,"success")}return this.updatePowerUpAvailability(),this.render(),!0}catch(i){return console.error(`Failed to apply power-up ${e}:`,i),this.showFeedback("Failed to apply power-up. Please try again.","error"),!1}}applyHangarUpgrade(e){var s;const t=(s=this.gameEngine)==null?void 0:s.hangarManager;if(!t)return console.error("Hangar manager not available"),this.showFeedback("Hangar system not available","error"),!1;try{let a=t.purchasePlayerUpgrade(e);return a.success?(this.showUpgradeSuccess(a),this.render(),!0):(this.showUpgradeError(a),!1)}catch(a){return console.error(`Failed to apply hangar upgrade ${e}:`,a),this.showFeedback("Failed to apply hangar upgrade. Please try again.","error"),!1}}showUpgradeSuccess(e){const t=document.createElement("div");t.className="upgrade-success-message",t.innerHTML=`
            <div class="success-content">
                ✅ ${e.upgradeId.replace("_"," ").toUpperCase()} upgraded to Level ${e.newLevel}!
                <br>
                <small>Cost: ${e.cost} ✨ WISH tokens</small>
            </div>
        `,this.container.appendChild(t),setTimeout(()=>{t.parentNode&&t.parentNode.removeChild(t)},3e3)}showUpgradeError(e){console.error("Upgrade purchase failed:",e.reason);const t=document.createElement("div");t.className="upgrade-error-message",t.innerHTML=`
            <div class="error-content">
                ❌ Upgrade failed: ${e.reason.replace("_"," ")}
            </div>
        `,this.container.appendChild(t),setTimeout(()=>{t.parentNode&&t.parentNode.removeChild(t)},3e3)}async applyEnvironment(e){if(!this.gameEngine.environmentTracker)return console.error("Environment tracker not available"),this.showFeedback("Environment system not available","error"),!1;try{const t=await this.gameEngine.environmentTracker.getEnvironment(e);if(!t)return console.error("Environment not found:",e),this.showFeedback("Environment not found","error"),!1;const s=this.getCurrentUserId(),a=t.price,i=Number(a);return isNaN(i)||i<=0?(console.error("Invalid environment cost:",a),this.showFeedback("Error: Invalid environment cost","error"),!1):await this.gameEngine.environmentTracker.recordEnvironmentPurchase(e,s,i)?(this.gameEngine.rewardManager&&this.gameEngine.rewardManager.processCreatorReward(t.creatorUserId,s,e,a,t),await this.applyEnvironmentToNextLevel(t),this.onEnvironmentPurchased&&this.onEnvironmentPurchased(t,a),this.updateAvailableEnvironments(),this.render(),this.showFeedback(`${t.name} selected for next level!`,"success"),!0):(this.showFeedback("Failed to record environment selection","error"),!1)}catch(t){return console.error("Error applying environment:",t),this.showFeedback("Error applying environment. Please try again.","error"),!1}}async applyEnvironmentToNextLevel(e){try{if(this.gameEngine.realityWarpManager){const t={type:e.type||"custom",name:e.name||"Custom Environment",description:e.description||"Custom environment from Genie",imagePrompt:e.imagePrompt||"",gameplayModifiers:e.gameplayModifiers||{},imageFileName:e.imageFileName||null,imageUrl:e.imageUrl||null,imageData:e.imageData||{images:[]}};if(e.imageUrl&&(!e.imageData||!e.imageData.images))t.imageData={images:[{url:e.imageUrl,localUrl:D.getImageUrl(e.imageUrl)}]};else if(e.imageData&&e.imageData.images&&e.imageData.images[0]){const a=e.imageData.images[0],i=a.localUrl||a.url;t.imageUrl=D.getImageUrl(i),t.imageData={images:[{...a,url:a.url,localUrl:i}]}}const s={...t,type:t.type||"custom",name:t.name||"Mystical Environment",description:t.description||"A mystical environment from the Genie.",gameplayModifiers:t.gameplayModifiers||{},imageData:t.imageData||{images:[]}};s.imageUrl&&(s.imageData={images:[{url:s.imageUrl,localUrl:D.getImageUrl(s.imageUrl)}]}),this.gameEngine.realityWarpManager.pendingEnvironment=s,console.log("Environment set for next level:",e.name,"with image URL:",t.imageUrl)}}catch(t){console.error("Error applying environment to next level:",t)}}async collectRealityWarpIdea(){return new Promise(e=>{let t=null,s=null,a=null,i=null;const n=()=>{t&&t.parentNode&&document.body.removeChild(t);const o=document.querySelector("style[data-reality-warp-style]");o&&document.head.removeChild(o)};t=document.createElement("div"),t.className="reality-warp-input-overlay",t.innerHTML=`
                <div class="reality-warp-input-modal">
                    <div class="reality-warp-input-header">
                        <h3>Reality Warp</h3>
                        <p>Describe the environment you want to create for the next level:</p>
                    </div>
                    <div class="reality-warp-input-body">
                        <textarea
                            id="reality-warp-idea"
                            class="reality-warp-textarea"
                            placeholder="e.g., A mystical forest with floating islands and ancient ruins..."
                            rows="4"
                            maxlength="200"
                        ></textarea>
                        <div class="reality-warp-input-footer">
                            <button id="reality-warp-cancel" class="genie-button secondary">Cancel</button>
                            <button id="reality-warp-confirm" class="genie-button primary">Create Reality</button>
                        </div>
                    </div>
                </div>
            `;const r=document.createElement("style");r.setAttribute("data-reality-warp-style","true"),r.textContent=`
                .reality-warp-input-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.8);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 10000;
                }
                .reality-warp-input-modal {
                    background: linear-gradient(135deg, #1a1a2e, #16213e);
                    border: 2px solid #6366f1;
                    border-radius: 12px;
                    padding: 24px;
                    max-width: 500px;
                    width: 90%;
                    box-shadow: 0 8px 32px rgba(99, 102, 241, 0.3);
                }
                .reality-warp-input-header h3 {
                    color: #818cf8;
                    margin: 0 0 8px 0;
                    font-size: 24px;
                    text-align: center;
                }
                .reality-warp-input-header p {
                    color: #cbd5e1;
                    margin: 0 0 16px 0;
                    text-align: center;
                    font-size: 14px;
                }
                .reality-warp-textarea {
                    width: 100%;
                    background: rgba(30, 41, 59, 0.8);
                    border: 1px solid #475569;
                    border-radius: 8px;
                    color: #e2e8f0;
                    padding: 12px;
                    font-size: 14px;
                    resize: vertical;
                    margin-bottom: 16px;
                }
                .reality-warp-textarea:focus {
                    outline: none;
                    border-color: #6366f1;
                    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
                }
                .reality-warp-input-footer {
                    display: flex;
                    justify-content: flex-end;
                    gap: 12px;
                }
                .genie-button {
                    padding: 10px 20px;
                    border: none;
                    border-radius: 6px;
                    font-size: 14px;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.2s ease;
                }
                .genie-button.primary {
                    background: #6366f1;
                    color: white;
                }
                .genie-button.primary:hover {
                    background: #4f46e5;
                }
                .genie-button.secondary {
                    background: #475569;
                    color: #e2e8f0;
                }
                .genie-button.secondary:hover {
                    background: #334155;
                }
                .loading-spinner {
                    width: 40px;
                    height: 40px;
                    border: 4px solid #475569;
                    border-top: 4px solid #6366f1;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    margin: 20px auto;
                }
                .loading-text {
                    text-align: center;
                    color: #cbd5e1;
                    font-size: 14px;
                    margin-top: 10px;
                }
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `,document.head.appendChild(r),document.body.appendChild(t),s=document.getElementById("reality-warp-idea"),a=document.getElementById("reality-warp-confirm"),i=document.getElementById("reality-warp-cancel"),s.focus(),i.addEventListener("click",()=>{console.log("[DEBUG] Reality Warp dialog cancelled by user"),n(),e(null)}),a.addEventListener("click",async()=>{const o=s.value.trim();if(console.log("[DEBUG] Reality Warp dialog confirmed with idea:",o),!o){console.log("[DEBUG] No idea provided, resolving with null"),e(null),n();return}a.disabled=!0,i.disabled=!0;const c=t.querySelector(".reality-warp-input-modal");c.innerHTML=`
                    <div class="reality-warp-input-header">
                        <h3>Generating Environment...</h3>
                        <p>Please wait while we create your custom environment...</p>
                    </div>
                    <div class="reality-warp-input-body">
                        <div class="loading-spinner"></div>
                        <div class="loading-text">Creating your reality...</div>
                    </div>
                `;try{const h=await fetch(D.getApiUrl("/api/generate-environment"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({environmentDescription:o})});if(!h.ok){const g=await h.json();throw new Error(g.error||"Failed to generate environment")}const d=await h.json();d?(console.log("Reality Warp executed with user idea:",o),this.gameEngine&&this.gameEngine.realityWarpManager&&await this.gameEngine.realityWarpManager.applyGeneratedEnvironment(d),e(o)):(console.warn("Reality Warp execution failed: Invalid response"),this.showFeedback&&this.showFeedback("Reality Warp failed: Invalid response from server","error"),e(null))}catch(h){console.error("Error during reality warp:",h),this.showFeedback&&this.showFeedback("Reality Warp failed due to an error","error"),e(null)}finally{n()}}),s.addEventListener("keydown",o=>{o.key.length===1&&!o.ctrlKey&&!o.altKey&&!o.metaKey||(o.key==="Enter"&&!o.shiftKey&&(o.preventDefault(),a.click()),o.key==="Escape"&&(o.preventDefault(),i.click()),["ArrowUp","ArrowDown","ArrowLeft","ArrowRight","Backspace","Delete","Home","End","Tab"].includes(o.key))})})}async close(){this.shoppingCart.size>0&&await this.processCartItems(),this.hide(),this.feedbackMessages=[],this.onClose&&this.onClose()}showFeedback(e,t="info",s=3e3){const a={id:Date.now(),message:e,type:t,timestamp:Date.now(),duration:s};this.feedbackMessages.push(a),this.renderFeedback(),setTimeout(()=>{this.removeFeedback(a.id)},s)}removeFeedback(e){this.feedbackMessages=this.feedbackMessages.filter(t=>t.id!==e),this.renderFeedback()}renderFeedback(){if(!this.container)return;const e=this.container.querySelector(".genie-feedback");if(e&&e.remove(),this.feedbackMessages.length>0){const t=document.createElement("div");t.className="genie-feedback",t.style.cssText=`
                position: absolute;
                top: 20px;
                right: 20px;
                z-index: 10;
                display: flex;
                flex-direction: column;
                gap: 10px;
                max-width: 300px;
            `,this.feedbackMessages.forEach(s=>{const a=document.createElement("div");a.className=`feedback-message feedback-${s.type}`;const i={success:{bg:"rgba(0, 255, 136, 0.9)",border:"#00ff88"},error:{bg:"rgba(255, 68, 68, 0.9)",border:"#ff4444"},warning:{bg:"rgba(255, 170, 0, 0.9)",border:"#ffaa00"},info:{bg:"rgba(0, 255, 255, 0.9)",border:"#00ffff"}},n=i[s.type]||i.info;a.style.cssText=`
                    background: ${n.bg};
                    border: 2px solid ${n.border};
                    border-radius: 8px;
                    padding: 12px 16px;
                    color: white;
                    font-size: 14px;
                    font-weight: bold;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                    animation: feedbackSlideIn 0.3s ease-out;
                    backdrop-filter: blur(5px);
                `,a.textContent=s.message,t.appendChild(a)}),this.container.appendChild(t)}}startGlowAnimation(){if(this.animationFrame)return;const e=()=>{this.glowAnimation+=.05;const t=this.container.querySelector(".genie-lamp"),s=this.container.querySelector(".token-icon");if(t){const a=Math.sin(this.glowAnimation)*.5+.5;t.style.filter=`drop-shadow(0 0 ${10+a*10}px #ffd700)`}if(s){const a=Math.sin(this.glowAnimation+1)*.5+.5;s.style.filter=`drop-shadow(0 0 ${5+a*5}px #00ffff)`}this.isVisible&&(this.animationFrame=requestAnimationFrame(e))};e()}stopGlowAnimation(){this.animationFrame&&(cancelAnimationFrame(this.animationFrame),this.animationFrame=null)}updateActivePowerUps(e){const t=this.gameEngine.playerShip;for(const[s,a]of this.activePowerUps)a.update(e,t)||(this.activePowerUps.delete(s),console.log(`Power-up expired: ${s}`))}setOnPowerUpPurchased(e){this.onPowerUpPurchased=e}setOnWarpPurchased(e){this.onWarpPurchased=e}setOnConsumablePurchased(e){this.onConsumablePurchased=e}setOnClose(e){this.onClose=e}formatPowerUpName(e){switch(e){case"EXTRA_LIFE":return"Extra Life";case"SPREAD_AMMO":return"Spread Ammo";case"EXTRA_WINGMAN":return"Wingman";case"REALITY_WARP":return"Reality Warp";case"KINETIC_BOOST":return"Kinetic Boost";case"LASER_ROUNDS":return"Laser Rounds";case"FLAME_ROUNDS":return"Flame Rounds";case"ICE_ROUNDS":return"Ice Rounds";case"PLASMA_ROUNDS":return"Plasma Rounds";case"SONIC_ROUNDS":return"Sonic Rounds";default:return e.replace(/_/g," ").toLowerCase().replace(/\b\w/g,t=>t.toUpperCase())}}destroy(){this.stopGlowAnimation(),this.container&&this.container.parentNode&&this.container.parentNode.removeChild(this.container),this.container=null,this.isInitialized=!1,document.removeEventListener("token-transaction",this.boundHandleTokenTransaction),console.log("GenieInterface destroyed")}}class at{constructor(){this.isVisible=!1,this.container=null,this.activePowerUps=new Map,this.position={x:20,y:20},this.iconSize=32,this.spacing=10,this.backgroundColor="rgba(0, 0, 0, 0.7)",this.borderColor="#ffd700",this.animationTime=0,this.pulseIntensity=0}initialize(){this.container||(this.container=document.createElement("div"),this.container.id="power-up-indicator",this.container.className="power-up-indicator",this.container.style.cssText=`
            position: fixed;
            top: ${this.position.y}px;
            left: ${this.position.x}px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: ${this.spacing}px;
            pointer-events: none;
            font-family: 'Arial', sans-serif;
        `,document.body.appendChild(this.container))}show(){this.container||this.initialize(),this.isVisible=!0,this.container.style.display="flex"}hide(){this.container&&(this.isVisible=!1,this.container.style.display="none")}updateActivePowerUps(e){this.activePowerUps=e,!this.isVisible&&e.size>0&&this.show(),this.render()}update(e){!this.isVisible||this.activePowerUps.size===0||(this.animationTime+=e/1e3,this.pulseIntensity=Math.sin(this.animationTime*2)*.3+.7,this.render())}render(){if(!(!this.container||!this.isVisible)){if(this.container.innerHTML="",this.container.style.display==="none"&&(this.container.style.display="flex"),this.activePowerUps.size===0){const e=document.createElement("div");e.className="power-up-placeholder",e.style.cssText=`
                color: rgba(255, 255, 255, 0.3);
                font-size: 12px;
                padding: 5px;
                display: none; /* Hide placeholder but keep container visible */
            `,e.textContent="No active power-ups",this.container.appendChild(e);return}for(const[e,t]of this.activePowerUps){const s=this.createPowerUpIndicator(t);this.container.appendChild(s)}}}createPowerUpIndicator(e){const t=document.createElement("div");t.className="power-up-item";const s=e.getDisplayInfo(),a=e.timeRemaining||0,i=Math.ceil(a/1e3),n=e.getTimeRemainingPercentage();let r="#00ff88";return n<.3?r="#ff4444":n<.6&&(r="#ffaa00"),t.innerHTML=`
            <div class="power-up-icon" style="
                width: ${this.iconSize}px;
                height: ${this.iconSize}px;
                background: linear-gradient(135deg, ${s.color}, ${s.glowColor});
                border: 2px solid ${r};
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 18px;
                box-shadow: 0 0 10px ${r}40;
                animation: ${n<.3?"powerUpUrgent 0.5s ease-in-out infinite alternate":"none"};
            ">
                ${s.icon}
            </div>
            <div class="power-up-info" style="
                margin-left: 8px;
                color: white;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
                min-width: 120px;
            ">
                <div class="power-up-name" style="
                    font-size: 12px;
                    font-weight: bold;
                    color: ${r};
                    margin-bottom: 2px;
                ">
                    ${this.formatPowerUpName(s.type)}
                </div>
                ${e.duration?`
                    <div class="power-up-timer" style="
                        font-size: 11px;
                        color: #cccccc;
                        display: flex;
                        align-items: center;
                        gap: 4px;
                    ">
                        <div class="timer-bar" style="
                            width: 60px;
                            height: 4px;
                            background: rgba(255, 255, 255, 0.2);
                            border-radius: 2px;
                            overflow: hidden;
                        ">
                            <div class="timer-fill" style="
                                width: ${n*100}%;
                                height: 100%;
                                background: ${r};
                                transition: width 0.1s ease;
                            "></div>
                        </div>
                        <span>${i}s</span>
                    </div>
                `:`
                    <div class="power-up-level" style="
                        font-size: 11px;
                        color: #00ff88;
                    ">
                        Active for Level
                    </div>
                `}
            </div>
        `,t.style.cssText=`
            display: flex;
            align-items: center;
            background: ${this.backgroundColor};
            border: 1px solid ${this.borderColor};
            border-radius: 10px;
            padding: 8px;
            margin-bottom: 4px;
            backdrop-filter: blur(5px);
            opacity: ${this.pulseIntensity};
        `,t}formatPowerUpName(e){switch(e){case"EXTRA_LIFE":return"Extra Life";case"SPREAD_AMMO":return"Spread Ammo";case"EXTRA_WINGMAN":return"Wingman";case"REALITY_WARP":return"Reality Warp";case"RAPID_FIRE":return"Rapid Fire";case"SHIELD":return"Shield";case"RICOCHET_ROUNDS":return"Ricochet Rounds";case"KINETIC_BOOST":return"Kinetic Rounds";case"LASER_ROUNDS":return"Laser Rounds";case"FLAME_ROUNDS":return"Flame Rounds";case"ICE_ROUNDS":return"Ice Rounds";case"PLASMA_ROUNDS":return"Plasma Rounds";case"SONIC_ROUNDS":return"Sonic Rounds";default:return e.replace(/_/g," ").toLowerCase().replace(/\b\w/g,t=>t.toUpperCase())}}setPosition(e,t){this.position={x:e,y:t},this.container&&(this.container.style.left=`${e}px`,this.container.style.top=`${t}px`)}destroy(){this.container&&(this.container.remove(),this.container=null),this.activePowerUps.clear()}}if(!document.getElementById("power-up-indicator-styles")){const u=document.createElement("style");u.id="power-up-indicator-styles",u.textContent=`
        @keyframes powerUpUrgent {
            from { 
                box-shadow: 0 0 10px #ff444440;
                transform: scale(1);
            }
            to { 
                box-shadow: 0 0 20px #ff4444;
                transform: scale(1.05);
            }
        }
    `,document.head.appendChild(u)}class it{constructor(){this.isVisible=!1,this.boostLevel=100,this.boostActive=!1,this.maxBoostLevel=100,this.boostDrainRate=20,this.boostRefillRate=10,this.width=200,this.height=20,this.backgroundColor="rgba(0, 0, 0, 0.7)",this.borderColor="#ffffff",this.animationTime=0}show(){this.isVisible=!0}hide(){this.isVisible=!1}setBoostLevel(e){this.boostLevel=Math.max(0,Math.min(this.maxBoostLevel,e))}getBoostLevel(){return this.boostLevel}isBoostAvailable(){return this.boostLevel>0}setBoostActive(e){this.boostActive=e}update(e){if(!this.isVisible)return;const t=e/1e3;this.boostActive&&this.boostLevel>0?(this.boostLevel=Math.max(0,this.boostLevel-this.boostDrainRate*t),this.boostLevel<=0&&(this.boostActive=!1)):!this.boostActive&&this.boostLevel<this.maxBoostLevel&&(this.boostLevel=Math.min(this.maxBoostLevel,this.boostLevel+this.boostRefillRate*t)),this.animationTime+=e/1e3}render(e,t,s,a,i){if(!this.isVisible)return;a&&(this.width=a),i&&(this.height=i);let n="#00ff00";this.boostLevel<30?n="#ff4444":this.boostLevel<60&&(n="#ffaa00"),e.fillStyle="#ffffff",e.font='bold 12px "Courier New", monospace',e.textAlign="left",e.shadowColor="#000000",e.shadowBlur=3,e.fillText("BOOST",t,s-5),e.textAlign="right",e.fillStyle=n,e.fillText(`${Math.round(this.boostLevel)}%`,t+this.width,s-5),e.shadowBlur=0,e.fillStyle=this.backgroundColor,e.fillRect(t,s,this.width,this.height),e.strokeStyle=this.borderColor,e.lineWidth=1,e.strokeRect(t,s,this.width,this.height);const r=(this.width-4)*(this.boostLevel/100);if(r>0){const o=e.createLinearGradient(t+2,s,t+2+r,s);if(o.addColorStop(0,n),o.addColorStop(1,n+"aa"),e.fillStyle=o,e.fillRect(t+2,s+2,r,this.height-4),this.boostActive){e.shadowColor=n,e.shadowBlur=10,e.fillRect(t+2,s+2,r,this.height-4),e.shadowBlur=0;const c=this.animationTime*100%(this.width+40)-20,h=e.createLinearGradient(t+c,s,t+c+20,s);h.addColorStop(0,"transparent"),h.addColorStop(.5,"rgba(255, 255, 255, 0.3)"),h.addColorStop(1,"transparent"),e.fillStyle=h,e.fillRect(t+2,s+2,r,this.height-4)}}}destroy(){}}var R={GG_GET_GAME_DATA:"GG_GET_GAME_DATA",GG_UPDATE_GAME_DATA:"GG_UPDATE_GAME_DATA",GG_SET_GAME_DATA:"GG_SET_GAME_DATA",GG_PAUSED_FROM_GAME:"GG_PAUSED_FROM_GAME",GG_PAUSED_FROM_PARENT:"GG_PAUSED_FROM_PARENT",GG_QUIT_FROM_PARENT:"GG_QUIT_FROM_PARENT",GG_GAME_OVER:"GG_GAME_OVER",GG_RESUMED_FROM_GAME:"GG_RESUMED_FROM_GAME",GG_RESUMED_FROM_PARENT:"GG_RESUMED_FROM_PARENT",GG_GAME_LOAD_FINISHED:"GG_GAME_LOAD_FINISHED"},ue=function(){function u(){this.registeredListeners=[]}return u.prototype.getTargetWindow=function(){try{if(window.top&&window.top!==window)return window.top}catch(e){console.warn("window.top access failed:",e.message)}try{if(window.parent&&window.parent!==window)return window.parent}catch(e){console.warn("window.parent access failed:",e.message)}return null},u.getInstance=function(){return u.instance||(u.instance=new u),u.instance},u.prototype.checkRegisteredListenersAndAdd=function(e,t){this.registeredListeners.includes(e)||(window.addEventListener("message",t),this.registeredListeners.push(e))},u.prototype.registerListener=function(e,t){this.getTargetWindow()?this.checkRegisteredListenersAndAdd(e,function(s){s.data.event_type===e&&t()}):console.error("Functions should be called from inside an iframe")},u.prototype.getGameData=function(e,t){var s=this.getTargetWindow();if(s){var a=setTimeout(function(){t(e)},3e3);this.checkRegisteredListenersAndAdd(R.GG_SET_GAME_DATA,function(i){i.data.event_type===R.GG_SET_GAME_DATA&&(clearTimeout(a),t(i.data.payload.gameData))}),s.postMessage({event_type:R.GG_GET_GAME_DATA,payload:{defaultData:e}},"*")}else console.error("Functions should be called from inside an iframe")},u.prototype.saveGameData=function(e){var t=this.getTargetWindow();t?t.postMessage({event_type:R.GG_UPDATE_GAME_DATA,payload:{data:e}},"*"):console.error("Functions should be called from inside an iframe")},u.prototype.gameOver=function(e){var t=this.getTargetWindow();t?(console.log("sending game over to Goama",e),t.postMessage({event_type:R.GG_GAME_OVER,payload:{score:e}},"*")):console.error("Functions should be called from inside an iframe")},u.prototype.gamePaused=function(){var e=this.getTargetWindow();e?e.postMessage({event_type:R.GG_PAUSED_FROM_GAME},"*"):console.error("Functions should be called from inside an iframe")},u.prototype.gameResumed=function(){var e=this.getTargetWindow();e?e.postMessage({event_type:R.GG_RESUMED_FROM_GAME},"*"):console.error("Functions should be called from inside an iframe")},u.prototype.gameLoaded=function(){var e=this.getTargetWindow();e?e.postMessage({event_type:R.GG_GAME_LOAD_FINISHED},"*"):console.error("Functions should be called from inside an iframe")},u.prototype.listenPaused=function(e){this.registerListener(R.GG_PAUSED_FROM_PARENT,e)},u.prototype.listenResumed=function(e){this.registerListener(R.GG_RESUMED_FROM_PARENT,e)},u.prototype.listenQuit=function(e){this.registerListener(R.GG_QUIT_FROM_PARENT,e)},u}(),k=ue.getInstance();typeof window<"u"&&(window.GGSDK=k);var pe=k.getGameData,ge=k.saveGameData,Y=k.gameOver,me=k.gamePaused,fe=k.gameResumed,q=k.gameLoaded,X=k.listenPaused,we=k.listenResumed,ve=k.listenQuit;const O=Object.freeze(Object.defineProperty({__proto__:null,EVENT_TYPES:R,GGSDK:ue,default:k,gameLoaded:q,gameOver:Y,gamePaused:me,gameResumed:fe,getGameData:pe,listenPaused:X,listenQuit:ve,listenResumed:we,saveGameData:ge},Symbol.toStringTag,{value:"Module"}));class nt{constructor(){this.isSDKReady=!1,this.isInitialized=!1,this.isDebugMode=this.checkDebugMode(),this.currentSessionId=this.generateSessionId(),this.playerData={totalScore:0,highestLevel:0,levelsCompleted:[],levelBestScores:{},totalPlayTime:0,perfectCompletions:0,totalEnemiesDefeated:0,averageCompletionTime:0,specialStatuses:{loginStreak:0,lastLoginDate:null,bonusLivesEarned:0,tournamentParticipant:!1,achievementUnlocks:[]},hangarUpgrades:{player:{engine_tuning:0,hull_plating:0,reactor_core:0,firepower:0,extra_lives:0},wingman:{wingman_armor:0,wingman_firepower:0}},currentSession:{sessionId:this.currentSessionId,startTime:Date.now(),levelsPlayedThisSession:0,tokensEarnedThisSession:0,scoreThisSession:0},lastSaveTime:Date.now(),gameVersion:"1.0.0",totalSessions:0,sessionHighScores:{}},this.saveInProgress=!1,this.pendingSaveData=null,this.saveRetryCount=0,this.maxRetries=3,this.retryDelay=1e3,this.onDataSavedCallback=null,this.onSaveErrorCallback=null,this.onSpecialStatusCallback=null,this.autoSaveEnabled=!0,this.autoSaveInterval=3e4,this.lastAutoSave=Date.now()}checkDebugMode(){return!!(window.AuthManager&&window.AuthManager.config&&window.AuthManager.config.debugMode||window.location.search.includes("debug=true")||window.DEBUG_MODE||window.location.hostname==="localhost"||window.location.hostname==="127.0.0.1")}generateSessionId(){return`session_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}async initialize(){if(!this.isInitialized){if(!this.isDebugMode){if(!O){console.error("GGSDK is not loaded"),this.isSDKReady=!1;return}console.log("GGSDK loaded successfully:",Object.keys(O))}if(this.isDebugMode){console.log("Debug mode detected - using localStorage instead of Orange SDK"),this.isSDKReady=!0,this.isInitialized=!0,this.loadFromLocalStorage(),this.playerData.currentSession.startTime=Date.now(),this.playerData.totalSessions++;return}try{this.playerData.currentSession.startTime=Date.now(),this.playerData.totalSessions++,this.setupSDKEventListeners(),await this.checkSpecialStatuses(),typeof q=="function"?q():console.error("GGSDK.gameLoaded is not a function"),this.isSDKReady=!0,this.isInitialized=!0,console.log("Orange SDK initialized successfully"),await this.savePlayerData("game_start")}catch(e){console.error("Failed to initialize OrangeSDKManager:",e),this.isSDKReady=!1}}}setupSDKEventListeners(){if(!this.isDebugMode){if(!O||typeof X!="function"){console.error("GGSDK is not properly loaded or listenPaused is not a function"),console.log("GGSDK object keys:",O?Object.keys(O):"undefined");return}try{X(()=>{this.handleGamePause()})}catch(e){console.error("Failed to set up listenPaused:",e)}try{we(()=>{this.handleGameResume()})}catch(e){console.error("Failed to set up listenResumed:",e)}try{ve(()=>{this.handleGameQuit()})}catch(e){console.error("Failed to set up listenQuit:",e)}}}async checkSpecialStatuses(){if(!this.isDebugMode)try{const e={specialStatuses:this.playerData.specialStatuses};pe(e,t=>{if(t&&t.specialStatuses){const s=t.specialStatuses,a=new Date().toDateString(),i=s.lastLoginDate;if(i){const n=new Date(i),r=Math.floor((Date.now()-n.getTime())/(1e3*60*60*24));r===1?this.playerData.specialStatuses.loginStreak=(s.loginStreak||0)+1:r>1?this.playerData.specialStatuses.loginStreak=1:this.playerData.specialStatuses.loginStreak=s.loginStreak||1}else this.playerData.specialStatuses.loginStreak=1;this.playerData.specialStatuses.lastLoginDate=a,this.playerData.specialStatuses.bonusLivesEarned=s.bonusLivesEarned||0,this.playerData.specialStatuses.tournamentParticipant=s.tournamentParticipant||!1,this.playerData.specialStatuses.achievementUnlocks=s.achievementUnlocks||[],this.processSpecialStatusBonuses()}})}catch(e){console.error("Error checking special statuses:",e)}}processSpecialStatusBonuses(){const e=this.playerData.specialStatuses.loginStreak;if(e>=2&&e<=7){const t=Math.min(e-1,3);this.playerData.specialStatuses.bonusLivesEarned=t,this.onSpecialStatusCallback&&this.onSpecialStatusCallback("bonus_lives",t)}this.playerData.specialStatuses.tournamentParticipant&&this.onSpecialStatusCallback&&this.onSpecialStatusCallback("tournament_participant",!0)}updatePlayerProgress(e){if(!e)return;if(e.score!==void 0&&(this.playerData.totalScore=Math.max(this.playerData.totalScore,e.score),this.playerData.currentSession.scoreThisSession+=e.scoreGained||0),e.level!==void 0&&(this.playerData.highestLevel=Math.max(this.playerData.highestLevel,e.level)),e.levelCompleted!==void 0){const a=e.levelCompleted;if(this.playerData.levelsCompleted.includes(a)||(this.playerData.levelsCompleted.push(a),this.playerData.currentSession.levelsPlayedThisSession++),e.levelScore!==void 0){const i=this.playerData.levelBestScores[a]||0;this.playerData.levelBestScores[a]=Math.max(i,e.levelScore)}}if(e.perfectCompletion&&this.playerData.perfectCompletions++,e.enemiesDefeated!==void 0&&(this.playerData.totalEnemiesDefeated+=e.enemiesDefeated),e.completionTime!==void 0){const a=this.playerData.levelsCompleted.length;a>0&&(this.playerData.averageCompletionTime=(this.playerData.averageCompletionTime*(a-1)+e.completionTime)/a)}const t=Date.now(),s=t-this.playerData.currentSession.startTime;this.playerData.totalPlayTime+=s,this.playerData.currentSession.startTime=t}loadFromLocalStorage(){if(this.isDebugMode)try{const e=localStorage.getItem("orangeDefenseGameData");if(e){const t=JSON.parse(e);Object.assign(this.playerData,t)}}catch(e){console.error("Failed to load data from localStorage:",e)}}saveToLocalStorage(e){if(this.isDebugMode)try{localStorage.setItem("orangeDefenseGameData",JSON.stringify(e))}catch(t){console.error("Failed to save data to localStorage:",t)}}async savePlayerData(e="manual",t=!1){if(!this.isSDKReady)return console.warn("SDK not ready, cannot save data"),!1;if(this.saveInProgress&&!t)return this.pendingSaveData={...this.playerData},!1;this.saveInProgress=!0,this.playerData.lastSaveTime=Date.now();const s={...this.playerData,saveReason:e,saveTimestamp:Date.now()};if(this.isDebugMode)return this.saveToLocalStorage(s),this.onDataSavedCallback&&this.onDataSavedCallback(s,e),this.pendingSaveData&&(this.pendingSaveData,this.pendingSaveData=null,setTimeout(()=>this.savePlayerData("pending_update"),100)),this.saveInProgress=!1,!0;try{if(await this.performSaveWithRetry(s),this.onDataSavedCallback&&this.onDataSavedCallback(s,e),this.pendingSaveData){const a=this.pendingSaveData;this.pendingSaveData=null,setTimeout(()=>this.savePlayerData("pending_update"),100)}return!0}catch(a){return console.error("Failed to save player data:",a),this.onSaveErrorCallback&&this.onSaveErrorCallback(a,e),!1}finally{this.saveInProgress=!1,this.saveRetryCount=0}}async performSaveWithRetry(e){for(let t=0;t<=this.maxRetries;t++)try{await new Promise((s,a)=>{try{ge(e),setTimeout(s,100)}catch(i){a(i)}});return}catch(s){if(this.saveRetryCount=t+1,t<this.maxRetries)console.warn(`Save attempt ${t+1} failed, retrying in ${this.retryDelay}ms:`,s),await new Promise(a=>setTimeout(a,this.retryDelay)),this.retryDelay*=2;else throw s}}async onLevelCompleted(e){var s,a,i,n,r;if(!e)return;const t=((s=e.score)==null?void 0:s.totalScore)||0;if(t>0){const o=this.playerData.sessionHighScores[this.currentSessionId]||0;t>o&&(this.playerData.sessionHighScores[this.currentSessionId]=t)}if(this.updatePlayerProgress({level:e.levelNumber,levelCompleted:e.levelNumber,levelScore:((a=e.score)==null?void 0:a.totalScore)||0,scoreGained:((i=e.score)==null?void 0:i.totalScore)||0,sessionId:this.currentSessionId,sessionHighScore:this.playerData.sessionHighScores[this.currentSessionId]||0,tokensEarned:((n=e.score)==null?void 0:n.tokenReward)||0,perfectCompletion:e.perfectCompletion,enemiesDefeated:e.enemiesDefeated,completionTime:e.completionTime}),!this.isDebugMode){const o=((r=e.score)==null?void 0:r.totalScore)||0;Y(o)}await this.savePlayerData("level_completed")}handleGamePause(){this.isDebugMode||me(),this.savePlayerData("game_paused")}handleGameResume(){this.isDebugMode||fe(),this.playerData.currentSession.startTime=Date.now()}async handleGameQuit(){const e=Date.now()-this.playerData.currentSession.startTime;this.playerData.totalPlayTime+=e,this.isDebugMode||Y(this.playerData.totalScore),await this.savePlayerData("game_quit")}updateAutoSave(){if(!this.autoSaveEnabled||!this.isSDKReady)return;const e=Date.now();e-this.lastAutoSave>=this.autoSaveInterval&&(this.savePlayerData("auto_save"),this.lastAutoSave=e)}getPlayerData(){return{...this.playerData}}getSpecialStatuses(){return{...this.playerData.specialStatuses}}getCurrentSessionHighScore(){return this.playerData.sessionHighScores[this.currentSessionId]||0}getCurrentSessionId(){return this.currentSessionId}unlockAchievement(e){this.playerData.specialStatuses.achievementUnlocks.includes(e)||(this.playerData.specialStatuses.achievementUnlocks.push(e),this.savePlayerData("achievement_unlock"),this.onSpecialStatusCallback&&this.onSpecialStatusCallback("achievement_unlock",e))}setTournamentParticipant(e){this.playerData.specialStatuses.tournamentParticipant=e,this.savePlayerData("tournament_status"),this.onSpecialStatusCallback&&this.onSpecialStatusCallback("tournament_participant",e)}setOnDataSavedCallback(e){this.onDataSavedCallback=e}setOnSaveErrorCallback(e){this.onSaveErrorCallback=e}setOnSpecialStatusCallback(e){this.onSpecialStatusCallback=e}setAutoSave(e,t=3e4){this.autoSaveEnabled=e,this.autoSaveInterval=t,e&&(this.lastAutoSave=Date.now())}getSaveStatistics(){return{isSDKReady:this.isSDKReady,saveInProgress:this.saveInProgress,saveRetryCount:this.saveRetryCount,lastSaveTime:this.playerData.lastSaveTime,autoSaveEnabled:this.autoSaveEnabled,autoSaveInterval:this.autoSaveInterval,lastAutoSave:this.lastAutoSave}}reset(){this.playerData={totalScore:0,highestLevel:0,levelsCompleted:[],levelBestScores:{},totalPlayTime:0,perfectCompletions:0,totalEnemiesDefeated:0,averageCompletionTime:0,specialStatuses:{loginStreak:0,lastLoginDate:null,bonusLivesEarned:0,tournamentParticipant:!1,achievementUnlocks:[]},currentSession:{startTime:Date.now(),levelsPlayedThisSession:0,tokensEarnedThisSession:0,scoreThisSession:0},lastSaveTime:Date.now(),gameVersion:"1.0.0",totalSessions:0},this.saveInProgress=!1,this.pendingSaveData=null,this.saveRetryCount=0,this.retryDelay=1e3}}const L={ENGINE_TUNING:{id:"engine_tuning",name:"Engine Tuning",description:"Increases ship speed and acceleration",maxLevel:10,baseCost:2500,costMultiplier:1.5,affects:["maxSpeed","acceleration"],baseValues:{maxSpeed:300,acceleration:800},maxMultiplier:2},HULL_PLATING:{id:"hull_plating",name:"Hull Plating",description:"Increases maximum health",maxLevel:10,baseCost:3e3,costMultiplier:1.6,affects:["maxHealth"],baseValues:{maxHealth:100},maxMultiplier:2.5},REACTOR_CORE:{id:"reactor_core",name:"Reactor Core",description:"Increases weapon fire rate",maxLevel:10,baseCost:4e3,costMultiplier:1.7,affects:["fireRate"],baseValues:{fireRate:250},maxMultiplier:.4},FIREPOWER:{id:"firepower",name:"Firepower",description:"Increases projectile damage",maxLevel:10,baseCost:3500,costMultiplier:1.6,affects:["projectileDamage"],baseValues:{projectileDamage:25},maxMultiplier:3},EXTRA_LIVES:{id:"extra_lives",name:"Extra Lives",description:"Increases starting lives",maxLevel:5,baseCost:1e4,costMultiplier:2,affects:["maxLives"],baseValues:{maxLives:3},maxMultiplier:1.67}};class rt{constructor(e,t){this.tokenEconomyManager=e,this.orangeSDKManager=t,this.playerUpgrades=this.loadPlayerUpgrades(),this.upgradeListeners=[]}loadPlayerUpgrades(){var s,a,i;const e=((i=(a=(s=this.orangeSDKManager)==null?void 0:s.playerData)==null?void 0:a.hangarUpgrades)==null?void 0:i.player)||{},t={};for(const[n,r]of Object.entries(L))t[r.id]=e[r.id]||0;return t}reloadUpgradeData(){this.playerUpgrades=this.loadPlayerUpgrades()}saveUpgrades(){this.orangeSDKManager&&(this.orangeSDKManager.playerData.hangarUpgrades||(this.orangeSDKManager.playerData.hangarUpgrades={}),this.orangeSDKManager.playerData.hangarUpgrades.player={...this.playerUpgrades},this.orangeSDKManager.savePlayerData())}calculateUpgradeCost(e,t){return t>=e.maxLevel?0:Math.floor(e.baseCost*Math.pow(e.costMultiplier,t))}purchasePlayerUpgrade(e){const t=Object.values(L).find(o=>o.id===e);if(!t)return{success:!1,reason:"invalid_upgrade"};const s=this.playerUpgrades[e];if(s>=t.maxLevel)return{success:!1,reason:"max_level_reached"};const a=this.calculateUpgradeCost(t,s),i=this.tokenEconomyManager.getBalance(),n=this.tokenEconomyManager.canAfford(a);if(console.log(`[HangarManager] Upgrade purchase check: ${e}, Cost: ${a}, Balance: ${i}, CanAfford: ${n}`),!n)return console.log(`[HangarManager] Purchase failed - insufficient tokens. Required: ${a}, Available: ${i}`),{success:!1,reason:"insufficient_tokens",cost:a,balance:i};console.log(`[HangarManager] Attempting to spend ${a} tokens for ${e}`);const r=this.tokenEconomyManager.spendTokens(a,"hangar_upgrade",{upgradeId:e,upgradeLevel:s+1,upgradeName:t.name});return console.log("[HangarManager] Spend result:",r),r.success?(this.playerUpgrades[e]=s+1,console.log("Post-increment level for hull_plating:",this.playerUpgrades.hull_plating),this.saveUpgrades(),console.log("Notifying for hull_plating level:",s+1),this.notifyUpgradeListeners(e,s+1),document.dispatchEvent(new CustomEvent("token-transaction",{detail:r})),{success:!0,upgradeId:e,newLevel:s+1,cost:a,transactionId:r.transactionId}):(console.log("[HangarManager] Transaction failed:",r.reason),{success:!1,reason:"transaction_failed"})}calculatePlayerStats(){const e={};for(const[t,s]of Object.entries(L)){const i=this.playerUpgrades[s.id]/s.maxLevel;for(const n of s.affects){const r=s.baseValues[n],o=s.maxMultiplier;n==="fireRate"?e[n]=r*(1-i*(1-o)):e[n]=r*(1+i*(o-1))}}return e}getUpgradeInfo(){const e=L,t=this.playerUpgrades;return Object.values(e).map(s=>{const a=t[s.id],i=this.calculateUpgradeCost(s,a),n=this.tokenEconomyManager.canAfford(i),r=a>=s.maxLevel;return{id:s.id,name:s.name,description:s.description,currentLevel:a,maxLevel:s.maxLevel,nextCost:r?0:i,canAfford:n,isMaxLevel:r,affects:s.affects}})}addUpgradeListener(e){this.upgradeListeners.push(e)}removeUpgradeListener(e){const t=this.upgradeListeners.indexOf(e);t>-1&&this.upgradeListeners.splice(t,1)}notifyUpgradeListeners(e,t){console.log("Notifying upgrade listeners for:",e,"level:",t);for(const s of this.upgradeListeners)try{s(e,t)}catch(a){console.error("Error in upgrade listener:",a)}}getUpgradeInvestment(){let e=0,t=0;for(const[s,a]of Object.entries(L)){const i=this.playerUpgrades[a.id];t+=i;for(let n=0;n<i;n++)e+=this.calculateUpgradeCost(a,n)}return{totalSpent:e,playerUpgradeLevels:t,totalUpgradeLevels:t}}}class ot{constructor(e,t){this.tokenManager=e,this.gameEngine=t,this.currentConsumable=null,this.availableConsumables=K.createAllConsumables(),this.isActivating=!1,this.lastActivationTime=0,this.activationCooldown=1e3,this.onInventoryUpdateCallback=null,this.onConsumableUsedCallback=null,this.loadInventory()}addConsumableToInventory(e){if(this.currentConsumable)return{success:!1,reason:"inventory_full",message:"You can only carry one consumable at a time. Use your current consumable first."};const t=this.availableConsumables.find(s=>s.type===e);return t?(this.currentConsumable=t.clone(),this.saveInventory(),this.triggerInventoryUpdate(),console.log(`Added consumable to inventory: ${e}`),{success:!0,consumable:this.currentConsumable}):{success:!1,reason:"invalid_consumable",message:"Unknown consumable type"}}async activateConsumable(){if(!this.currentConsumable)return{success:!1,reason:"no_consumable",message:"No consumable available"};if(this.isActivating)return{success:!1,reason:"already_activating",message:"Consumable activation in progress"};const e=Date.now();if(e-this.lastActivationTime<this.activationCooldown)return{success:!1,reason:"cooldown",message:"Consumable activation on cooldown"};if(this.currentConsumable.isUsed)return{success:!1,reason:"already_used",message:"Consumable already used"};this.isActivating=!0,this.lastActivationTime=e;try{if(await this.currentConsumable.activate(this.gameEngine)){const s=this.currentConsumable;return this.currentConsumable=null,this.saveInventory(),this.triggerInventoryUpdate(),this.triggerConsumableUsed(s),console.log(`Activated consumable: ${s.type}`),{success:!0,consumable:s,message:`${s.name} activated successfully!`}}else return{success:!1,reason:"activation_failed",message:"Failed to activate consumable"}}catch(t){return console.error("Error activating consumable:",t),{success:!1,reason:"activation_error",message:"Error occurred during activation"}}finally{this.isActivating=!1}}getCurrentConsumable(){return this.currentConsumable}hasConsumable(){return this.currentConsumable!==null}getAvailableConsumables(){return this.availableConsumables}saveInventory(){try{const e={currentConsumable:this.currentConsumable?{type:this.currentConsumable.type,isUsed:this.currentConsumable.isUsed,usedAt:this.currentConsumable.usedAt}:null};localStorage.setItem("consumable_inventory",JSON.stringify(e))}catch(e){console.warn("Failed to save consumable inventory:",e)}}loadInventory(){try{const e=localStorage.getItem("consumable_inventory");if(e){const t=JSON.parse(e);t.currentConsumable&&(this.currentConsumable=K.createConsumable(t.currentConsumable.type),this.currentConsumable&&(this.currentConsumable.isUsed=t.currentConsumable.isUsed,this.currentConsumable.usedAt=t.currentConsumable.usedAt))}}catch(e){console.warn("Failed to load consumable inventory:",e),this.currentConsumable=null}}reset(){this.currentConsumable=null,this.isActivating=!1,this.lastActivationTime=0,this.saveInventory(),this.triggerInventoryUpdate()}setOnInventoryUpdate(e){this.onInventoryUpdateCallback=e}setOnConsumableUsed(e){this.onConsumableUsedCallback=e}triggerInventoryUpdate(){this.onInventoryUpdateCallback&&this.onInventoryUpdateCallback(this.currentConsumable)}triggerConsumableUsed(e){this.onConsumableUsedCallback&&this.onConsumableUsedCallback(e)}}class lt{constructor(e,t=null){this.canvas=e,this.ctx=e.getContext("2d"),this.gameEngine=t,this.state="idle",this.currentWarp={type:"reality_warp",duration:6e3,elapsed:0,cooldown:0,promptSnippet:null},this.theme=this.getRealityWarpTheme(),this.overlay={alpha:0,targetAlpha:0},this.vignette={strength:0,target:0},this.tint={r:0,g:0,b:0,strength:0},this.noise={time:0},this.screenShake={x:0,y:0,t:0,amp:0},this.particles=[],this.freeParticles=[],this.maxParticles=400,this.streaks=[],this.showUI=!0,this.debug=!1,this.lod=1,this.audioCtx=null,this.ambientNode=null,this.transitionInMs=900,this.transitionOutMs=700,this.initiationMs=500,this.metrics={particles:0,frameMsAvg:0,frames:0},this._lastPerfSample=performance.now()}initDevControls(){this._devControlsAttached||(this._devControlsAttached=!0,window.addEventListener("keydown",e=>{e.code==="KeyV"&&(this.debug=!this.debug),e.code==="KeyU"&&(this.showUI=!this.showUI),e.code==="Digit1"&&this.startWarp({type:"reality_warp",duration:6e3}),e.code==="KeyK"&&this.endWarp(!0),e.code==="KeyF"&&this.showError("Debug Warp Error"),e.code==="KeyC"&&this.startCooldown(3e3)}),window.WarpVFX=this)}setDebugEnabled(e){this.debug=!!e}setUIVisible(e){this.showUI=!!e}setTheme(){this.theme=this.getRealityWarpTheme()}startWarp({type:e="reality_warp",duration:t=6e3,effects:s=[],prompt:a=null}={}){this.setTheme(),this.currentWarp.type="reality_warp",this.currentWarp.duration=t,this.currentWarp.elapsed=0,this.currentWarp.promptSnippet=a?String(a).slice(0,80):null,this.state="initiating",this.overlay.targetAlpha=.85*this.theme.overlayAlpha,this.vignette.target=.8*this.theme.vignette,this.tint.strength=0,this.screenShake.amp=this.theme.shake,this.spawnInitiationBurst(),this.playSound("init")}endWarp(e=!0){this.state==="idle"||this.state==="cooldown"||(this.state="transitioning_out",this.spawnCompletionBurst(e),this.playSound(e?"complete":"error"))}startCooldown(e=0){this.state="cooldown",this.currentWarp.cooldown=e,this.currentWarp.elapsed=0,this.overlay.targetAlpha=.15*this.theme.overlayAlpha,this.vignette.target=.3*this.theme.vignette,this.stopAmbient()}endCooldown(){this.state="idle",this.overlay.targetAlpha=0,this.vignette.target=0,this.stopAmbient()}showError(e="Warp failed"){this.state="error",this.overlay.targetAlpha=.9,this.tint={r:1,g:0,b:0,strength:.6},this.screenShake.amp=12,this.spawnCompletionBurst(!1),this.playSound("error"),setTimeout(()=>this.endCooldown(),800)}registerWithWarpManager(e){!e||typeof e.setCallback!="function"||(e.setCallback("warpStart",t=>{this.startWarp({type:"reality_warp",duration:(t==null?void 0:t.duration)||1/0,effects:(t==null?void 0:t.effects)||[],prompt:t==null?void 0:t.prompt})}),e.setCallback("warpEnd",t=>{this.endWarp(!0)}),e.setCallback("cooldownStart",t=>{this.startCooldown(t||0)}),e.setCallback("cooldownEnd",()=>{this.endCooldown()}),e.setCallback("promptGenerated",t=>{this.currentWarp.promptSnippet=String(t||"").slice(0,80)}))}update(e){const t=this.gameEngine&&this.gameEngine.currentFPS||60,s=t>=58?1:t>=45?.8:t>=30?.65:.5;if(this.lod+=(s-this.lod)*.05,this.currentWarp.elapsed+=e,this.overlay.alpha+=(this.overlay.targetAlpha-this.overlay.alpha)*.12,this.vignette.strength+=(this.vignette.target-this.vignette.strength)*.1,this.state==="transitioning_in"||this.state==="active"){const n=this.theme.tintStrength;this.tint.strength+=(n-this.tint.strength)*.06}else this.tint.strength+=(0-this.tint.strength)*.08;this.screenShake.t+=e*.006,this.screenShake.amp*=.98,this.noise.time+=e*.0015,this.state==="initiating"&&this.currentWarp.elapsed>=this.initiationMs?(this.state="transitioning_in",this.currentWarp.elapsed=0,this.startAmbient()):this.state==="transitioning_in"&&this.currentWarp.elapsed>=this.transitionInMs?(this.state="active",this.currentWarp.elapsed=0):this.state==="transitioning_out"&&this.currentWarp.elapsed>=this.transitionOutMs&&(this.state="cooldown",this.currentWarp.elapsed=0,this.stopAmbient()),this.updateParticles(e),this.updateStreaks(e),this.metrics.particles=this.particles.length;const a=performance.now(),i=a-this._lastPerfSample;this._lastPerfSample=a,this.metrics.frames+=1,this.metrics.frameMsAvg+=(i-this.metrics.frameMsAvg)*.05}render(e){if(e||(e=this.ctx),!!e){if(e.save(),this.screenShake.amp>.2){const t=Math.sin(this.screenShake.t*6.3)*this.screenShake.amp,s=Math.cos(this.screenShake.t*7.1)*this.screenShake.amp;e.translate(t,s)}this.overlay.alpha>.01&&this.drawOverlay(e),this.drawStreaks(e),this.drawParticles(e),e.restore()}}getRealityWarpTheme(){return{name:"reality_warp",overlayAlpha:.55,vignette:.7,tint:"#7b61ff",tintStrength:.12,gradient:["rgba(40,0,80,0.6)","rgba(0,120,200,0.5)"],particleColor:"#9da6ff",particleSecondary:"#7af9ff",shake:4,sound:{base:320}}}spawnInitiationBurst(){const e=this.canvas.width/2,t=this.canvas.height/2,s=Math.floor(80*this.lod);for(let a=0;a<s;a++){const i=a/s*Math.PI*2,n=60+Math.random()*240,r=600+Math.random()*500;this.emitParticle({x:e,y:t},i,n,r,2+Math.random()*3,a%2===0)}this.spawnStreakWave({x:e,y:t},2)}spawnCompletionBurst(e){const t=this.canvas.width/2,s=this.canvas.height/2,a=Math.floor((e?60:40)*this.lod);for(let i=0;i<a;i++){const n=Math.random()*Math.PI*2,r=40+Math.random()*(e?180:100),o=400+Math.random()*400;this.emitParticle({x:t,y:s},n,r,o,2+Math.random()*2,i%3===0)}this.spawnStreakWave({x:t,y:s},e?1:.6)}emitParticle(e,t,s,a,i,n=!1){const r=this.freeParticles.pop()||{};r.x=e.x,r.y=e.y,r.vx=Math.cos(t)*s,r.vy=Math.sin(t)*s,r.life=a,r.age=0,r.size=i,r.alpha=1,r.color=n?this.theme.particleSecondary:this.theme.particleColor,this.particles.push(r)}updateParticles(e){for(let i=this.particles.length-1;i>=0;i--){const n=this.particles[i];if(n.age+=e,n.age>=n.life){this.particles.splice(i,1),this.freeParticles.push(n);continue}n.vx*=.98,n.vy*=.98,n.x+=n.vx*(e/1e3),n.y+=n.vy*(e/1e3),n.alpha=Math.max(0,1-n.age/n.life-.0025*(1-this.lod))}const a=Math.floor(this.maxParticles*this.lod);this.particles.length>a&&(this.particles.length=a)}drawParticles(e){const t=this.canvas.width,s=this.canvas.height,a=16;for(const i of this.particles)i.alpha<=.01||i.x<-a||i.x>t+a||i.y<-a||i.y>s+a||(e.globalAlpha=i.alpha,e.fillStyle=i.color,e.beginPath(),e.arc(i.x,i.y,i.size,0,Math.PI*2),e.fill());e.globalAlpha=1}spawnStreakWave(e,t=1){const s=Math.floor(5*t*this.lod);for(let a=0;a<s;a++)this.streaks.push({x:e.x,y:e.y,r:0,w:8+Math.random()*18,alpha:.4,speed:220+Math.random()*220})}updateStreaks(e){for(let t=this.streaks.length-1;t>=0;t--){const s=this.streaks[t];s.r+=s.speed*(e/1e3),s.alpha*=.985,s.w*=.995,s.alpha<.03&&this.streaks.splice(t,1)}}drawStreaks(e){if(this.streaks.length!==0){e.save(),e.strokeStyle=this.theme.particleSecondary;for(const t of this.streaks)e.globalAlpha=t.alpha,e.lineWidth=t.w,e.beginPath(),e.arc(t.x,t.y,t.r,0,Math.PI*2),e.stroke();e.restore(),e.globalAlpha=1}}drawOverlay(e){const{width:t,height:s}=this.canvas,a=e.createRadialGradient(t/2,s/2,Math.min(t,s)*.1,t/2,s/2,Math.max(t,s)*.7);if(a.addColorStop(0,this.theme.gradient[0]),a.addColorStop(1,this.theme.gradient[1]),e.save(),e.globalAlpha=this.overlay.alpha,e.fillStyle=a,e.fillRect(0,0,t,s),this.vignette.strength>.02){const n=e.createRadialGradient(t/2,s/2,Math.min(t,s)*.45,t/2,s/2,Math.max(t,s)*.8);n.addColorStop(0,"rgba(0,0,0,0)"),n.addColorStop(1,`rgba(0,0,0,${.6*this.vignette.strength})`),e.fillStyle=n,e.fillRect(0,0,t,s)}this.tint.strength>.02&&(e.fillStyle=this.theme.tint,e.globalAlpha=this.tint.strength*.7,e.fillRect(0,0,t,s));const i=Math.floor(10*this.lod);e.globalAlpha=.08*this.overlay.alpha,e.fillStyle="#ffffff";for(let n=0;n<i;n++){const r=(n*37.3+this.noise.time*120)%s;e.fillRect(0,r,t,1)}e.restore(),e.globalAlpha=1}ensureAudio(){if(!this.audioCtx)try{this.audioCtx=new(window.AudioContext||window.webkitAudioContext)}catch{this.audioCtx=null}}playSound(e){if(this.ensureAudio(),!this.audioCtx)return;const t=this.audioCtx,s=t.createOscillator(),a=t.createGain();s.type=e==="error"?"square":e==="complete"?"triangle":"sine";const i=this.theme.sound.base;s.frequency.value=e==="init"?i*1.5:e==="complete"?i*.75:i*.5,a.gain.value=1e-4,s.connect(a).connect(t.destination),s.start();const n=t.currentTime,r=e==="init"?.2:e==="complete"?.15:.12;a.gain.exponentialRampToValueAtTime(r,n+.03),a.gain.exponentialRampToValueAtTime(1e-4,n+.25),s.stop(n+.3)}startAmbient(){if(this.ensureAudio(),!this.audioCtx||this.ambientNode)return;const e=this.audioCtx,t=e.createOscillator(),s=e.createGain();t.type="sawtooth",t.frequency.value=this.theme.sound.base*.33,s.gain.value=1e-4,t.connect(s).connect(e.destination),t.start();const a=e.currentTime;s.gain.exponentialRampToValueAtTime(.03,a+.2),this.ambientNode={o:t,g:s}}stopAmbient(){if(!this.ambientNode||!this.audioCtx)return;const{o:e,g:t}=this.ambientNode,s=this.audioCtx.currentTime;t.gain.exponentialRampToValueAtTime(1e-4,s+.2),e.stop(s+.25),this.ambientNode=null}}class ct{constructor(e,t={}){this.canvas=e,this.ctx=e.getContext("2d"),this.enabled=t.enabled!==!1,this.intensity=t.intensity||.8,this.threshold=t.threshold||.6,this.blurRadius=t.blurRadius||15,this.downsample=t.downsample||2,this.brightCanvas=null,this.brightCtx=null,this.blurCanvas=null,this.blurCtx=null,this.initializeCanvases(),this.bloomElements=new Set,this.lastFrameTime=0,this.skipFrames=0}initializeCanvases(){const e=Math.floor(this.canvas.width/this.downsample),t=Math.floor(this.canvas.height/this.downsample);this.brightCanvas=document.createElement("canvas"),this.brightCanvas.width=e,this.brightCanvas.height=t,this.brightCtx=this.brightCanvas.getContext("2d"),this.blurCanvas=document.createElement("canvas"),this.blurCanvas.width=e,this.blurCanvas.height=t,this.blurCtx=this.blurCanvas.getContext("2d")}resize(){(this.canvas.width!==this.lastWidth||this.canvas.height!==this.lastHeight)&&(this.initializeCanvases(),this.lastWidth=this.canvas.width,this.lastHeight=this.canvas.height)}addBloomElement(e){this.bloomElements.add(e)}removeBloomElement(e){this.bloomElements.delete(e)}applyBloom(){if(!this.enabled)return;const e=performance.now();if(this.skipFrames>0){this.skipFrames--;return}this.resize(),this.extractBrightAreas(),this.applyBlur(),this.compositeBloom(),performance.now()-e>8&&(this.skipFrames=1)}extractBrightAreas(){this.brightCtx.clearRect(0,0,this.brightCanvas.width,this.brightCanvas.height),this.brightCtx.save(),this.brightCtx.scale(1/this.downsample,1/this.downsample),this.brightCtx.drawImage(this.canvas,0,0),this.brightCtx.restore(),this.applyBrightnessThreshold()}applyBrightnessThreshold(){const e=this.brightCtx.getImageData(0,0,this.brightCanvas.width,this.brightCanvas.height),t=e.data;for(let s=0;s<t.length;s+=4){const a=t[s]/255,i=t[s+1]/255,n=t[s+2]/255;t[s+3]/255;const r=.299*a+.587*i+.114*n;if(r<this.threshold)t[s]=0,t[s+1]=0,t[s+2]=0,t[s+3]=0;else{const o=Math.pow((r-this.threshold)/(1-this.threshold),.8);t[s]=Math.min(255,t[s]*o*1.2),t[s+1]=Math.min(255,t[s+1]*o*1.2),t[s+2]=Math.min(255,t[s+2]*o*1.2)}}this.brightCtx.putImageData(e,0,0)}applyBlur(){this.blurCtx.clearRect(0,0,this.blurCanvas.width,this.blurCanvas.height),this.blurCtx.filter=`blur(${this.blurRadius}px)`,this.blurCtx.drawImage(this.brightCanvas,0,0),this.blurCtx.filter="none"}compositeBloom(){this.ctx.save(),this.ctx.globalCompositeOperation="screen",this.ctx.globalAlpha=this.intensity,this.ctx.drawImage(this.blurCanvas,0,0,this.blurCanvas.width,this.blurCanvas.height,0,0,this.canvas.width,this.canvas.height),this.ctx.restore()}toggle(){return this.enabled=!this.enabled,this.enabled}setIntensity(e){this.intensity=Math.max(0,Math.min(2,e))}setThreshold(e){this.threshold=Math.max(0,Math.min(1,e))}getSettings(){return{enabled:this.enabled,intensity:this.intensity,threshold:this.threshold,blurRadius:this.blurRadius}}destroy(){this.brightCanvas=null,this.brightCtx=null,this.blurCanvas=null,this.blurCtx=null,this.bloomElements.clear()}}class ht{constructor(){this.audioContext=null,this.masterVolume=1,this.musicVolume=.7,this.sfxVolume=.8,this.ambientVolume=.3,this.audioBuffers=new Map,this.activeSounds=new Map,this.musicTracks=new Map,this.soundPool=new Map,this.enabled=!0,this.musicEnabled=!0,this.sfxEnabled=!0,this.ambientEnabled=!0,this.activeSoundCount=0,this.maxConcurrentSounds=32,this.audioElements=new Map,this.backgroundMusicId=null,this.init()}async init(){try{this.audioContext=new(window.AudioContext||window.webkitAudioContext),this.masterGain=this.audioContext.createGain(),this.musicGain=this.audioContext.createGain(),this.sfxGain=this.audioContext.createGain(),this.ambientGain=this.audioContext.createGain(),this.musicGain.connect(this.masterGain),this.sfxGain.connect(this.masterGain),this.ambientGain.connect(this.masterGain),this.masterGain.connect(this.audioContext.destination),this.updateVolumeSettings(),console.log("AudioManager initialized successfully"),console.log("Audio context state:",this.audioContext.state),this.audioContext.onstatechange=()=>{console.log("Audio context state changed to:",this.audioContext.state)}}catch(e){console.warn("Web Audio API not supported, falling back to HTML5 Audio:",e),this.audioContext=null}}async loadAudioFile(e,t){try{if(this.audioContext){const a=await(await fetch(e)).arrayBuffer(),i=await this.audioContext.decodeAudioData(a);return this.audioBuffers.set(t,i),!0}else{const s=new Audio;return s.preload="auto",s.src=e,this.audioElements.set(t,s),!0}}catch(s){return console.error(`Failed to load audio file ${t}:`,s),!1}}async loadAudioFiles(e){const t=e.map(i=>this.loadAudioFile(i.url,i.name)),a=(await Promise.allSettled(t)).filter(i=>i.status==="fulfilled"&&i.value).length;console.log(`Loaded ${a}/${e.length} audio files`)}playSound(e,t={}){if(!this.enabled||!this.sfxEnabled||this.activeSoundCount>=this.maxConcurrentSounds)return null;const s=t.volume??1,a=t.pitch??1,i=t.loop??!1;try{if(this.audioContext){const n=this.audioBuffers.get(e);if(!n)return console.warn(`Audio buffer not found: ${e}`),null;const r=this.audioContext.createBufferSource(),o=this.audioContext.createGain();r.buffer=n,r.playbackRate.value=a,o.gain.value=s,r.connect(o),o.connect(this.sfxGain);const c=`sfx_${Date.now()}_${Math.random()}`;return this.activeSounds.set(c,{source:r,gainNode:o}),this.activeSoundCount++,r.onended=()=>{this.activeSounds.delete(c),this.activeSoundCount--},r.start(),c}else{const n=this.audioElements.get(e);if(!n)return console.warn(`Audio element not found: ${e}`),null;const r=n.cloneNode();r.volume=s*this.sfxVolume*this.masterVolume,r.playbackRate=a,r.loop=i;const o=`sfx_${Date.now()}_${Math.random()}`;return this.activeSounds.set(o,r),this.activeSoundCount++,r.addEventListener("ended",()=>{this.activeSounds.delete(o),this.activeSoundCount--}),r.play().catch(c=>{console.warn("Could not play sound:",c),this.activeSounds.delete(o),this.activeSoundCount--}),o}}catch(n){return console.error(`Error playing sound ${e}:`,n),null}}playMusic(e,t=!0){if(!this.enabled||!this.musicEnabled)return null;this.stopAllMusic();try{if(this.audioContext){const s=this.audioBuffers.get(e);if(!s)return console.warn(`Music buffer not found: ${e}`),null;const a=this.audioContext.createBufferSource(),i=this.audioContext.createGain();a.buffer=s,a.loop=t,i.gain.value=1,a.connect(i),i.connect(this.musicGain);const n=`music_${Date.now()}_${Math.random()}`;return this.musicTracks.set(n,{source:a,gainNode:i}),a.start(),n}else{const s=this.audioElements.get(e);if(!s)return console.warn(`Music element not found: ${e}`),null;const a=s.cloneNode();a.volume=this.musicVolume*this.masterVolume,a.loop=t;const i=`music_${Date.now()}_${Math.random()}`;return this.musicTracks.set(i,a),a.play().catch(n=>{console.warn("Could not play music:",n),this.musicTracks.delete(i)}),i}}catch(s){return console.error(`Error playing music ${e}:`,s),null}}playBackgroundMusic(e,t=!0){if(!this.enabled||!this.musicEnabled)return null;this.backgroundMusicId&&(this.stopMusic(this.backgroundMusicId),this.backgroundMusicId=null);try{if(this.audioContext){const s=this.audioBuffers.get(e);if(!s)return console.warn(`Music buffer not found: ${e}`),null;const a=this.audioContext.createBufferSource(),i=this.audioContext.createGain();a.buffer=s,a.loop=t,i.gain.value=1,a.connect(i),i.connect(this.musicGain);const n=`background_music_${Date.now()}_${Math.random()}`;return this.musicTracks.set(n,{source:a,gainNode:i}),this.backgroundMusicId=n,a.start(),console.log(`Background music started: ${e} with ID: ${n}`),n}else{const s=this.audioElements.get(e);if(!s)return console.warn(`Music element not found: ${e}`),null;const a=s.cloneNode();a.volume=this.musicVolume*this.masterVolume,a.loop=t;const i=`background_music_${Date.now()}_${Math.random()}`;return this.musicTracks.set(i,a),this.backgroundMusicId=i,a.play().catch(n=>{console.warn("Could not play background music:",n),this.musicTracks.delete(i),this.backgroundMusicId=null}),console.log(`Background music started: ${e} with ID: ${i}`),i}}catch(s){return console.error(`Error playing background music ${e}:`,s),this.backgroundMusicId=null,null}}stopBackgroundMusic(){this.backgroundMusicId&&(this.stopMusic(this.backgroundMusicId),this.backgroundMusicId=null)}stopAllMusic(){const e=this.backgroundMusicId?this.musicTracks.get(this.backgroundMusicId):null;for(const[t,s]of this.musicTracks)if(t!==this.backgroundMusicId)if(this.audioContext){const{source:a}=s;try{a.stop()}catch{}}else s.pause(),s.currentTime=0;this.backgroundMusicId&&e?(this.musicTracks.clear(),this.musicTracks.set(this.backgroundMusicId,e)):this.musicTracks.clear()}stopMusic(e){const t=this.musicTracks.get(e);if(t){if(this.audioContext){const{source:s}=t;try{s.stop()}catch{}}else t.pause(),t.currentTime=0;this.musicTracks.delete(e)}}setVolume(e,t){switch(t=Math.max(0,Math.min(1,t)),e){case"master":this.masterVolume=t;break;case"music":this.musicVolume=t;break;case"sfx":this.sfxVolume=t;break;case"ambient":this.ambientVolume=t;break;default:console.warn(`Unknown volume type: ${e}`);return}this.updateVolumeSettings()}updateVolumeSettings(){this.audioContext&&this.masterGain&&this.musicGain&&this.sfxGain&&this.ambientGain&&(this.masterGain.gain.value=this.masterVolume,this.musicGain.gain.value=this.musicVolume,this.sfxGain.gain.value=this.sfxVolume,this.ambientGain.gain.value=this.ambientVolume)}mute(e,t=!0){switch(e){case"music":this.musicEnabled=!t,t&&this.stopAllMusic();break;case"sfx":this.sfxEnabled=!t;break;case"ambient":this.ambientEnabled=!t;break;case"all":this.enabled=!t,t&&this.stopAllMusic();break;default:console.warn(`Unknown mute type: ${e}`)}}getVolumeSettings(){return{master:this.masterVolume,music:this.musicVolume,sfx:this.sfxVolume,ambient:this.ambientVolume,musicEnabled:this.musicEnabled,sfxEnabled:this.sfxEnabled,ambientEnabled:this.ambientEnabled,enabled:this.enabled}}cleanup(){for(const[e,t]of this.musicTracks)if(this.audioContext){const{source:s}=t;try{s.stop()}catch{}}else t.pause(),t.currentTime=0;this.musicTracks.clear(),this.backgroundMusicId=null;for(const[e,t]of this.activeSounds)if(this.audioContext){const{source:s}=t;try{s.stop()}catch{}}else t.pause(),t.currentTime=0;this.activeSounds.clear(),this.activeSoundCount=0,this.audioContext&&(this.audioContext.close(),this.audioContext=null)}async handleUserInteraction(){if(!this.audioContext){console.warn("Audio context not available");return}try{this.audioContext.state==="suspended"?(console.log("Resuming suspended audio context..."),await this.audioContext.resume(),console.log("Audio context resumed successfully")):this.audioContext.state==="closed"&&(console.warn("Audio context is closed, attempting to recreate..."),this.audioContext=new(window.AudioContext||window.webkitAudioContext),await this.init())}catch(e){console.error("Error handling user interaction for audio:",e)}}}const b={MUSIC:{SPACE_THEME:"/assets/audio/music/themes/water_theme.mp3",WATER_THEME:"/assets/audio/music/themes/water_theme.mp3",FIRE_INTENSITY:"/assets/audio/music/themes/water_theme.mp3",AIR_AMBIENT:"/assets/audio/music/themes/water_theme.mp3",EARTH_RHYTHM:"/assets/audio/music/themes/water_theme.mp3",CRYSTAL_HARMONY:"/assets/audio/music/themes/water_theme.mp3",SHADOW_MYSTERY:"/assets/audio/music/themes/water_theme.mp3",BOSS_THEME:"/assets/audio/music/boss/water_theme.mp3",MENU_THEME:"/assets/audio/music/menu/water_theme.mp3"},SFX:{PLAYER_SHOT:"/assets/audio/sfx/weapons/player_shot.wav",AIR_SHOT:"/assets/audio/sfx/weapons/shadow_shot.wav",WATER_SHOT:"/assets/audio/sfx/weapons/shadow_shot.wav",FIRE_SHOT:"/assets/audio/sfx/weapons/shadow_shot.wav",EARTH_SHOT:"/assets/audio/sfx/weapons/shadow_shot.wav",CRYSTAL_SHOT:"/assets/audio/sfx/weapons/shadow_shot.wav",SHADOW_SHOT:"/assets/audio/sfx/weapons/shadow_shot.wav",ENEMY_EXPLOSION:"/assets/audio/sfx/explosions/enemy_explosion.wav",PLAYER_EXPLOSION:"/assets/audio/sfx/explosions/boss_explosion.wav",BOSS_EXPLOSION:"/assets/audio/sfx/explosions/boss_explosion.wav",BUTTON_CLICK:"/assets/audio/sfx/ui/menu_open.wav",MENU_OPEN:"/assets/audio/sfx/ui/menu_open.wav",MENU_CLOSE:"/assets/audio/sfx/ui/menu_open.wav",ACHIEVEMENT:"/assets/audio/sfx/ui/achievement.wav",COLLECT_POWERUP:"/assets/audio/sfx/ui/collect_powerup.wav",WEAPON_UPGRADE:"/assets/audio/sfx/powerups/weapon_upgrade.wav",EXTRA_LIFE:"/assets/audio/sfx/powerups/weapon_upgrade.wav",FORMATION_SPAWN:"/assets/audio/sfx/formations/formation_spawn.wav",FORMATION_TURN:"/assets/audio/sfx/formations/formation_turn.wav",DIVE_ATTACK:"/assets/audio/sfx/formations/formation_spawn.wav",SPACE_AMBIENT:"/assets/audio/sfx/ambient/space_ambient.wav",WARP_AMBIENT:"/assets/audio/sfx/ambient/warp_ambient.wav"}},dt=[{url:b.MUSIC.SPACE_THEME,name:"space_theme"},{url:b.MUSIC.WATER_THEME,name:"water_theme"},{url:b.MUSIC.FIRE_INTENSITY,name:"fire_intensity"},{url:b.MUSIC.AIR_AMBIENT,name:"air_ambient"},{url:b.MUSIC.EARTH_RHYTHM,name:"earth_rhythm"},{url:b.MUSIC.CRYSTAL_HARMONY,name:"crystal_harmony"},{url:b.MUSIC.SHADOW_MYSTERY,name:"shadow_mystery"},{url:b.MUSIC.BOSS_THEME,name:"boss_theme"},{url:b.MUSIC.MENU_THEME,name:"menu_theme"},{url:b.SFX.PLAYER_SHOT,name:"player_shot"},{url:b.SFX.AIR_SHOT,name:"air_shot"},{url:b.SFX.WATER_SHOT,name:"water_shot"},{url:b.SFX.FIRE_SHOT,name:"fire_shot"},{url:b.SFX.EARTH_SHOT,name:"earth_shot"},{url:b.SFX.CRYSTAL_SHOT,name:"crystal_shot"},{url:b.SFX.SHADOW_SHOT,name:"shadow_shot"},{url:b.SFX.ENEMY_EXPLOSION,name:"enemy_explosion"},{url:b.SFX.PLAYER_EXPLOSION,name:"player_explosion"},{url:b.SFX.BOSS_EXPLOSION,name:"boss_explosion"},{url:b.SFX.BUTTON_CLICK,name:"button_click"},{url:b.SFX.MENU_OPEN,name:"menu_open"},{url:b.SFX.MENU_CLOSE,name:"menu_close"},{url:b.SFX.ACHIEVEMENT,name:"achievement"},{url:b.SFX.COLLECT_POWERUP,name:"collect_powerup"},{url:b.SFX.WEAPON_UPGRADE,name:"weapon_upgrade"},{url:b.SFX.EXTRA_LIFE,name:"extra_life"},{url:b.SFX.FORMATION_SPAWN,name:"formation_spawn"},{url:b.SFX.FORMATION_TURN,name:"formation_turn"},{url:b.SFX.DIVE_ATTACK,name:"dive_attack"},{url:b.SFX.SPACE_AMBIENT,name:"space_ambient"},{url:b.SFX.WARP_AMBIENT,name:"warp_ambient"}];class ut{constructor(e,t){this.canvas=e,this.ctx=e.getContext("2d"),this.uiElement=t,this.stars=[],this.backgroundParallaxOffsetX=0,this.backgroundParallaxOffsetY=0,this.backgroundParallaxFactor=.2,this.isRunning=!1,this.isPaused=!1,this.gameState="MENU",this.levelCompletionData=null,this.gameOverData=null,this.mainMenu=null,this.targetFPS=60,this.fixedTimeStep=1e3/this.targetFPS,this.maxFrameTime=250,this.lastFrameTime=0,this.accumulator=0,this.currentTime=0,this.frameCount=0,this.fpsTimer=0,this.currentFPS=0,this.enemySprites=new Map,this.playerSprite=null,this.wingmanSprite=null,this.bossWarpManager=null,this.llmClient=null,this.warpVFX=null,this._warpVFXRegistered=!1,this.bloomEffect=null,this.gameLoop=this.gameLoop.bind(this),this.handleResize=this.handleResize.bind(this)}setMainMenu(e){this.mainMenu=e}async init(){return this.setupCanvas(),await this.initializeSystems(),this.start(),Promise.resolve()}setupCanvas(){this.ctx.imageSmoothingEnabled=!1,this.handleResize(),window.addEventListener("resize",this.handleResize)}initializeStarfield(){for(let t=0;t<200;t++)this.stars.push({x:Math.random()*this.canvas.width,y:Math.random()*this.canvas.height,size:Math.random()*1.5+.5,speed:Math.random()*50+10,parallaxFactor:Math.random()*.5+.5})}async initializeSystems(){var s,a;this.inputManager=new Ee(this.canvas,this),this.audioManager=new ht,this.setupAudioContextActivation(),this.loadAudioFiles(),this.gameObjectManager=new Ge;const e=this.canvas.width/2,t=this.canvas.height-100;this.playerShip=new Ce(e,t,this.canvas.width,this.canvas.height,this.gameObjectManager,this.audioManager),this.levelManager=new Ve(this.gameObjectManager,this.audioManager),this.enemyManager=new qe(this.canvas.width,this.canvas.height,this.gameObjectManager,this.audioManager),await this.loadEnemySprites(),await this.loadPlayerAndWingmanSprites(),this.tokenManager=new Je,this.ethTestModeManager=new Qe(this.tokenManager),this.environmentTracker=new et,this.rewardManager=new tt(this.tokenManager),this.realityWarpManager=new Ze(this.tokenManager,null,this.levelManager),this.realityWarpManager.gameEngine=this,this.realityWarpManager.init(),this.bossWarpManager=new Xe(this.realityWarpManager,this.enemyManager),this.bossWarpManager.init(),this.enemyManager.setBossWarpManager(this.bossWarpManager),this.orangeSDKManager=new nt,this.hangarManager=new rt(this.tokenManager,this.orangeSDKManager),this.playerShip.setHangarManager(this.hangarManager),this.hangarManager.addUpgradeListener((i,n)=>{console.log(`[GameEngine] Hangar upgrade applied: ${i} to level ${n}`),console.log("Listener triggered for upgradeId:",i,"level:",n),this.playerShip&&this.playerShip.applyHangarUpgrades()}),this.genieInterface=new st(this.tokenManager,this),this.consumableManager=new ot(this.tokenManager,this),this.powerUpIndicator=new at,this.powerUpIndicator.initialize(),this.boostGauge=new it,this.warpVFX=new lt(this.canvas,this),this.warpVFX&&((a=(s=window.AuthManager)==null?void 0:s.config)!=null&&a.debugMode)&&this.warpVFX.initDevControls(),this.bloomEffect=new ct(this.canvas,{enabled:!0,intensity:.6,threshold:.5,blurRadius:12,downsample:2}),this.initBloomDebugControls(),this.setupLevelManagerCallbacks(),this.setupEnemyManagerCallbacks(),this.setupBossWarpManagerCallbacks(),this.setupOrangeSDKCallbacks(),this.setupTokenManagerCallbacks(),await this.genieInterface.initialize(),this.setupGenieInterfaceCallbacks(),this.syncWeaponPowerUps(),this.enemyManager.setEnvironment({getEnvironmentType:()=>"space",getCurrentGameplayModifiers:()=>({}),getCurrentEnvironmentType:()=>"space",getCurrentEnvironmentName:()=>"Default Environment",getCurrentEnvironmentDescription:()=>"Default space environment",isEnemyCompatible:()=>!0,applyEnvironmentEffects:i=>i,update:()=>{},render:()=>{},resetToDefault:()=>{}}),this.initializeOrangeSDK(),this.initializeStarfield()}async loadEnemySprites(){const e={[l.WATER]:"/assets/sprites/enemies/water.png",[l.FIRE]:"/assets/sprites/enemies/fire.png",[l.AIR]:"/assets/sprites/enemies/air.png",[l.EARTH]:"/assets/sprites/enemies/earth.png",[l.CRYSTAL]:"/assets/sprites/enemies/crystal.png",[l.SHADOW]:"/assets/sprites/enemies/shadow.png"},t=Object.entries(e).map(([s,a])=>new Promise(i=>{const n=new Image;n.crossOrigin="anonymous",n.onload=()=>{this.enemySprites.set(s,n),console.log(`✅ Loaded sprite for ${s} enemy`),i()},n.onerror=()=>{console.warn(`⚠️ Could not load sprite for ${s} enemy at ${a}, will use fallback rendering`),i()},n.src=a}));await Promise.all(t),console.log("✅ All enemy sprites loaded")}async loadPlayerAndWingmanSprites(){this.playerSprite=await this.loadSprite("/assets/sprites/enemies/player.png","player"),this.wingmanSprite=await this.loadSprite("/assets/sprites/enemies/fire.png","wingman"),console.log("✅ Player and wingman sprites loaded")}loadSprite(e,t){return new Promise((s,a)=>{const i=new Image;i.crossOrigin="anonymous",i.onload=()=>{console.log(`✅ Loaded ${t} sprite: ${e}`),s(i)},i.onerror=()=>{console.warn(`⚠️ Could not load ${t} sprite at ${e}`),s(null)},i.src=e})}start(){this.isRunning||(this.isRunning=!0,this.isPaused=!1,this.currentTime=performance.now(),this.lastFrameTime=this.currentTime,this.accumulator=0,this.frameCount=0,this.fpsTimer=0,requestAnimationFrame(this.gameLoop))}startGameplay(e=null){const t=document.getElementById("game-container");t&&(t.style.display="block"),this.gameState="GAME_PLAY",this.playerShip&&(this.playerShip.resetHealthAndLives(),console.log("Player ship reset for new game")),this.audioManager&&this.audioManager.playBackgroundMusic("space_theme",!0),this.levelManager&&this.levelManager.startLevel(1),this.powerUpIndicator&&this.powerUpIndicator.show(),this.boostGauge&&this.boostGauge.show()}startGame(e=null){this.levelManager&&this.levelManager.startLevel(1),this.powerUpIndicator&&this.powerUpIndicator.show()}pause(){this.gameState==="GAME_PLAY"?(this.gameState="PAUSED",this.isPaused=!0,this.orangeSDKManager&&this.orangeSDKManager.handleGamePause()):this.gameState==="PAUSED"&&this.resume()}resume(){this.gameState==="PAUSED"&&(this.gameState="GAME_PLAY",this.isPaused=!1,this.currentTime=performance.now(),this.lastFrameTime=this.currentTime,this.accumulator=0,this.orangeSDKManager&&this.orangeSDKManager.handleGameResume())}pauseGame(){this.pause()}resumeGame(){this.resume()}async destroy(){this.isRunning=!1,this.orangeSDKManager&&await this.orangeSDKManager.handleGameQuit(),this.inputManager&&this.inputManager.destroy(),this.realityWarpManager&&this.realityWarpManager.getWarpState().status==="active"&&this.realityWarpManager.endWarp(),this.bloomEffect&&this.bloomEffect.destroy()}gameLoop(e){if(!this.isRunning)return;let t=e-this.lastFrameTime;if(t>this.maxFrameTime&&(t=this.maxFrameTime),this.lastFrameTime=e,!this.isPaused&&(this.gameState==="GAME_PLAY"||this.gameState==="LEVEL_COMPLETE"||this.gameState==="GAME_OVER")){for(this.accumulator+=t;this.accumulator>=this.fixedTimeStep;)this.gameState,this.update(this.fixedTimeStep),this.accumulator-=this.fixedTimeStep;const s=this.accumulator/this.fixedTimeStep;this.render(s),this.updateFPSCounter(t)}else this.gameState==="PAUSED"?(this.update(0),this.render(0)):this.render(0);requestAnimationFrame(this.gameLoop)}update(e){if(this.gameState==="PAUSED"){this.inputManager&&this.inputManager.isActionPressed("pause")&&this.pauseGame(),this.inputManager&&this.inputManager.update();return}if(this.updateStarfield(e),this.gameState==="LEVEL_COMPLETE"&&(console.log("DEBUG: Level complete state detected, checking for input..."),this.inputManager)){const t=this.inputManager.isKeyPressed("Space"),s=this.inputManager.isKeyPressed("Enter"),a=this.inputManager.isKeyPressed("KeyE");(t||s||a)&&this.proceedFromLevelComplete()}if(this.gameState==="GAME_OVER"&&this.inputManager){const t=this.inputManager.isKeyPressed("Space"),s=this.inputManager.isKeyPressed("Enter");if(t||s){console.log("Space or Enter pressed in game over - returning to main menu"),this.returnToMainMenuFromGameOver();return}}if(this.gameState==="GAME_PLAY"){if(this.playerShip&&this.inputManager){const t=this.inputManager.getMovementVector();if(this.playerShip.update(e,t),this.playerShip.activePowerUps)for(const[s,a]of this.playerShip.activePowerUps){const i=a.isActive;a.update(e,this.playerShip),i&&!a.isActive&&(this.playerShip.activePowerUps.delete(s),console.log(`Power-up ${s} expired and removed from player`),this.genieInterface&&this.genieInterface.activePowerUps.has(s)&&this.genieInterface.activePowerUps.delete(s))}(this.inputManager.isActionDown("fire")||this.inputManager.virtualJoystick&&this.inputManager.virtualJoystick.isFiring())&&this.playerShip.fire(),this.inputManager.isActionPressed("interact")&&(console.log("🔥 E key pressed - attempting consumable activation"),console.log("🎮 Game state:",this.gameState),console.log("🎮 Consumable manager exists:",!!this.consumableManager),this.activateConsumable()),this.inputManager.isTouchDevice&&this.consumableButtonBounds&&this.handleConsumableTouchInput(),this.inputManager.isKeyPressed("KeyE")&&console.log("🔑 Raw KeyE detected!"),this.inputManager.isKeyPressed("KeyD")&&this.playerShip.takeDamage(25),this.inputManager.isKeyPressed("KeyH")&&this.playerShip.heal(25),this.inputManager.isKeyPressed("KeyL")&&this.playerShip.addLives(1),this.inputManager.isKeyPressed("KeyT")&&this.tokenManager.awardTokens(5e4,"debug_tokens"),this.inputManager.isKeyPressed("KeyG")&&this.showGenieInterface({levelNumber:1,nextLevel:2}),this.inputManager.isKeyPressed("KeyP")&&this.testPowerUpSystem(),this.handleBoostActivation(),this.inputManager.isActionPressed("pause")&&this.pauseGame()}if(this.genieInterface&&(this.genieInterface.updateActivePowerUps(e),this.powerUpIndicator&&(this.powerUpIndicator.updateActivePowerUps(this.genieInterface.activePowerUps),this.powerUpIndicator.update(e)),this.boostGauge&&this.boostGauge.update(e)),this.levelManager){const t={playerDestroyed:this.playerShip?this.playerShip.getHealthStatus().isDestroyed:!1,playerDamageTaken:!1,shotsFired:0,shotsHit:0};this.levelManager.update(e,t)}if(this.enemyManager&&this.playerShip&&this.levelManager&&this.levelManager.levelInProgress){const t=this.playerShip.position;this.enemyManager.update(e,t),this.handleCollisions()}this.gameObjectManager&&this.gameObjectManager.update(e),this.cleanupProjectiles(),this.orangeSDKManager&&this.orangeSDKManager.updateAutoSave(),this.bossWarpManager&&this.bossWarpManager.update(e)}this.warpVFX&&this.warpVFX.update(e),this.inputManager&&this.inputManager.update()}cleanupProjectiles(){if(!this.gameObjectManager)return;const e={left:-50,right:this.canvas.width+50,top:-50,bottom:this.canvas.height+50},t=this.gameObjectManager.findByTag("projectile");for(const s of t)s.isOutOfBounds(e)&&this.gameObjectManager.returnToPool("projectile",s)}updateStarfield(e){const t=e/1e3;let s=0,a=0;if(this.playerShip&&this.inputManager){const r=this.inputManager.getMovementVector();s=r.x,a=r.y}for(const r of this.stars)r.y+=r.speed*t,r.x-=s*r.parallaxFactor*2,r.y-=a*r.parallaxFactor*2,r.y>this.canvas.height?(r.y=0,r.x=Math.random()*this.canvas.width):r.y<0&&(r.y=this.canvas.height,r.x=Math.random()*this.canvas.width),r.x>this.canvas.width?(r.x=0,r.y=Math.random()*this.canvas.height):r.x<0&&(r.x=this.canvas.width,r.y=Math.random()*this.canvas.height);this.backgroundParallaxOffsetX-=s*this.backgroundParallaxFactor*2,this.backgroundParallaxOffsetY-=a*this.backgroundParallaxFactor*2;const i=this.canvas.width*.05,n=this.canvas.height*.05;this.backgroundParallaxOffsetX=Math.max(-i,Math.min(i,this.backgroundParallaxOffsetX)),this.backgroundParallaxOffsetY=Math.max(-n,Math.min(n,this.backgroundParallaxOffsetY))}render(e=0){this.ctx.fillStyle="#000011",this.ctx.fillRect(0,0,this.canvas.width,this.canvas.height);let t=!1;if(this.levelManager&&this.levelManager.levelConfig&&this.levelManager.levelConfig.environmentData){const s=this.levelManager.levelConfig.environmentData;if(s.imageData&&s.imageData.images&&s.imageData.images[0]){const a=s.imageData.images[0],i=a.localUrl||a.url;if((!this.customBackgroundImage||this.customBackgroundImage.currentUrl!==i)&&(this.customBackgroundImage=new Image,this.customBackgroundImage.crossOrigin="anonymous",this.customBackgroundImage.currentUrl=i,this.customBackgroundImage.onload=()=>{},this.customBackgroundImage.onerror=n=>{this.customBackgroundImage=null},this.customBackgroundImage.src=i),this.customBackgroundImage,this.customBackgroundImage&&this.customBackgroundImage.complete){this.ctx.save();const n=this.customBackgroundImage.naturalWidth/this.customBackgroundImage.naturalHeight,r=this.canvas.width/this.canvas.height;let o,c,h,d;n>r?(c=this.canvas.height*1.1,o=c*n):(o=this.canvas.width*1.1,c=o/n),h=(this.canvas.width-o)/2,d=(this.canvas.height-c)/2,h+=this.backgroundParallaxOffsetX,d+=this.backgroundParallaxOffsetY;const g=(o-this.canvas.width)*.5,w=(c-this.canvas.height)*.5;this.backgroundParallaxOffsetX=Math.max(-g,Math.min(g,this.backgroundParallaxOffsetX)),this.backgroundParallaxOffsetY=Math.max(-w,Math.min(w,this.backgroundParallaxOffsetY)),this.ctx.drawImage(this.customBackgroundImage,h,d,o,c),this.ctx.restore(),t=!0}else this.customBackgroundImage&&!this.customBackgroundImage.complete&&(t=!0)}}if(!t&&this.realityWarpManager&&this.realityWarpManager.currentEnvironment&&this.realityWarpManager.getWarpState().status==="active"){const s=this.realityWarpManager.currentEnvironment;if(s.imageData&&s.imageData.images&&s.imageData.images[0]){const a=s.imageData.images[0],i=a.localUrl||a.url;if((!this.customBackgroundImage||this.customBackgroundImage.currentUrl!==i)&&(this.customBackgroundImage=new Image,this.customBackgroundImage.crossOrigin="anonymous",this.customBackgroundImage.currentUrl=i,this.customBackgroundImage.onload=()=>{},this.customBackgroundImage.onerror=n=>{this.customBackgroundImage=null},this.customBackgroundImage.src=i),this.customBackgroundImage,this.customBackgroundImage&&this.customBackgroundImage.complete){this.ctx.save();const n=this.customBackgroundImage.naturalWidth/this.customBackgroundImage.naturalHeight,r=this.canvas.width/this.canvas.height;let o,c,h,d;n>r?(c=this.canvas.height*1.1,o=c*n):(o=this.canvas.width*1.1,c=o/n),h=(this.canvas.width-o)/2,d=(this.canvas.height-c)/2,h+=this.backgroundParallaxOffsetX,d+=this.backgroundParallaxOffsetY;const g=(o-this.canvas.width)*.5,w=(c-this.canvas.height)*.5;this.backgroundParallaxOffsetX=Math.max(-g,Math.min(g,this.backgroundParallaxOffsetX)),this.backgroundParallaxOffsetY=Math.max(-w,Math.min(w,this.backgroundParallaxOffsetY)),this.ctx.drawImage(this.customBackgroundImage,h,d,o,c),this.ctx.restore(),t=!0}else this.customBackgroundImage&&!this.customBackgroundImage.complete&&(t=!0)}}if(t||(this.aiServiceManager?this.aiServiceManager.render(this.ctx,this.canvas.width,this.canvas.height):this.renderDefaultBackground()),this.renderStarField(),this.gameState==="GAME_PLAY")if(this.bloomEffect){const s=document.createElement("canvas");s.width=this.canvas.width,s.height=this.canvas.height;const a=s.getContext("2d");this.playerShip&&this.playerShip.render(a),this.enemyManager&&this.enemyManager.render(a,e),this.gameObjectManager&&this.gameObjectManager.render(a,e);const i=this.bloomEffect.canvas,n=this.bloomEffect.ctx;this.bloomEffect.canvas=s,this.bloomEffect.ctx=a,this.bloomEffect.applyBloom(),this.bloomEffect.canvas=i,this.bloomEffect.ctx=n,this.ctx.drawImage(s,0,0)}else this.playerShip&&this.playerShip.render(this.ctx),this.enemyManager&&this.enemyManager.render(this.ctx,e),this.gameObjectManager&&this.gameObjectManager.render(this.ctx,e);this.inputManager&&(this.renderInputDebug(),this.inputManager.render(this.ctx)),this.gameState==="GAME_PLAY"&&this.renderHealthAndLivesUI(),this.renderLevelAndScoreUI(),this.tokenManager&&this.gameState==="GAME_PLAY"&&this.tokenManager.render(this.ctx,this.fixedTimeStep),this.powerUpIndicator&&this.gameState==="GAME_PLAY"&&this.powerUpIndicator.render(this.ctx),this.gameState==="GAME_PLAY"&&this.renderConsumableHUD(),this.warpVFX&&this.warpVFX.render(this.ctx),this.gameState==="PAUSED"&&this.renderPauseOverlay()}async changeEnvironment(e){if(this.aiServiceManager)try{await this.aiServiceManager.createEnvironment(e),console.log(`Environment changed to: ${e}`)}catch(t){console.error("Failed to change environment:",t)}}getCurrentEnvironmentModifiers(){return this.aiServiceManager?this.aiServiceManager.getCurrentEnvironmentModifiers():{}}applyEnvironmentModifiers(e){return this.aiServiceManager?this.aiServiceManager.applyEnvironmentModifiers(e):e}renderInputDebug(){}updateFPSCounter(e){this.frameCount++,this.fpsTimer+=e,this.fpsTimer>=1e3&&(this.currentFPS=Math.round(this.frameCount*1e3/this.fpsTimer),this.frameCount=0,this.fpsTimer=0)}renderDefaultBackground(){if(this.defaultBackgroundImage||(this.defaultBackgroundImage=new Image,this.defaultBackgroundImage.crossOrigin="anonymous",this.defaultBackgroundImage.onload=()=>{},this.defaultBackgroundImage.onerror=()=>{console.error("Failed to load default background image"),this.defaultBackgroundImage=null,this.renderStarField()},this.defaultBackgroundImage.src="./assets/graphics/bg1.webp"),this.defaultBackgroundImage&&this.defaultBackgroundImage.complete){this.ctx.save();const e=this.defaultBackgroundImage.naturalWidth/this.defaultBackgroundImage.naturalHeight,t=this.canvas.width/this.canvas.height;let s,a,i,n;e>t?(a=this.canvas.height*1.1,s=a*e):(s=this.canvas.width*1.1,a=s/e),i=(this.canvas.width-s)/2,n=(this.canvas.height-a)/2,i+=this.backgroundParallaxOffsetX,n+=this.backgroundParallaxOffsetY;const r=(s-this.canvas.width)*.5,o=(a-this.canvas.height)*.5;this.backgroundParallaxOffsetX=Math.max(-r,Math.min(r,this.backgroundParallaxOffsetX)),this.backgroundParallaxOffsetY=Math.max(-o,Math.min(o,this.backgroundParallaxOffsetY)),this.ctx.drawImage(this.defaultBackgroundImage,i,n,s,a),this.ctx.restore(),this.renderStarField()}else this.renderStarField()}renderStarField(){for(const e of this.stars){const t=Math.min(.8,e.speed/30);this.ctx.fillStyle=`rgba(255, 255, 255, ${t})`,this.ctx.fillRect(e.x,e.y,e.size,e.size),e.size>1.2&&(this.ctx.fillStyle=`rgba(255, 255, 255, ${t*.3})`,this.ctx.fillRect(e.x-1,e.y-1,e.size+2,e.size+2))}}renderPauseOverlay(){const e=this.ctx.createRadialGradient(this.canvas.width/2,this.canvas.height/2,0,this.canvas.width/2,this.canvas.height/2,Math.max(this.canvas.width,this.canvas.height)/2);e.addColorStop(0,"rgba(0, 20, 40, 0.85)"),e.addColorStop(1,"rgba(0, 0, 20, 0.9)"),this.ctx.fillStyle=e,this.ctx.fillRect(0,0,this.canvas.width,this.canvas.height),this.ctx.fillStyle="#00ffff",this.ctx.font='bold 48px "Courier New", monospace',this.ctx.textAlign="center",this.ctx.shadowColor="#00ffff",this.ctx.shadowBlur=20,this.ctx.fillText("GAME PAUSED",this.canvas.width/2,this.canvas.height/2-50),this.ctx.strokeStyle="rgba(0, 255, 255, 0.3)",this.ctx.lineWidth=2,this.ctx.shadowBlur=10;const t=180,s=this.canvas.width/2,a=this.canvas.height/2-50;this.ctx.beginPath();for(let n=0;n<6;n++){const r=Math.PI/3*n,o=s+t*Math.cos(r),c=a+t*Math.sin(r);n===0?this.ctx.moveTo(o,c):this.ctx.lineTo(o,c)}this.ctx.closePath(),this.ctx.stroke(),this.ctx.shadowBlur=0,this.ctx.fillStyle="#00ffff",this.ctx.shadowColor="#00ffff",this.ctx.shadowBlur=8,this.ctx.font='bold 16px "Courier New", monospace';const i=.5+.5*Math.sin(Date.now()/300);this.ctx.globalAlpha=i,this.ctx.fillText("PRESS P TO RESUME...",this.canvas.width/2,this.canvas.height/2+50),this.ctx.globalAlpha=1,this.ctx.shadowBlur=0}renderHealthAndLivesUI(){if(!this.playerShip)return;const e=this.playerShip.getHealthStatus(),t=this.isMobileDevice(),s=this.canvas.width-(t?190:240),a=t?10:20,i=t?170:220,n=t?25:30;this.ctx.fillStyle="rgba(10, 10, 46, 0.8)",this.ctx.fillRect(s-10,a-10,i+20,n+80),this.ctx.strokeStyle="#00ffff",this.ctx.lineWidth=2,this.ctx.shadowColor="#00ffff",this.ctx.shadowBlur=15,this.ctx.strokeRect(s-10,a-10,i+20,n+80),this.ctx.shadowBlur=0;const r=this.ctx.createLinearGradient(s,a,s+i,a);r.addColorStop(0,"rgba(0, 20, 40, 0.9)"),r.addColorStop(1,"rgba(0, 10, 30, 0.9)"),this.ctx.fillStyle=r,this.ctx.fillRect(s,a,i,n),this.ctx.strokeStyle="rgba(0, 255, 255, 0.5)",this.ctx.lineWidth=1,this.ctx.strokeRect(s,a,i,n);const o=(i-4)*Math.max(0,Math.min(1,e.healthPercentage));let c=this.ctx.createLinearGradient(s+2,a,s+Math.max(1,o),a);if(e.healthPercentage<.3){const f=.7+.3*Math.sin(Date.now()/200);c.addColorStop(0,`rgba(255, 0, 0, ${f})`),c.addColorStop(.5,`rgba(255, 100, 0, ${f})`),c.addColorStop(1,`rgba(255, 0, 0, ${f})`)}else e.healthPercentage<.6?(c.addColorStop(0,"#ff6600"),c.addColorStop(.5,"#ff9900"),c.addColorStop(1,"#ff6600")):(c.addColorStop(0,"#00ff88"),c.addColorStop(.5,"#0b00a0ff"),c.addColorStop(1,"#00ff88"));this.ctx.fillStyle=c,o>0&&this.ctx.fillRect(s+2,a+2,o-4,n-4),e.healthPercentage>.1&&o>0&&(this.ctx.shadowColor=e.healthPercentage<.3?"#ff0000":e.healthPercentage<.6?"#ff9900":"#00ffff",this.ctx.shadowBlur=10,this.ctx.fillRect(s+2,a+2,o-4,n-4),this.ctx.shadowBlur=0);const h=Math.max(0,Math.min(e.maxHealth,Math.round(e.health))),d=Math.max(0,Math.round(e.maxHealth));if(this.ctx.fillStyle="#b90d07ff",this.ctx.font='bold 14px "Courier New", monospace',this.ctx.textAlign="center",this.ctx.shadowColor="#ce6006ff",this.ctx.shadowBlur=5,this.ctx.fillText(`HULL INTEGRITY: ${h}/${d}`,s+i/2,a+n/2+5),this.ctx.shadowBlur=0,this.boostGauge){const f=a+n+10;this.boostGauge.render(this.ctx,s,f,i,20)}const g=a+n+45;this.ctx.textAlign="right",this.ctx.fillStyle="#00ffff",this.ctx.font='bold 16px "Courier New", monospace',this.ctx.shadowColor="#00ffff",this.ctx.shadowBlur=5,this.ctx.fillText(`LIVES: ${e.lives}`,this.canvas.width-20,g),this.ctx.shadowBlur=0;const w=18,v=22,p=this.canvas.width-20-e.lives*v;for(let f=0;f<e.lives;f++){const y=p+f*v,M=g+12;this.ctx.save(),this.ctx.translate(y,M),this.ctx.beginPath();for(let I=0;I<6;I++){const B=I*Math.PI/3,_=Math.cos(B)*(w/2),J=Math.sin(B)*(w/2);I===0?this.ctx.moveTo(_,J):this.ctx.lineTo(_,J)}this.ctx.closePath();const S=this.ctx.createRadialGradient(0,0,0,0,0,w/2);S.addColorStop(0,"#00ffff"),S.addColorStop(1,"#0088ff"),this.ctx.fillStyle=S,this.ctx.fill(),this.ctx.strokeStyle="#00ffff",this.ctx.lineWidth=1,this.ctx.shadowColor="#00ffff",this.ctx.shadowBlur=8,this.ctx.stroke(),this.ctx.shadowBlur=0,this.ctx.restore()}if(e.isInvulnerable){this.ctx.fillStyle="#ffff00",this.ctx.font='bold 12px "Courier New", monospace',this.ctx.textAlign="right",this.ctx.shadowColor="#ffff00",this.ctx.shadowBlur=8;const f=(e.invulnerabilityTimeRemaining/1e3).toFixed(1);this.ctx.fillText(`QUANTUM SHIELD: ${f}s`,this.canvas.width-20,g+50),this.ctx.shadowBlur=0}if(e.isDestroyed){const f=this.ctx.createRadialGradient(this.canvas.width/2,this.canvas.height/2,0,this.canvas.width/2,this.canvas.height/2,Math.max(this.canvas.width,this.canvas.height)/2);f.addColorStop(0,"rgba(255, 0, 0, 0.2)"),f.addColorStop(1,"rgba(0, 0, 0, 0.9)"),this.ctx.fillStyle=f,this.ctx.fillRect(0,0,this.canvas.width,this.canvas.height);const y=1+.1*Math.sin(Date.now()/300);this.ctx.save(),this.ctx.translate(this.canvas.width/2,this.canvas.height/2-50),this.ctx.scale(y,y),this.ctx.fillStyle="#ff4444",this.ctx.font='bold 48px "Courier New", monospace',this.ctx.textAlign="center",this.ctx.shadowColor="#ff0000",this.ctx.shadowBlur=20,this.ctx.fillText("SYSTEM FAILURE",0,0),this.ctx.restore(),this.ctx.fillStyle="#ffffff",this.ctx.font='bold 24px "Courier New", monospace',this.ctx.textAlign="center",this.ctx.shadowColor="#ffffff",this.ctx.shadowBlur=10,this.ctx.fillText("HULL INTEGRITY: 0%",this.canvas.width/2,this.canvas.height/2+50),this.ctx.shadowBlur=0}}renderLevelAndScoreUI(){if(!this.levelManager)return;const e=this.levelManager.getLevelStatus(),t=this.isMobileDevice(),s=t?5:10,a=t?10:15,i=t?100:125,n=t?90:110,r=this.ctx.createLinearGradient(s,a,s+i,a+n);if(r.addColorStop(0,"rgba(10, 10, 46, 0.8)"),r.addColorStop(1,"rgba(0, 10, 30, 0.8)"),this.ctx.fillStyle=r,this.ctx.fillRect(s,a,i,n),this.ctx.strokeStyle="#00ffff",this.ctx.lineWidth=2,this.ctx.shadowColor="#00ffff",this.ctx.shadowBlur=15,this.ctx.strokeRect(s,a,i,n),this.ctx.shadowBlur=0,this.ctx.fillStyle="#00ffff",this.ctx.font=t?'bold 10px "Courier New", monospace':'bold 12px "Courier New", monospace',this.ctx.textAlign="left",this.ctx.shadowColor="#00ffff",this.ctx.shadowBlur=5,this.ctx.fillText(`SECTOR: ${e.currentLevel}`,s+(t?8:10),a+(t?16:20)),this.ctx.font=t?'bold 8px "Courier New", monospace':'bold 10px "Courier New", monospace',this.ctx.fillText(`SCORE: ${e.score.current.toLocaleString()}`,s+(t?8:10),a+(t?28:35)),this.ctx.shadowBlur=0,e.levelInProgress){let o=0,c=0;if(this.enemyManager&&this.enemyManager.waveInProgress){const S=this.enemyManager.getWaveStatus();o=S.enemiesKilled,c=S.totalEnemies}const h=`TARGETS: ${o}/${c}`;this.ctx.fillStyle="#00ffff",this.ctx.font=t?'bold 8px "Courier New", monospace':'bold 10px "Courier New", monospace',this.ctx.fillText(h,s+(t?8:10),a+(t?50:65));const d=`WAVES: ${e.progress.wavesCompleted}/${e.progress.requiredWaves}`;this.ctx.fillText(d,s+(t?8:10),a+(t?62:80));const g=s+(t?8:10),w=a+(t?68:85),v=i-(t?16:20),p=t?5:6;this.ctx.fillStyle="rgba(0, 20, 40, 0.8)",this.ctx.fillRect(g,w,v,p),this.ctx.strokeStyle="rgba(0, 255, 255, 0.5)",this.ctx.lineWidth=1,this.ctx.strokeRect(g,w,v,p);const f=e.progress.wavesCompleted/e.progress.requiredWaves,y=v*f,M=this.ctx.createLinearGradient(g,w,g+y,w);M.addColorStop(0,"#00ffff"),M.addColorStop(1,"#0088ff"),this.ctx.fillStyle=M,this.ctx.fillRect(g,w,y,p)}if(this.gameState==="LEVEL_COMPLETE"&&this.levelCompletionData){const o=this.enemyManager.getStatistics(),c={...this.levelCompletionData,progress:{...this.levelCompletionData.progress,enemiesEscaped:o.totalEnemiesEscaped,totalEnemies:o.totalEnemiesSpawned}};this.renderLevelCompletionOverlay(c)}else!e.levelInProgress&&e.levelConfig&&this.renderLevelCompletionOverlay(e);this.gameState==="GAME_OVER"&&this.gameOverData&&this.renderGameOverOverlay(this.gameOverData)}renderLevelCompletionOverlay(e){var p,f,y,M;const t=this.ctx.createRadialGradient(this.canvas.width/2,this.canvas.height/2,0,this.canvas.width/2,this.canvas.height/2,Math.max(this.canvas.width,this.canvas.height)/2);t.addColorStop(0,"rgba(0, 20, 40, 0.9)"),t.addColorStop(1,"rgba(0, 0, 20, 0.95)"),this.ctx.fillStyle=t,this.ctx.fillRect(0,0,this.canvas.width,this.canvas.height),this.ctx.fillStyle="#00ffff",this.ctx.font='bold 42px "Courier New", monospace',this.ctx.textAlign="center",this.ctx.shadowColor="#00ffff",this.ctx.shadowBlur=20;const s=e.levelNumber||e.currentLevel||1;this.ctx.fillText(`SECTOR ${s} COMPLETE`,this.canvas.width/2,this.canvas.height/2-70),this.ctx.strokeStyle="rgba(0, 255, 255, 0.3)",this.ctx.lineWidth=2,this.ctx.shadowBlur=10;const a=180,i=this.canvas.width/2,n=this.canvas.height/2-70;this.ctx.beginPath();for(let S=0;S<6;S++){const I=Math.PI/3*S,B=i+a*Math.cos(I),_=n+a*Math.sin(I);S===0?this.ctx.moveTo(B,_):this.ctx.lineTo(B,_)}this.ctx.closePath(),this.ctx.stroke(),this.ctx.shadowBlur=0,this.ctx.fillStyle="#00ffff",this.ctx.font='bold 22px "Courier New", monospace',this.ctx.shadowColor="#00ffff",this.ctx.shadowBlur=10;const r=e.score&&(e.score.totalScore||e.score.level)||0;this.ctx.fillText(`FINAL SCORE: ${r.toLocaleString()}`,this.canvas.width/2,this.canvas.height/2),this.ctx.font='bold 16px "Courier New", monospace';const o=e.enemiesDefeated||((p=e.progress)==null?void 0:p.enemiesDefeated)||0;this.ctx.fillText(`TARGETS ELIMINATED: ${o}`,this.canvas.width/2,this.canvas.height/2+35);const c=((f=e.progress)==null?void 0:f.enemiesEscaped)||0,h=((y=e.progress)==null?void 0:y.totalEnemies)||o+c;this.ctx.fillText(`TARGETS ESCAPED: ${c}`,this.canvas.width/2,this.canvas.height/2+60),this.ctx.fillText(`TOTAL TARGETS: ${h}`,this.canvas.width/2,this.canvas.height/2+85);const g=`COMPLETION TIME: ${(e.completionTime||0).toFixed(2)}s`;this.ctx.fillText(g,this.canvas.width/2,this.canvas.height/2+110);const w=e.tokenReward||0;if(w>0){this.ctx.fillStyle="#ff00ff",this.ctx.shadowColor="#ff00ff",this.ctx.shadowBlur=15,this.ctx.font='bold 18px "Courier New", monospace';const S=(M=this.tokenManager)!=null&&M.getBalanceDisplay?this.tokenManager.getBalanceDisplay().replace(/\(.*\)/,"").trim():"WISH";this.ctx.fillText(`${S} EARNED: ${w}`,this.canvas.width/2,this.canvas.height/2+140)}this.ctx.fillStyle="#00ffff",this.ctx.shadowColor="#00ffff",this.ctx.shadowBlur=8,this.ctx.font='bold 14px "Courier New", monospace';const v=.5+.5*Math.sin(Date.now()/300);this.ctx.globalAlpha=v,this.ctx.fillText("PRESS SPACE TO CONTINUE...",this.canvas.width/2,this.canvas.height/2+170),this.ctx.globalAlpha=1,this.ctx.shadowBlur=0}renderGameOverOverlay(e){var h;const t=this.ctx.createRadialGradient(this.canvas.width/2,this.canvas.height/2,0,this.canvas.width/2,this.canvas.height/2,Math.max(this.canvas.width,this.canvas.height)/2);t.addColorStop(0,"rgba(40, 0, 0, 0.9)"),t.addColorStop(1,"rgba(20, 0, 0, 0.95)"),this.ctx.fillStyle=t,this.ctx.fillRect(0,0,this.canvas.width,this.canvas.height),this.ctx.fillStyle="#ff4444",this.ctx.font='bold 48px "Courier New", monospace',this.ctx.textAlign="center",this.ctx.shadowColor="#ff0000",this.ctx.shadowBlur=25,this.ctx.fillText("GAME OVER",this.canvas.width/2,this.canvas.height/2-100),this.ctx.strokeStyle="rgba(255, 68, 68, 0.5)",this.ctx.lineWidth=2,this.ctx.shadowBlur=15;const s=200,a=this.canvas.width/2,i=this.canvas.height/2-100;this.ctx.beginPath();for(let d=0;d<6;d++)if(d!==2&&d!==5){const g=Math.PI/3*d,w=a+s*Math.cos(g),v=i+s*Math.sin(g);d===0||d===1&&d!==2?this.ctx.moveTo(w,v):d!==2&&d!==5&&this.ctx.lineTo(w,v)}this.ctx.stroke(),this.ctx.shadowBlur=0,this.ctx.fillStyle="#ff6666",this.ctx.font='bold 24px "Courier New", monospace',this.ctx.shadowColor="#ff4444",this.ctx.shadowBlur=15;const n=e.finalScore||0;this.ctx.fillText(`FINAL SCORE: ${n.toLocaleString()}`,this.canvas.width/2,this.canvas.height/2-20),this.ctx.font='bold 18px "Courier New", monospace';const r=e.levelReached||1,o=e.enemiesDefeated||0;if(this.ctx.fillText(`SECTORS CLEARED: ${r}`,this.canvas.width/2,this.canvas.height/2+20),this.ctx.fillText(`TARGETS ELIMINATED: ${o}`,this.canvas.width/2,this.canvas.height/2+50),e.tokenReward>0){this.ctx.fillStyle="#ff00ff",this.ctx.shadowColor="#ff00ff",this.ctx.shadowBlur=15,this.ctx.font='bold 20px "Courier New", monospace';const d=(h=this.tokenManager)!=null&&h.getBalanceDisplay?this.tokenManager.getBalanceDisplay().replace(/\(.*\)/,"").trim():"WISH";this.ctx.fillText(`${d} EARNED: ${e.tokenReward}`,this.canvas.width/2,this.canvas.height/2+90)}this.ctx.fillStyle="#ff6666",this.ctx.shadowColor="#ff4444",this.ctx.shadowBlur=10,this.ctx.font='bold 16px "Courier New", monospace';const c=.5+.5*Math.sin(Date.now()/400);this.ctx.globalAlpha=c,this.ctx.fillText("PRESS SPACE TO RETURN TO MAIN MENU...",this.canvas.width/2,this.canvas.height/2+140),this.ctx.globalAlpha=1,this.ctx.shadowBlur=0}renderConsumableHUD(){if(!this.consumableManager)return;const e=this.consumableManager.getCurrentConsumable();if(!e)return;const t=e.getDisplayInfo(),s=20,a=this.canvas.height-80,i=200,n=60,r=this.ctx.createLinearGradient(s,a,s+i,a+n);r.addColorStop(0,"rgba(10, 10, 46, 0.9)"),r.addColorStop(1,"rgba(0, 10, 30, 0.9)"),this.ctx.fillStyle=r,this.ctx.fillRect(s,a,i,n),this.ctx.strokeStyle=t.color,this.ctx.lineWidth=2,this.ctx.shadowColor=t.color,this.ctx.shadowBlur=10,this.ctx.strokeRect(s,a,i,n),this.ctx.shadowBlur=0,this.ctx.fillStyle=t.color,this.ctx.font="bold 24px Arial",this.ctx.textAlign="left",this.ctx.fillText(t.icon,s+10,a+30),this.ctx.fillStyle="#ffffff",this.ctx.font='bold 12px "Courier New", monospace',this.ctx.fillText(t.name,s+45,a+20),this.ctx.fillStyle="#00ffff",this.ctx.font='bold 10px "Courier New", monospace',this.ctx.fillText("Press [E] to activate",s+45,a+35),this.ctx.fillStyle="#ffaa00",this.ctx.font='9px "Courier New", monospace',this.ctx.fillText("Single-use tactical item",s+45,a+50),this.inputManager&&this.inputManager.isTouchDevice&&this.renderConsumableTouchButton(e)}renderConsumableTouchButton(e){const t=e.getDisplayInfo(),s=60,a=this.canvas.width-s-20,i=this.canvas.height-s-20;this.consumableButtonBounds={x:a,y:i,width:s,height:s};const n=this.ctx.createRadialGradient(a+s/2,i+s/2,0,a+s/2,i+s/2,s/2);n.addColorStop(0,t.color+"80"),n.addColorStop(1,t.color+"40"),this.ctx.fillStyle=n,this.ctx.fillRect(a,i,s,s),this.ctx.strokeStyle=t.color,this.ctx.lineWidth=3,this.ctx.shadowColor=t.color,this.ctx.shadowBlur=15,this.ctx.strokeRect(a,i,s,s),this.ctx.shadowBlur=0,this.ctx.fillStyle="#ffffff",this.ctx.font="bold 32px Arial",this.ctx.textAlign="center",this.ctx.fillText(t.icon,a+s/2,i+s/2+10),this.ctx.fillStyle="#00ffff",this.ctx.font='bold 12px "Courier New", monospace',this.ctx.fillText("E",a+s/2,i+s-5)}setupLevelManagerCallbacks(){this.levelManager.setOnLevelStart((e,t)=>{console.log(`Level ${e} started:`,t),this.playerShip&&(this.playerShip.resetHealthOnly(),console.log("Player ship health reset for new level, lives preserved")),this.enemyManager&&(this.enemyManager.reset(),console.log("Enemy manager reset for new level"))}),this.levelManager.setOnLevelComplete(async e=>{var t,s,a,i;if(console.log("Level completed:",e),e.completed){if(console.log(`Level ${e.levelNumber} completed in ${e.completionTime.toFixed(2)}s`),console.log(`Score: ${e.score.totalScore}, Enemies: ${e.enemiesDefeated}`),this.tokenManager){const n=await this.tokenManager.handleLevelCompletion(e,e.score.totalScore,(t=this.levelManager)==null?void 0:t.levelConfig);if(n.success){if(console.log(`Awarded ${n.transaction.amount} WISH tokens for level completion`),e.tokenReward=n.transaction.amount,await this.tokenManager.verifyWalletBalance(),this.tokenManager.isWalletConnected()&&e.tokenReward>0){const r=((s=this.orangeSDKManager)==null?void 0:s.currentSessionId)||"unknown",o=await this.tokenManager.sendFromHotWallet(e.tokenReward,r);o.success?console.log(`Successfully sent ${e.tokenReward} tokens from hot wallet to user`):console.error("Failed to send tokens from hot wallet to user:",o.reason)}}else if(console.log(`No tokens awarded for level completion: ${n.reason}`),e.tokenReward=0,this.tokenManager.isWalletConnected()){const r=((a=this.orangeSDKManager)==null?void 0:a.currentSessionId)||"unknown";await this.tokenManager.sendFromHotWallet(0,r)}}if(this.orangeSDKManager&&this.orangeSDKManager.onLevelCompleted(e),this.isGameOverCompletion){this.gameState="GAME_OVER",this.isGameOverCompletion=!1;const n=typeof e.score=="number"?e.score:((i=e.score)==null?void 0:i.totalScore)||0;this.gameOverData={finalScore:n,levelReached:e.levelNumber,enemiesDefeated:e.enemiesDefeated,tokenReward:e.tokenReward||0,timestamp:Date.now()},this.powerUpIndicator&&this.powerUpIndicator.hide(),this.boostGauge&&this.boostGauge.hide(),this.realityWarpManager&&this.realityWarpManager.getWarpState().status==="active"&&this.realityWarpManager.endWarp(),console.log("Game over data prepared:",this.gameOverData)}else this.gameState="LEVEL_COMPLETE",this.levelCompletionData=e,console.log("Level complete - waiting for user input to continue")}else console.log(`Level ${e.levelNumber} failed: ${e.reason}`),setTimeout(()=>{e.canRetry&&this.levelManager.startLevel(e.levelNumber)},3e3)}),this.levelManager.setOnScoreUpdate(e=>{})}setupEnemyManagerCallbacks(){this.enemyManager.onWaveComplete=(e,t)=>{console.log(`Wave ${e} completed with bonus: ${t}`),this.levelManager&&this.levelManager.recordWaveCompletion(e,t)},this.enemyManager.onEnemyEscaped=e=>{console.log(`Enemy ${e.id} escaped off-screen`),this.levelManager}}setupBossWarpManagerCallbacks(){this.bossWarpManager&&(this.bossWarpManager.onBossWarpStart=e=>{console.log("Boss warp started:",e)},this.bossWarpManager.onBossWarpComplete=e=>{console.log("Boss warp completed:",e)},this.bossWarpManager.onBossWarpFailed=e=>{console.log("Boss warp failed:",e)})}handleCollisions(){if(!this.enemyManager||!this.playerShip)return;const e=this.enemyManager.checkPlayerCollisions(this.playerShip);for(const n of e){const r=this.enemyManager.handlePlayerEnemyCollision(this.playerShip,n);console.log("Player-Enemy collision:",r)}const t=this.gameObjectManager.findByTag("projectile"),s=this.enemyManager.checkProjectileCollisions(t);for(const n of s){const r=this.enemyManager.handleProjectileEnemyCollision(n.projectile,n.enemy,n.damage);r.enemyDestroyed&&this.levelManager&&(this.levelManager.recordEnemyDefeat(n.enemy,r.scoreGained),r.powerUpDropType&&this.spawnPowerUpCollectible(r.powerUpDropType,r.enemyPosition))}if(this.enemyManager&&this.enemyManager.projectileSystem&&this.playerShip&&this.playerShip.active){const n=this.enemyManager.projectileSystem.checkPlayerCollisions(this.playerShip);for(const r of n){const o=this.enemyManager.projectileSystem.handlePlayerCollision(r,this.playerShip);o.damage&&this.playerShip.takeDamage(o.damage)}}const a=this.gameObjectManager.findByTag("player-projectile");if(a.length>0&&this.enemyManager){const n=this.enemyManager.checkProjectileCollisions(a);for(const r of n){const o=this.enemyManager.handleProjectileEnemyCollision(r.projectile,r.enemy,r.damage);o.enemyDestroyed&&this.levelManager&&(this.levelManager.recordEnemyDefeat(r.enemy,o.scoreGained),o.powerUpDropType&&this.spawnPowerUpCollectible(o.powerUpDropType,o.enemyPosition)),r.projectile.destroy()}}const i=this.gameObjectManager.findByTag("powerup-collectible");if(this.playerShip&&this.playerShip.active)for(const n of i)n.active&&this.playerShip.collidesWith(n)&&this.handlePowerUpCollection(n)}spawnPowerUpCollectible(e,t){try{const s=this.gameObjectManager.findByTag("powerup-collectible");if(s.length>=2){console.log(`Cannot spawn ${e} power-up - already ${s.length} power-ups on the field (limit: 2)`);return}const a=new $e(t.x,t.y,e);this.gameObjectManager.add(a),console.log(`Spawned ${e} power-up collectible at position: ${t.x}, ${t.y}`)}catch(s){console.error("Error spawning power-up collectible:",s)}}async handlePowerUpCollection(e){try{const t=e.collect();if(!t){console.log("Power-up collection failed - no power-up returned from collectible");return}await t.apply(this.playerShip)?(console.log(`Power-up ${t.type} collected and applied successfully`),this.playerShip.activePowerUps.set(t.type,t),this.genieInterface&&this.genieInterface.activePowerUps.set(t.type,t),this.showPowerUpCollectEffect(e.position)):console.warn(`Failed to apply collected power-up ${t.type}`)}catch(t){console.error("Error handling power-up collection:",t)}}showPowerUpCollectEffect(e){console.log(`Power-up collected at position: ${e.x}, ${e.y}`)}setupTokenManagerCallbacks(){this.tokenManager.setOnBalanceUpdate((e,t)=>{console.log(`Token balance updated: ${e} WISH tokens`)}),this.tokenManager.setOnTransaction(e=>{console.log("Token transaction:",e)}),this.tokenManager.setOnRewardEarned((e,t,s)=>{console.log(`Token reward earned: ${e} for ${t}`)})}setupGenieInterfaceCallbacks(){this.genieInterface.setOnPowerUpPurchased((e,t)=>{console.log(`Power-up purchased: ${e.type} for ${e.cost} tokens`)}),this.genieInterface.setOnWarpPurchased((e,t)=>{console.log(`Reality warp purchased: ${e.type} for ${e.cost} tokens`)}),this.genieInterface.setOnConsumablePurchased((e,t)=>{console.log(`Consumable purchased: ${e.type} for ${e.cost} tokens`)}),this.genieInterface.setOnClose(()=>{console.log("Genie interface closed, resuming game"),this.continueToNextLevel()})}async showGenieInterface(e){console.log("Showing Genie interface for level completion"),this.lastCompletionData=e,this.clearPowerUps(),this.pause(),await this.genieInterface.show()}proceedFromLevelComplete(){this.gameState==="LEVEL_COMPLETE"&&this.levelCompletionData&&(console.log("Proceeding from level complete screen to genie interface"),this.gameState="GAME_PLAY",this.showGenieInterface(this.levelCompletionData),this.levelCompletionData=null)}clearPowerUps(){if(this.genieInterface&&this.genieInterface.activePowerUps&&this.playerShip){console.log("Clearing per-level power-ups for next level");const e=this.playerShip,t=["SPREAD_AMMO","EXTRA_LIFE","EXTRA_WINGMAN","REALITY_WARP","RAPID_FIRE","SHIELD"],s=[];for(const[a,i]of this.genieInterface.activePowerUps)t.includes(a)&&s.push([a,i]);for(const[a,i]of s)console.log(`Removing per-level power-up: ${a}`),i.remove(e),this.genieInterface.activePowerUps.delete(a),e.activePowerUps&&e.activePowerUps.has(a)&&(e.activePowerUps.delete(a),console.log(`Also removed ${a} from playerShip.activePowerUps`));console.log("Per-level power-ups cleared from both GenieInterface and PlayerShip, permanent weapon power-ups preserved"),this.syncWeaponPowerUps()}}syncWeaponPowerUps(){if(!this.genieInterface||!this.playerShip||!this.playerShip.weaponSystem)return;const e=["KINETIC_BOOST","LASER_ROUNDS","FLAME_ROUNDS","ICE_ROUNDS","PLASMA_ROUNDS","SONIC_ROUNDS"],t=this.playerShip.weaponSystem.currentVariant,s=this.genieInterface.availablePowerUps.find(a=>e.includes(a.type)&&a.weaponVariant===t);s&&(this.genieInterface.activePowerUps.set(s.type,s),console.log(`Synchronized weapon power-up: ${s.type} for variant: ${t}`))}continueToNextLevel(){if(this.lastCompletionData){const e=this.lastCompletionData;this.lastCompletionData=null,this.resume(),e.nextLevel<=this.levelManager.maxLevels?(console.log(`Starting level ${e.nextLevel}`),this.levelManager.startLevel(e.nextLevel)):console.log("Game completed! All levels finished.")}}async handleGameOver(){if(console.log("Game Over - Player has no lives remaining"),this.isGameOverCompletion=!0,this.levelManager&&this.levelManager.levelInProgress)this.levelManager.completeLevel();else{this.gameState="GAME_OVER";const e=this.levelManager?this.levelManager.currentScore:0,t=this.levelManager?this.levelManager.currentLevel:1,s=this.levelManager?this.levelManager.enemiesDefeated:0;this.gameOverData={finalScore:e,levelReached:t,enemiesDefeated:s,tokenReward:0,timestamp:Date.now()},this.powerUpIndicator&&this.powerUpIndicator.hide(),this.boostGauge&&this.boostGauge.hide(),this.realityWarpManager&&this.realityWarpManager.getWarpState().status==="active"&&this.realityWarpManager.endWarp(),console.log("Game over data prepared:",this.gameOverData)}}returnToMainMenuFromGameOver(){console.log("Returning to main menu from game over"),this.gameState="MENU",this.gameOverData=null;const e=document.getElementById("game-container");if(e&&(e.style.display="none"),this.mainMenu&&this.mainMenu.show(),this.mainMenu&&this.tokenManager){const t=this.tokenManager.getBalanceDisplay?this.tokenManager.getBalanceDisplay():`${this.tokenManager.getBalance()} WISH`,s=document.getElementById("token-balance");s&&(s.textContent=t)}}testPowerUpSystem(){console.log("Testing power-up system..."),this.tokenManager.getBalance()<5e4&&(this.tokenManager.awardTokens(5e4,"debug_test_tokens"),console.log("Added test tokens")),["EXTRA_LIFE","SPREAD_AMMO","EXTRA_WINGMAN"].forEach(async(t,s)=>{setTimeout(async()=>{console.log(`Testing ${t} power-up...`),this.genieInterface&&(await this.genieInterface.applyPowerUp(t),console.log(`${t} test completed`))},s*2e3)}),console.log("Power-up system test initiated")}async activateConsumable(){if(console.log("🎯 activateConsumable called"),!this.consumableManager){console.error("❌ Consumable manager not available");return}console.log("✅ Consumable manager found, proceeding with activation"),console.log("🎮 Attempting to activate consumable...");const e=await this.consumableManager.activateConsumable();e.success?(console.log(`✅ Consumable activated: ${e.consumable.type}`),console.log("🎯 Effect applied to game objects"),this.ctx&&this.showConsumableActivationFeedback(e.consumable)):console.log(`❌ Consumable activation failed: ${e.reason} - ${e.message||"No message"}`)}showConsumableActivationFeedback(e){const t=e.getDisplayInfo();console.log(`✨ ${t.name} activated! ${t.icon}`)}handleConsumableTouchInput(){if(!(!this.consumableButtonBounds||!this.inputManager)){for(const[e,t]of this.inputManager.touchStarted)if(this.isTouchInBounds(t,this.consumableButtonBounds)){this.activateConsumable();break}}}isTouchInBounds(e,t){return e.x>=t.x&&e.x<=t.x+t.width&&e.y>=t.y&&e.y<=t.y+t.height}handleBoostActivation(){if(!this.playerShip||!this.boostGauge)return;this.inputManager.isActionDown("boost")&&this.boostGauge.isBoostAvailable()?(this.playerShip.setBoostActive(!0),this.boostGauge.setBoostActive(!0)):(this.playerShip.setBoostActive(!1),this.boostGauge.setBoostActive(!1))}async initializeOrangeSDK(){try{await this.orangeSDKManager.initialize(),console.log("Orange SDK initialized successfully"),this.hangarManager&&this.hangarManager.reloadUpgradeData()}catch(e){console.error("Failed to initialize Orange SDK:",e)}}setupOrangeSDKCallbacks(){this.orangeSDKManager.setOnSpecialStatusCallback((e,t)=>{this.handleSpecialStatus(e,t)}),this.orangeSDKManager.setOnDataSavedCallback((e,t)=>{console.log(`Game data saved to Orange SDK (${t})`)}),this.orangeSDKManager.setOnSaveErrorCallback((e,t)=>{console.error(`Failed to save game data (${t}):`,e)})}handleSpecialStatus(e,t){switch(e){case"bonus_lives":this.playerShip&&t>0&&(this.playerShip.addLives(t),console.log(`Bonus lives awarded: ${t} (login streak bonus)`));break;case"tournament_participant":t&&console.log("Tournament participant status active");break;case"achievement_unlock":console.log(`Achievement unlocked: ${t}`);break;default:console.log(`Unknown special status: ${e} = ${t}`)}}isMobileDevice(){return window.innerWidth<=768}handleResize(){const e=document.getElementById("game-container");if(e){const{width:t,height:s}=e.getBoundingClientRect(),a=this.isMobileDevice();a?(this.canvas.width=t,this.canvas.height=s):(this.canvas.width=t,this.canvas.height=s),console.log(`Canvas resized to: ${this.canvas.width}x${this.canvas.height} (Mobile: ${a})`),this.bloomEffect&&this.bloomEffect.resize()}}initBloomDebugControls(){document.addEventListener("keydown",e=>{if(this.bloomEffect)switch(e.key.toLowerCase()){case"b":this.bloomEffect.toggle();break;case"=":case"+":const t=this.bloomEffect.getSettings().intensity;this.bloomEffect.setIntensity(t+.1);break;case"-":case"_":const s=this.bloomEffect.getSettings().intensity;this.bloomEffect.setIntensity(s-.1);break;case"[":const a=this.bloomEffect.getSettings().threshold;this.bloomEffect.setThreshold(a-.05);break;case"]":const i=this.bloomEffect.getSettings().threshold;this.bloomEffect.setThreshold(i+.05);break}})}async loadAudioFiles(){try{const e=[];for(const t of dt)try{(await fetch(t.url,{method:"HEAD"})).ok?e.push(t):console.warn(`Audio file not found: ${t.url}`)}catch{console.warn(`Audio file not accessible: ${t.url}`)}e.length>0&&await this.audioManager.loadAudioFiles(e)}catch{}}setupAudioContextActivation(){let e=!1;const t=async()=>{if(!e&&this.audioManager)try{await this.audioManager.handleUserInteraction(),e=!0,console.log("Audio context activated through user interaction"),document.removeEventListener("click",t),document.removeEventListener("keydown",t),document.removeEventListener("touchstart",t)}catch(s){console.error("Error activating audio context:",s)}};document.addEventListener("click",t),document.addEventListener("keydown",t),document.addEventListener("touchstart",t),console.log("Audio context activation handlers set up")}}class pt{constructor(e={}){this.config={baseUrl:"https://api.bedrockpassport.com",authCallbackUrl:window.location.origin,tenantId:e.tenantId||"orangeid-4Pf9tVe0mL",subscriptionKey:e.subscriptionKey||"ca85570bb0c949738f6732e87954c961",debugMode:e.debugMode||this.isDebugEnvironment()},!this.isDebugEnvironment()&&window.location.hostname!=="localhost"&&window.location.hostname!=="127.0.0.1"&&window.location.origin!=="https://warpsector.merchgenieai.com"&&console.warn("Unexpected origin detected:",window.location.origin),this.user=null,this.token=null,this.refreshToken=null,this.isAuthenticated=!1,this.authCallbacks=[],this.setupAuthEventListeners(),this.checkExistingAuth()}isDebugEnvironment(){return window.location.hostname==="localhost"||window.location.hostname==="127.0.0.1"||window.location.search.includes("debug=true")}async initializeOrangeID(){return console.log("Orange ID widget initialization handled by HTML script"),Promise.resolve()}checkExistingAuth(){const e=new URLSearchParams(window.location.search),t=e.get("token"),s=e.get("refreshToken");if(t&&s){console.log("Found auth tokens in URL, processing callback...");return}console.log("No existing authentication found")}setupAuthEventListeners(){window.addEventListener("orangeAuthSuccess",e=>{this.handleAuthSuccess(e.detail.token,e.detail.refreshToken)}),window.addEventListener("orangeAuthError",e=>{this.handleAuthError(e.detail.error)})}async handleAuthSuccess(e,t){var s,a;this.token=e,this.refreshToken=t;try{const i=await this.validateToken(e),n=this.checkAndEnableDebugMode(i);if(this.user=n,this.isAuthenticated=!0,window.ethereum&&((a=(s=window.gameEngine)==null?void 0:s.ethTestModeManager)!=null&&a.isTestMode))try{await window.ethereum.request({method:"wallet_switchEthereumChain",params:[{chainId:"0x7a69"}]})}catch(o){console.warn("Could not switch to Hardhat chain:",o)}const r=new URL(window.location);r.searchParams.delete("token"),r.searchParams.delete("refreshToken"),window.history.replaceState({},document.title,r),this.notifyAuthCallbacks(!0,this.user)}catch(i){this.handleAuthError(i)}}handleAuthError(e){this.isAuthenticated=!1,this.user=null,this.token=null,this.refreshToken=null,this.notifyAuthCallbacks(!1,e)}async validateToken(e){const t=await fetch("https://api.bedrockpassport.com/api/v1/auth/user",{headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(!t.ok)throw new Error("Token validation failed");return await t.json()}isDebugWallet(e){return e&&e.toLowerCase()==="******************************************".toLowerCase()}checkAndEnableDebugMode(e){return e&&e.ethAddress&&this.isDebugWallet(e.ethAddress)&&(console.log("🔧 Debug wallet detected - enabling debug mode with fake WISH tokens"),this.config.debugMode=!0,e.isDebugUser=!0),e}async logout(){this.user=null,this.token=null,this.refreshToken=null,this.isAuthenticated=!1,this.notifyAuthCallbacks(!1,null)}getUser(){return this.user}onAuthStateChange(e){this.authCallbacks.push(e)}notifyAuthCallbacks(e,t){this.authCallbacks.forEach(s=>{s(e,t)})}}class gt{constructor(e){this.gameEngine=e,this.authManager=new pt,this.container=null,this.isVisible=!1,this.setupEventListeners()}async initialize(){this.createMenuHTML();try{console.log("Checking for Orange ID initialization function..."),console.log("window.initializeOrangeIDWidget exists:",!!window.initializeOrangeIDWidget),console.log("bedrock-login-widget container exists:",!!document.getElementById("bedrock-login-widget")),window.initializeOrangeIDWidget?(console.log("Calling Orange ID initialization..."),window.initializeOrangeIDWidget(),console.log("Orange ID initialization called successfully")):console.warn("Orange ID initialization function not found")}catch(e){console.error("Failed to initialize Orange ID:",e),this.showError(`Authentication system failed to load: ${e.message}. You can use debug login instead.`)}this.authManager.onAuthStateChange((e,t)=>{e?this.onAuthenticationSuccess(t):this.onAuthenticationFailure(t)})}createMenuHTML(){this.container=document.createElement("div"),this.container.id="main-menu",this.container.className="main-menu",this.container.innerHTML=`
      <div class="menu-background">
        <div class="falling-stars">
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
        </div>
        <div class="menu-content">
          <h1 class="game-title">Warp Sector</h1>
          <p class="game-subtitle">Reality Warping Shooter</p>
          
          <div class="auth-section" id="auth-section">
            <div class="orange-id-container">
              <div id="bedrock-login-widget"></div>
            </div>
          </div>
          
          <div class="game-controls" id="game-controls" style="display: none;">
            <button id="start-game-btn" class="menu-button primary">
              Start Game
            </button>
            <button id="genie-shop-btn" class="menu-button">
              Genie Shop 🪔
            </button>
            <button id="audio-settings-btn" class="menu-button">
              Audio Settings 🔊
            </button>
            <button id="instructions-btn" class="menu-button">
              How to Play
            </button>
            <button id="logout-btn" class="menu-button secondary">
              Logout
            </button>
          </div>
          
          <!-- Audio Settings Panel -->
          <div class="audio-settings-panel" id="audio-settings-panel" style="display: none;">
            <h3>Audio Settings</h3>
            <div class="audio-control">
              <label for="master-volume">Master Volume:</label>
              <input type="range" id="master-volume" min="0" max="100" value="70">
              <span id="master-volume-value">70%</span>
            </div>
            <div class="audio-control">
              <label for="music-volume">Music Volume:</label>
              <input type="range" id="music-volume" min="0" max="100" value="70">
              <span id="music-volume-value">70%</span>
            </div>
            <div class="audio-control">
              <label for="sfx-volume">Sound Effects Volume:</label>
              <input type="range" id="sfx-volume" min="0" max="100" value="80">
              <span id="sfx-volume-value">80%</span>
            </div>
            <div class="audio-control">
              <label for="ambient-volume">Ambient Volume:</label>
              <input type="range" id="ambient-volume" min="0" max="100" value="30">
              <span id="ambient-volume-value">30%</span>
            </div>
            <div class="audio-control">
              <label>
                <input type="checkbox" id="mute-all"> Mute All
              </label>
            </div>
            <div class="audio-control-buttons">
              <button id="apply-audio-settings" class="menu-button primary">Apply</button>
              <button id="cancel-audio-settings" class="menu-button secondary">Cancel</button>
            </div>
          </div>
          
          <div class="user-info" id="user-info" style="display: none;">
            <p>Welcome, <span id="user-name"></span>!</p>
          </div>
          
          <div class="error-message" id="error-message" style="display: none;"></div>
        </div>
      </div>
    `,document.body.appendChild(this.container)}setupEventListeners(){document.addEventListener("click",async e=>{e.target.id==="start-game-btn"?this.startGame():e.target.id==="genie-shop-btn"?(this.gameEngine.audioManager&&await this.gameEngine.audioManager.handleUserInteraction(),this.showGenieShop()):e.target.id==="audio-settings-btn"?(this.gameEngine.audioManager&&await this.gameEngine.audioManager.handleUserInteraction(),this.showAudioSettings()):e.target.id==="instructions-btn"?(this.gameEngine.audioManager&&await this.gameEngine.audioManager.handleUserInteraction(),this.showInstructions()):e.target.id==="logout-btn"?this.logout():e.target.id==="apply-audio-settings"?this.applyAudioSettings():e.target.id==="cancel-audio-settings"&&this.hideAudioSettings()}),document.addEventListener("input",e=>{e.target.id==="master-volume"?this.updateVolumeDisplay("master-volume","master-volume-value"):e.target.id==="music-volume"?this.updateVolumeDisplay("music-volume","music-volume-value"):e.target.id==="sfx-volume"?this.updateVolumeDisplay("sfx-volume","sfx-volume-value"):e.target.id==="ambient-volume"&&this.updateVolumeDisplay("ambient-volume","ambient-volume-value")}),document.addEventListener("change",e=>{e.target.id==="mute-all"&&this.toggleMuteAll(e.target.checked)})}onAuthenticationSuccess(e){document.getElementById("auth-section").style.display="none",document.getElementById("game-controls").style.display="block",document.getElementById("user-info").style.display="block",document.getElementById("user-name").textContent=e.name||e.email||"Player",this.gameEngine&&this.gameEngine.tokenManager&&this.gameEngine.tokenManager.initializeWallet(),this.loadUserTokenBalance(),this.hideError()}onAuthenticationFailure(e){console.error("Authentication failed:",e),document.getElementById("auth-section").style.display="block",document.getElementById("game-controls").style.display="none",document.getElementById("user-info").style.display="none",e&&typeof e=="object"&&this.showError("Authentication failed. Please try again.")}async loadUserTokenBalance(){if(this.gameEngine&&this.gameEngine.tokenManager){const e=this.gameEngine.tokenManager.getBalanceDisplay?this.gameEngine.tokenManager.getBalanceDisplay():`${this.gameEngine.tokenManager.getBalance()} WISH`,t=document.getElementById("token-balance");t&&(t.textContent=e)}else{const e=document.getElementById("token-balance");e&&(e.textContent="100 WISH")}}async startGame(){if(!this.authManager.isAuthenticated){this.showError("Please authentication first");return}this.gameEngine.audioManager&&await this.gameEngine.audioManager.handleUserInteraction(),this.hide(),this.gameEngine.startGameplay(this.authManager.getUser())}showGenieShop(){var e;if(!this.authManager.isAuthenticated){this.showError("Please authenticate first");return}this.gameEngine&&this.gameEngine.genieInterface?((e=this.authManager.user)!=null&&e.isDebugUser&&this.gameEngine.tokenManager&&this.gameEngine.tokenManager.getBalance()<100&&(console.log("🔧 Debug user detected - awarding fake WISH tokens"),this.gameEngine.tokenManager.awardTokens(5e4,"debug_menu_tokens")),this.gameEngine.genieInterface.show()):this.showError("Genie Shop not available. Please start the game first.")}showInstructions(){alert(`
      Warp Sector - How to Play
      🚀 CONTROLS:
      • Arrow Keys / WASD / Joystick on Mobile: Move ship
      • Spacebar/Joystick: Fire weapons
      • Shift key / Double tap: BOOST
      • E Key / Tap Power Up Icon: Activate power up
      • P: Pause game
      
      💎 WISH TOKENS:
      • Earn tokens by defeating as many enemies as possible.
      • Spend tokens on reality warps and power-ups.
      • Make purchases in the Genie Shop.
      
      🌟 REALITY WARPS:
      • Transform battlefields with AI-generated environments
      • Different environments affect enemy effectiveness
      • Strategic warping can give you tactical advantages
    `)}async logout(){await this.authManager.logout(),this.onAuthenticationFailure(null)}show(){this.container&&(this.container.style.display="block",this.isVisible=!0)}hide(){this.container&&(this.container.style.display="none",this.isVisible=!1)}showAudioSettings(){const e=document.getElementById("audio-settings-panel"),t=document.getElementById("game-controls");if(e&&t)if(t.style.display="none",e.style.display="block",this.gameEngine&&this.gameEngine.audioManager)try{const s=this.gameEngine.audioManager.getVolumeSettings();document.getElementById("master-volume").value=Math.round(s.master*100),document.getElementById("music-volume").value=Math.round(s.music*100),document.getElementById("sfx-volume").value=Math.round(s.sfx*100),document.getElementById("ambient-volume").value=Math.round(s.ambient*100),document.getElementById("mute-all").checked=!s.enabled,this.updateVolumeDisplay("master-volume","master-volume-value"),this.updateVolumeDisplay("music-volume","music-volume-value"),this.updateVolumeDisplay("sfx-volume","sfx-volume-value"),this.updateVolumeDisplay("ambient-volume","ambient-volume-value")}catch(s){console.warn("Could not load audio settings:",s)}else console.warn("AudioManager not available")}hideAudioSettings(){const e=document.getElementById("audio-settings-panel"),t=document.getElementById("game-controls");e&&t&&(e.style.display="none",t.style.display="block")}applyAudioSettings(){if(this.gameEngine&&this.gameEngine.audioManager)try{const e=document.getElementById("master-volume").value/100,t=document.getElementById("music-volume").value/100,s=document.getElementById("sfx-volume").value/100,a=document.getElementById("ambient-volume").value/100,i=document.getElementById("mute-all").checked;this.gameEngine.audioManager.setVolume("master",e),this.gameEngine.audioManager.setVolume("music",t),this.gameEngine.audioManager.setVolume("sfx",s),this.gameEngine.audioManager.setVolume("ambient",a),i?this.gameEngine.audioManager.mute("all",!0):this.gameEngine.audioManager.mute("all",!1),console.log("Audio settings applied successfully")}catch(e){console.warn("Could not apply audio settings:",e)}else console.warn("AudioManager not available for applying settings");this.hideAudioSettings()}updateVolumeDisplay(e,t){const s=document.getElementById(e),a=document.getElementById(t);s&&a&&(a.textContent=s.value+"%")}toggleMuteAll(e){if(this.gameEngine&&this.gameEngine.audioManager)try{this.gameEngine.audioManager.mute("all",e),["master-volume","music-volume","sfx-volume","ambient-volume"].forEach(s=>{const a=document.getElementById(s);a&&(a.disabled=e)})}catch(t){console.warn("Could not toggle mute:",t)}}showError(e){const t=document.getElementById("error-message");t&&(t.textContent=e,t.style.display="block")}hideError(){const e=document.getElementById("error-message");e&&(e.style.display="none")}destroy(){this.container&&(document.body.removeChild(this.container),this.container=null)}}const x={baseUrl:"https://api.bedrockpassport.com",authCallbackUrl:window.location.origin,tenantId:"orangeid-4Pf9tVe0mL",subscriptionKey:"ca85570bb0c949738f6732e87954c961"};(!x.tenantId||!x.subscriptionKey)&&(console.error("Orange ID configuration missing! Please check your .env file for:"),console.error("- VITE_ORANGE_ID_TENANT_ID"),console.error("- VITE_ORANGE_ID_SUBSCRIPTION_KEY"));window.OrangeIDConfig=x;console.log("Orange ID Config loaded:",{baseUrl:x.baseUrl,authCallbackUrl:x.authCallbackUrl,tenantId:x.tenantId?"***"+x.tenantId.slice(-4):"MISSING",subscriptionKey:x.subscriptionKey?"***"+x.subscriptionKey.slice(-4):"MISSING"});class mt{constructor(){this.gameEngine=null,this.mainMenu=null,this.isInitialized=!1}async initialize(){if(!this.isInitialized)try{try{const t=D.getApiUrl("/api/health"),s=await fetch(t,{method:"GET",timeout:5e3});s.ok||console.warn("Server health check failed:",s.status)}catch(t){console.warn("Server health check failed. The environment server may not be running:",t)}const e=document.getElementById("gameCanvas");if(!e)throw new Error("Game canvas not found");this.gameEngine=new ut(e),await this.gameEngine.init(),this.mainMenu=new gt(this.gameEngine),await this.mainMenu.initialize(),this.gameEngine.setMainMenu(this.mainMenu),this.mainMenu.show(),this.isInitialized=!0}catch(e){console.error("Failed to initialize game:",e),this.showInitializationError(e)}}showInitializationError(e){document.body.innerHTML=`
      <div style="
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        background: #0a0a2e;
        color: white;
        font-family: Arial, sans-serif;
        text-align: center;
      ">
        <div>
          <h1>Game Initialization Failed</h1>
          <p>Error: ${e.message}</p>
          <button onclick="location.reload()" style="
            background: #00ffff;
            color: #0a0a2e;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 20px;
          ">
            Retry
          </button>
        </div>
      </div>
    `}}let T=null;document.addEventListener("DOMContentLoaded",async()=>{T=new mt,await T.initialize(),T.gameEngine&&(window.gameEngine=T.gameEngine),T.mainMenu&&T.mainMenu.authManager&&(window.AuthManager=T.mainMenu.authManager)});document.addEventListener("visibilitychange",()=>{T&&T.gameEngine&&(document.hidden?T.gameEngine.pause():T.gameEngine.resume())});window.addEventListener("beforeunload",async u=>{T&&T.gameEngine&&await T.gameEngine.destroy()});document.addEventListener("pagehide",async u=>{T&&T.gameEngine&&await T.gameEngine.destroy()});export{G,m as V,Me as W};
//# sourceMappingURL=main-D3TgL-C9.js.map
