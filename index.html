<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WarpSector - Reality Warping Shooter</title>
    <link rel="stylesheet" href="src/styles/menu.css">
    <link rel="stylesheet" href="src/styles/game.css">
    <link rel="stylesheet" href="src/styles/genie-interface.css">
</head>
<body>
    <div id="game-container">
        <canvas id="gameCanvas"></canvas>
    </div>
    

    <!-- Add this BEFORE your Orange ID script -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- React libraries for Orange ID -->
    <script src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>

    <!-- Bedrock Passport for Orange ID -->
    <script src="https://public-cdn-files.pages.dev/bedrock-passport.umd.js"></script>

    <!-- Orange ID Initialization Script -->
    <script>
      // Orange ID configuration will be loaded from environment config
      window.OrangeIDConfig = null; // Will be set by the config module

      // Global function to initialize Orange ID widget
      window.initializeOrangeIDWidget = function() {
        // Prevent multiple initializations
        if (window.orangeIdInitialized) {
          console.log('Orange ID already initialized, skipping...');
          return;
        }

        console.log('initializeOrangeIDWidget called');
        console.log('React available:', !!window.React);
        console.log('ReactDOM available:', !!window.ReactDOM);
        console.log('Bedrock available:', !!window.Bedrock);

        // Ensure required libraries are loaded
        if (!window.React || !window.ReactDOM || !window.Bedrock) {
          console.error('Error: Required libraries failed to load.');
          console.error('React:', !!window.React, 'ReactDOM:', !!window.ReactDOM, 'Bedrock:', !!window.Bedrock);
          const container = document.getElementById('bedrock-login-widget');
          if (container) {
            container.innerHTML =
              '<div style="color: red; text-align: center;">Error loading authentication widget. Please check your internet connection.</div>';
          }
          return;
        }

        // Container for widget
        const container = document.getElementById('bedrock-login-widget');
        if (!container) {
          console.error('Error: bedrock-login-widget container not found.');
          return;
        }

        const root = ReactDOM.createRoot(container);

        // Check if we're handling a callback
        const params = new URLSearchParams(window.location.search);
        const token = params.get('token');
        const refreshToken = params.get('refreshToken');

        if (token && refreshToken) {
          // We're handling a callback
          // Create the callback processor component
          function AuthCallbackProcessor() {
            const {loginCallback} = Bedrock.useBedrockPassport();
            const [message, setMessage] = React.useState(
              'Processing authentication...'
            );

            React.useEffect(() => {
              async function processLogin() {
                try {
                  setMessage('Verifying tokens...');
                  const success = await loginCallback(token, refreshToken);

                  if (success) {
                    setMessage('Login successful! Loading game...');
                    // Remove token parameters from URL
                    window.history.replaceState(
                      {},
                      document.title,
                      window.location.pathname
                    );
                    // Dispatch custom event for the AuthManager to handle
                    window.dispatchEvent(new CustomEvent('orangeAuthSuccess', {
                      detail: { token, refreshToken }
                    }));
                    // Don't redirect - let the game handle showing the main menu
                  } else {
                    setMessage('Authentication failed. Please try again.');
                    window.dispatchEvent(new CustomEvent('orangeAuthError', {
                      detail: { error: new Error('Login callback failed') }
                    }));
                  }
                } catch (error) {
                  console.error('Login error:', error);
                  setMessage('An error occurred during login.');
                  window.dispatchEvent(new CustomEvent('orangeAuthError', {
                    detail: { error }
                  }));
                }
              }

              processLogin();
            }, [loginCallback]);

            return React.createElement(
              'div',
              {style: {textAlign: 'center', padding: '1rem'}},
              message
            );
          }

          // Render callback processor
          root.render(
            React.createElement(
              Bedrock.BedrockPassportProvider,
              window.OrangeIDConfig,
              React.createElement(AuthCallbackProcessor)
            )
          );
        } else {
          // Normal login flow
          root.render(
            React.createElement(
              Bedrock.BedrockPassportProvider,
              window.OrangeIDConfig,
              React.createElement(Bedrock.LoginPanel, {
                // Content options — No changes needed unless specific customization is required
                title: "Sign in to",
                logo: "assets/sprites/enemies/crystal.png",
                logoAlt: "Orange Web3",
                walletButtonText: "Connect Wallet",
                showConnectWallet: true,
                separatorText: "OR",

                // Feature toggles — Adjust these based on which login methods you want to support
                features: {
                  enableWalletConnect: true,
                  enableAppleLogin: false,
                  enableGoogleLogin: false,
                  enableEmailLogin: false,
                },

                // Style options - use the working classes from your documentation
                titleClass: "text-xl font-bold text-white",
                logoClass: "ml-2 h-6 md:h-8",
                panelClass: "bg-gray-900 bg-opacity-90 p-4 md:p-6 rounded-xl max-w-md mx-auto border border-orange-500",
                buttonClass: "bg-gray-800 hover:bg-gray-700 text-white font-medium py-3 px-6 rounded-lg border border-gray-600 hover:border-orange-500 transition-colors duration-200",
                separatorTextClass: "text-gray-400 text-sm font-medium",
                separatorClass: "border-t border-gray-600 my-4",
                linkRowClass: "justify-center space-x-4",
                headerClass: "justify-center mb-6",
              })
            )
          );
        }

        // Mark as initialized to prevent multiple initializations
        window.orangeIdInitialized = true;
      };

      // Check if we're handling a callback immediately on page load
      const params = new URLSearchParams(window.location.search);
      const token = params.get('token');
      const refreshToken = params.get('refreshToken');

      if (token && refreshToken) {
        // For callbacks, we need to initialize immediately since the container might not exist yet
        // Create a temporary container for the callback processing
        document.addEventListener('DOMContentLoaded', function() {
          if (!document.getElementById('bedrock-login-widget')) {
            const tempContainer = document.createElement('div');
            tempContainer.id = 'bedrock-login-widget';
            tempContainer.style.cssText = 'position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 10000; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 4px 20px rgba(0,0,0,0.3);';
            document.body.appendChild(tempContainer);
          }
          window.initializeOrangeIDWidget();
        });
      }
    </script>

    <!-- Game scripts -->
    <script type="module" src="src/main.js"></script>
</body>
</html>
