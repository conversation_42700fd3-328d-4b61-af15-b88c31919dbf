<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication - WarpSector</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0a0a2e 0%, #16213e 50%, #0f3460 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
        }
        .auth-container {
            text-align: center;
            padding: 2rem;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 12px;
            border: 1px solid rgba(255, 165, 0, 0.3);
            max-width: 400px;
            width: 90%;
        }
        .loading {
            font-size: 18px;
            margin-bottom: 20px;
        }
        .spinner {
            border: 3px solid rgba(255, 165, 0, 0.3);
            border-top: 3px solid #ff6b35;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .error {
            color: #ff4444;
            margin-top: 20px;
        }
        .retry-btn {
            background: #ff6b35;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin-top: 15px;
            font-size: 16px;
        }
        .retry-btn:hover {
            background: #e55a2b;
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <h2>WarpSector Authentication</h2>
        <div id="auth-status" class="loading">Processing authentication...</div>
        <div class="spinner"></div>
        <div id="error-message" class="error" style="display: none;"></div>
    </div>

    <!-- React libraries for Orange ID -->
    <script src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    
    <!-- Bedrock Passport for Orange ID -->
    <script src="https://public-cdn-files.pages.dev/bedrock-passport.umd.js"></script>

    <script>
        // Orange ID configuration - will be replaced by build process
        const bedrockConfig = {
            baseUrl: 'https://api.bedrockpassport.com',
            authCallbackUrl: window.location.origin + '/auth/callback.html',
            tenantId: 'orangeid-4Pf9tVe0mL',
            subscriptionKey: 'ca85570bb0c949738f6732e87954c961',
        };

        function updateStatus(message) {
            document.getElementById('auth-status').textContent = message;
        }

        function showError(message) {
            document.getElementById('error-message').textContent = message;
            document.getElementById('error-message').style.display = 'block';
            document.querySelector('.spinner').style.display = 'none';
        }

        function processAuthCallback() {
            // Check if required libraries are loaded
            if (!window.React || !window.ReactDOM || !window.Bedrock) {
                showError('Authentication libraries failed to load. Please refresh the page.');
                return;
            }

            // Get URL parameters
            const params = new URLSearchParams(window.location.search);
            const token = params.get('token');
            const refreshToken = params.get('refreshToken');

            if (!token || !refreshToken) {
                showError('Invalid authentication callback. Missing tokens.');
                return;
            }

            updateStatus('Verifying authentication tokens...');

            // Create React root for callback processing
            const container = document.createElement('div');
            document.body.appendChild(container);
            const root = ReactDOM.createRoot(container);

            // Callback processor component
            function AuthCallbackProcessor() {
                const { loginCallback } = Bedrock.useBedrockPassport();
                const [processing, setProcessing] = React.useState(true);

                React.useEffect(() => {
                    async function processLogin() {
                        try {
                            updateStatus('Validating with Orange ID...');
                            const success = await loginCallback(token, refreshToken);

                            if (success) {
                                updateStatus('Authentication successful! Redirecting to game...');
                                
                                // Store auth success flag for main page
                                sessionStorage.setItem('orangeAuthSuccess', 'true');
                                sessionStorage.setItem('orangeAuthToken', token);
                                sessionStorage.setItem('orangeAuthRefreshToken', refreshToken);
                                
                                // Redirect to main page after short delay
                                setTimeout(() => {
                                    window.location.href = '/';
                                }, 1500);
                            } else {
                                showError('Authentication failed. Please try logging in again.');
                                setTimeout(() => {
                                    window.location.href = '/';
                                }, 3000);
                            }
                        } catch (error) {
                            console.error('Authentication error:', error);
                            showError('An error occurred during authentication. Please try again.');
                            setTimeout(() => {
                                window.location.href = '/';
                            }, 3000);
                        }
                        setProcessing(false);
                    }

                    processLogin();
                }, [loginCallback]);

                return null; // This component doesn't render anything visible
            }

            // Render the callback processor
            root.render(
                React.createElement(
                    Bedrock.BedrockPassportProvider,
                    bedrockConfig,
                    React.createElement(AuthCallbackProcessor)
                )
            );
        }

        // Start processing when page loads
        document.addEventListener('DOMContentLoaded', processAuthCallback);
    </script>
</body>
</html>
