.main-menu {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  font-family: 'Arial', sans-serif;
}

.menu-background {
  width: 100%;
  height: 100%;
  background: transparent;
  position: relative;
  overflow: hidden;
}


.falling-stars {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.falling-star {
  position: absolute;
  width: 4px;
  height: 4px;
  background: radial-gradient(circle, #eee, transparent);
  border-radius: 50%;
  opacity: 0;
  box-shadow: 0 0 6px rgba(255, 255, 255, 0.5);
}

.falling-star.small {
  width: 2px;
  height: 2px;
  background: radial-gradient(circle, #fff, transparent);
  box-shadow: 0 0 4px rgba(255, 255, 255, 0.7);
}

.falling-star.tiny {
  width: 1px;
  height: 1px;
  background: radial-gradient(circle, rgba(255,255,255,0.8), transparent);
  box-shadow: 0 0 2px rgba(255, 255, 255, 0.3);
}

/* Create 40 falling stars with different positions and timing */
.falling-star:nth-child(1) { left: 5%; animation: fall 3s linear infinite; }
.falling-star:nth-child(2) { left: 12%; animation: fall 4s linear infinite 0.3s; }
.falling-star:nth-child(3) { left: 18%; animation: fall 3.5s linear infinite 0.7s; }
.falling-star:nth-child(4) { left: 25%; animation: fall 4.5s linear infinite 1.1s; }
.falling-star:nth-child(5) { left: 32%; animation: fall 3.2s linear infinite 1.5s; }
.falling-star:nth-child(6) { left: 38%; animation: fall 4.2s linear infinite 1.9s; }
.falling-star:nth-child(7) { left: 45%; animation: fall 3.8s linear infinite 2.3s; }
.falling-star:nth-child(8) { left: 52%; animation: fall 4.8s linear infinite 2.7s; }
.falling-star:nth-child(9) { left: 58%; animation: fall 3.6s linear infinite 3.1s; }
.falling-star:nth-child(10) { left: 65%; animation: fall 4.3s linear infinite 3.5s; }
.falling-star:nth-child(11) { left: 72%; animation: fall 3.9s linear infinite 3.9s; }
.falling-star:nth-child(12) { left: 78%; animation: fall 4.1s linear infinite 4.3s; }
.falling-star:nth-child(13) { left: 85%; animation: fall 3.7s linear infinite 4.7s; }
.falling-star:nth-child(14) { left: 92%; animation: fall 4.4s linear infinite 5.1s; }
.falling-star:nth-child(15) { left: 8%; animation: fall 3.3s linear infinite 5.5s; }
.falling-star:nth-child(16) { left: 15%; animation: fall 4.6s linear infinite 5.9s; }
.falling-star:nth-child(17) { left: 22%; animation: fall 3.4s linear infinite 6.3s; }
.falling-star:nth-child(18) { left: 28%; animation: fall 4.7s linear infinite 6.7s; }
.falling-star:nth-child(19) { left: 35%; animation: fall 3.5s linear infinite 7.1s; }
.falling-star:nth-child(20) { left: 42%; animation: fall 4.8s linear infinite 7.5s; }
.falling-star:nth-child(21) { left: 48%; animation: fall 3.1s linear infinite 7.9s; }
.falling-star:nth-child(22) { left: 55%; animation: fall 4.2s linear infinite 8.3s; }
.falling-star:nth-child(23) { left: 62%; animation: fall 3.9s linear infinite 8.7s; }
.falling-star:nth-child(24) { left: 68%; animation: fall 4.5s linear infinite 9.1s; }
.falling-star:nth-child(25) { left: 75%; animation: fall 3.6s linear infinite 9.5s; }
.falling-star:nth-child(26) { left: 82%; animation: fall 4.3s linear infinite 9.9s; }
.falling-star:nth-child(27) { left: 88%; animation: fall 3.8s linear infinite 10.3s; }
.falling-star:nth-child(28) { left: 95%; animation: fall 4.1s linear infinite 10.7s; }
.falling-star:nth-child(29) { left: 10%; animation: fall 3.7s linear infinite 11.1s; }
.falling-star:nth-child(30) { left: 20%; animation: fall 4.4s linear infinite 11.5s; }
.falling-star:nth-child(31) { left: 30%; animation: fall 3.2s linear infinite 11.9s; }
.falling-star:nth-child(32) { left: 40%; animation: fall 4.6s linear infinite 12.3s; }
.falling-star:nth-child(33) { left: 50%; animation: fall 3.9s linear infinite 12.7s; }
.falling-star:nth-child(34) { left: 60%; animation: fall 4.2s linear infinite 13.1s; }
.falling-star:nth-child(35) { left: 70%; animation: fall 3.5s linear infinite 13.5s; }
.falling-star:nth-child(36) { left: 80%; animation: fall 4.8s linear infinite 13.9s; }
.falling-star:nth-child(37) { left: 90%; animation: fall 3.3s linear infinite 14.3s; }
.falling-star:nth-child(38) { left: 3%; animation: fall 4.5s linear infinite 14.7s; }
.falling-star:nth-child(39) { left: 97%; animation: fall 3.8s linear infinite 15.1s; }
.falling-star:nth-child(40) { left: 7%; animation: fall 4.1s linear infinite 15.5s; }

/* Make some stars smaller and some tiny */
.falling-star:nth-child(3),
.falling-star:nth-child(7),
.falling-star:nth-child(11),
.falling-star:nth-child(15),
.falling-star:nth-child(19),
.falling-star:nth-child(23),
.falling-star:nth-child(27),
.falling-star:nth-child(31),
.falling-star:nth-child(35),
.falling-star:nth-child(39) {
  width: 2px;
  height: 2px;
  background: radial-gradient(circle, #fff, transparent);
  box-shadow: 0 0 4px rgba(255, 255, 255, 0.7);
}

.falling-star:nth-child(5),
.falling-star:nth-child(9),
.falling-star:nth-child(13),
.falling-star:nth-child(17),
.falling-star:nth-child(21),
.falling-star:nth-child(25),
.falling-star:nth-child(29),
.falling-star:nth-child(33),
.falling-star:nth-child(37) {
  width: 1px;
  height: 1px;
  background: radial-gradient(circle, rgba(255,255,255,0.8), transparent);
  box-shadow: 0 0 2px rgba(255, 255, 255, 0.3);
}

@keyframes fall {
  0% {
    opacity: 0;
    transform: translateY(-20px);
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    transform: translateY(calc(100vh + 20px));
  }
}

.menu-content {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px;
  text-align: center;
  border-radius: 20px;
  background: rgba(0, 10, 20, 0.7);
  border: 2px solid rgba(0, 255, 255, 0.3);
  box-shadow:
    0 0 30px rgba(0, 255, 255, 0.2),
    inset 0 0 20px rgba(0, 128, 255, 0.1);
  backdrop-filter: blur(5px);
  max-width: 800px;
  margin: 0 auto;
}

.game-title {
  font-size: 5rem;
  font-weight: bold;
  color: #00ffff;
  text-shadow:
    0 0 10px #00ffff,
    0 0 20px #00ffff,
    0 0 30px #0080ff,
    0 0 40px #0080ff,
    2px 2px 4px rgba(0, 0, 0, 0.8);
  margin-bottom: 20px;
  letter-spacing: 6px;
  font-family: 'Courier New', monospace;
  text-transform: uppercase;
  position: relative;
  animation: titleGlow 2s ease-in-out infinite alternate;
}

@keyframes titleGlow {
  0% {
    text-shadow:
      0 0 10px #00ffff,
      0 0 20px #00ffff,
      0 0 30px #0080ff,
      0 0 40px #0080ff,
      2px 2px 4px rgba(0, 0, 0, 0.8);
  }
  100% {
    text-shadow:
      0 0 15px #00ffff,
      0 0 30px #00ffff,
      0 0 45px #0080ff,
      0 0 60px #0080ff,
      2px 2px 4px rgba(0, 0, 0, 0.8);
  }
}

.game-subtitle {
  font-size: 1.2rem;
  color: #ffffff;
  margin-bottom: 40px;
  opacity: 0.8;
  font-family: 'Courier New', monospace;
  text-transform: uppercase;
  letter-spacing: 3px;
  text-shadow:
    0 0 5px rgba(0, 255, 255, 0.5),
    0 0 10px rgba(0, 128, 255, 0.3);
  animation: subtitleGlow 3s ease-in-out infinite alternate;
}

@keyframes subtitleGlow {
  0% {
    text-shadow:
      0 0 5px rgba(0, 255, 255, 0.5),
      0 0 10px rgba(0, 128, 255, 0.3);
  }
  100% {
    text-shadow:
      0 0 8px rgba(0, 255, 255, 0.7),
      0 0 15px rgba(0, 128, 255, 0.5);
  }
}

.auth-section {
  margin-bottom: 30px;
  min-height: 100px;
}

.orange-id-container {
  margin-bottom: 20px;
}

.debug-section {
  margin-top: 20px;
}

.debug-button {
  background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 50%, #ff6b35 100%);
  color: white;
  border: 1px solid #ff6b35;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  font-family: 'Courier New', monospace;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  box-shadow:
    0 0 10px rgba(255, 107, 53, 0.5),
    inset 0 0 5px rgba(255, 107, 53, 0.2);
}

.debug-button:hover {
  background: linear-gradient(135deg, #e55a2b 0%, #ff7a32 50%, #e55a2b 100%);
  box-shadow:
    0 0 15px rgba(255, 107, 53, 0.7),
    inset 0 0 8px rgba(255, 107, 53, 0.3);
  transform: translateY(-2px);
}

.menu-button {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #1e3c72 100%);
  color: #00ffff;
  border: 2px solid #00ffff;
  padding: 15px 30px;
  margin: 10px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 18px;
  font-weight: bold;
  font-family: 'Courier New', monospace;
  text-transform: uppercase;
  letter-spacing: 2px;
  transition: all 0.3s ease;
  min-width: 200px;
  position: relative;
  overflow: hidden;
  box-shadow:
    0 0 15px rgba(0, 255, 255, 0.5),
    inset 0 0 10px rgba(0, 255, 255, 0.1);
}

.menu-button:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.3), transparent);
  transition: left 0.6s;
}

.menu-button:hover {
  transform: translateY(-3px);
  box-shadow:
    0 8px 25px rgba(0, 255, 255, 0.6),
    inset 0 0 20px rgba(0, 255, 255, 0.2);
  text-shadow: 0 0 10px #00ffff;
}

.menu-button:hover:before {
  left: 100%;
}

.menu-button:active {
  transform: translateY(-1px);
  box-shadow:
    0 4px 15px rgba(0, 255, 255, 0.8),
    inset 0 0 15px rgba(0, 255, 255, 0.3);
}

.menu-button.primary {
  background: linear-gradient(135deg, #0f3460 0%, #00ffff 50%, #0f3460 100%);
  color: #0a0a2e;
  border-color: #00ffff;
  box-shadow:
    0 0 25px rgba(0, 255, 255, 0.7),
    inset 0 0 15px rgba(0, 255, 255, 0.2);
}

.menu-button.primary:hover {
  box-shadow:
    0 10px 30px rgba(0, 255, 255, 0.8),
    inset 0 0 25px rgba(0, 255, 255, 0.3);
}

.menu-button.secondary {
  background: linear-gradient(135deg, #2d1a05 0%, #ffd700 50%, #2d1a05 100%);
  color: #ffd700;
  border-color: #ffd700;
  font-size: 16px;
  padding: 12px 25px;
  min-width: 180px;
  box-shadow:
    0 0 20px rgba(255, 215, 0, 0.5),
    inset 0 0 10px rgba(255, 215, 0, 0.1);
}

.menu-button.secondary:hover {
  box-shadow:
    0 8px 25px rgba(255, 215, 0, 0.7),
    inset 0 0 20px rgba(255, 215, 0, 0.2);
  text-shadow: 0 0 10px #ffd700;
}

.user-info {
  margin-top: 20px;
  color: #ffffff;
  background: rgba(0, 10, 20, 0.7);
  padding: 15px 25px;
  border-radius: 10px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 255, 255, 0.3);
  box-shadow:
    0 0 15px rgba(0, 255, 255, 0.2),
    inset 0 0 10px rgba(0, 128, 255, 0.1);
  font-family: 'Courier New', monospace;
}

.user-info p {
  margin: 5px 0;
}

.error-message {
  color: #ff4444;
  background: rgba(255, 68, 68, 0.1);
  padding: 10px 20px;
  border-radius: 5px;
  margin-top: 20px;
  border: 1px solid #ff4444;
  font-family: 'Courier New', monospace;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow:
    0 0 10px rgba(255, 68, 68, 0.3),
    inset 0 0 5px rgba(255, 68, 68, 0.1);
  animation: errorPulse 2s ease-in-out infinite alternate;
}

@keyframes errorPulse {
  0% {
    box-shadow:
      0 0 10px rgba(255, 68, 68, 0.3),
      inset 0 0 5px rgba(255, 68, 68, 0.1);
  }
  100% {
    box-shadow:
      0 0 15px rgba(255, 68, 68, 0.5),
      inset 0 0 8px rgba(255, 68, 68, 0.2);
  }
}

/* Audio Settings Panel Styles */
.audio-settings-panel {
  background: rgba(0, 10, 20, 0.9);
  border: 2px solid rgba(0, 255, 255, 0.4);
  border-radius: 15px;
  padding: 30px;
  margin: 20px 0;
  backdrop-filter: blur(10px);
  box-shadow:
    0 0 30px rgba(0, 255, 255, 0.3),
    inset 0 0 20px rgba(0, 128, 255, 0.1);
  max-width: 500px;
  width: 100%;
}

.audio-settings-panel h3 {
  color: #00ffff;
  font-family: 'Courier New', monospace;
  font-size: 1.5rem;
  text-align: center;
  margin-bottom: 25px;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.8);
  text-transform: uppercase;
  letter-spacing: 2px;
}

.audio-control {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding: 10px 0;
  border-bottom: 1px solid rgba(0, 255, 255, 0.2);
}

.audio-control:last-of-type {
  border-bottom: none;
  margin-bottom: 25px;
}

.audio-control label {
  color: #ffffff;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 1px;
  flex: 1;
  margin-right: 15px;
}

.audio-control input[type="range"] {
  flex: 2;
  margin: 0 10px;
  height: 6px;
  background: linear-gradient(to right, #1e3c72, #00ffff);
  border-radius: 3px;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.audio-control input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  background: #00ffff;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.8);
  transition: all 0.2s ease;
}

.audio-control input[type="range"]::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 0 15px rgba(0, 255, 255, 1);
}

.audio-control input[type="range"]::-moz-range-thumb {
  width: 18px;
  height: 18px;
  background: #00ffff;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.8);
  border: none;
  transition: all 0.2s ease;
}

.audio-control input[type="range"]:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.audio-control input[type="range"]:disabled::-webkit-slider-thumb {
  background: #666;
  box-shadow: none;
}

.audio-control span {
  color: #00ffff;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  min-width: 35px;
  text-align: right;
  font-weight: bold;
}

.audio-control input[type="checkbox"] {
  margin-right: 10px;
  width: 16px;
  height: 16px;
  accent-color: #00ffff;
}

.audio-control-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 25px;
}

.audio-control-buttons .menu-button {
  min-width: 120px;
  padding: 10px 20px;
  font-size: 14px;
}

/* Orange ID widget styling */
#bedrock-login-widget {
  min-height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Orange ID widget styling - add at the end */
#bedrock-login-widget {
  min-height: 200px;
  width: 100%;
  max-width: 480px;
  margin: 0 auto;
}

/* Ensure widget styles take precedence */
#bedrock-login-widget * {
  box-sizing: border-box;
}

/* Override any conflicting game styles */
.orange-id-container #bedrock-login-widget {
  background: rgba(0, 10, 20, 0.9);
  border-radius: 1rem;
  padding: 1rem;
  border: 1px solid rgba(255, 165, 0, 0.3);
}

/* Responsive design */
@media (max-width: 768px) {
  .game-title {
    font-size: 2.5rem;
  }
  
  .menu-button {
    min-width: 250px;
    padding: 12px 25px;
  }
  
  .menu-content {
    padding: 10px;
  }
}