/**
 * AuthManager - Orange ID authentication wrapper
 * Handles Orange ID integration with authentication state management
 */
export class AuthManager {
    constructor(config = {}) {
        this.config = {
            baseUrl: 'https://api.bedrockpassport.com',
            authCallbackUrl: window.location.origin,
            tenantId: config.tenantId || import.meta.env.VITE_ORANGE_ID_TENANT_ID || 'orangeid-4Pf9tVe0mL',
            subscriptionKey: config.subscriptionKey || import.meta.env.VITE_ORANGE_ID_SUBSCRIPTION_KEY || 'ca85570bb0c949738f6732e87954c961',
            debugMode: config.debugMode || this.isDebugEnvironment()
        };
        
        // Ensure the correct origin is used for production
        if (!this.isDebugEnvironment() && window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') {
            // For production, ensure we're using the correct domain
            const expectedOrigin = 'https://warpsector.merchgenieai.com';
            if (window.location.origin !== expectedOrigin) {
                console.warn('Unexpected origin detected:', window.location.origin);
            }
        }
        
        this.user = null;
        this.token = null;
        this.refreshToken = null;
        this.isAuthenticated = false;
        this.authCallbacks = [];

        // Set up event listeners for Orange ID integration
        this.setupAuthEventListeners();

        // Check for existing authentication on page load
        this.checkExistingAuth();
    }

    isDebugEnvironment() {
        return window.location.hostname === 'localhost' || 
               window.location.hostname === '127.0.0.1' ||
               window.location.search.includes('debug=true');
    }

    async initializeOrangeID() {
        // Widget initialization is now handled by the HTML script
        // This method is kept for compatibility but does nothing
        console.log('Orange ID widget initialization handled by HTML script');
        return Promise.resolve();
    }

    checkExistingAuth() {
        // Check if we have tokens in URL parameters (callback scenario)
        const params = new URLSearchParams(window.location.search);
        const token = params.get('token');
        const refreshToken = params.get('refreshToken');

        if (token && refreshToken) {
            console.log('Found auth tokens in URL, processing callback...');
            // The HTML script will handle this, but we can prepare for it
            return;
        }

        // Check for existing stored authentication (if any)
        // This could be expanded later to check localStorage or other storage
        console.log('No existing authentication found');
    }



    setupAuthEventListeners() {
        // Listen for custom auth events (for additional integration points)
        window.addEventListener('orangeAuthSuccess', (event) => {
            this.handleAuthSuccess(event.detail.token, event.detail.refreshToken);
        });

        window.addEventListener('orangeAuthError', (event) => {
            this.handleAuthError(event.detail.error);
        });
    }

    async handleAuthSuccess(token, refreshToken) {
        this.token = token;
        this.refreshToken = refreshToken;

        try {
            const userProfile = await this.validateToken(token);

            // Check if this is the debug wallet and enable debug mode
            const processedProfile = this.checkAndEnableDebugMode(userProfile);

            this.user = processedProfile;
            this.isAuthenticated = true;

            // Ensure chain ID is set correctly after authentication
            if (window.ethereum && window.gameEngine?.ethTestModeManager?.isTestMode) {
                try {
                    await window.ethereum.request({
                        method: 'wallet_switchEthereumChain',
                        params: [{ chainId: '0x7a69' }] // 31337 in hex
                    });
                } catch (switchError) {
                    // Handle error if needed
                    console.warn('Could not switch to Hardhat chain:', switchError);
                }
            }

            // Clear URL parameters
            const url = new URL(window.location);
            url.searchParams.delete('token');
            url.searchParams.delete('refreshToken');
            window.history.replaceState({}, document.title, url);

            this.notifyAuthCallbacks(true, this.user);
        } catch (error) {
            this.handleAuthError(error);
        }
    }

    handleAuthError(error) {
        this.isAuthenticated = false;
        this.user = null;
        this.token = null;
        this.refreshToken = null;
        this.notifyAuthCallbacks(false, error);
    }

    async validateToken(token) {
        const response = await fetch('https://api.bedrockpassport.com/api/v1/auth/user', {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('Token validation failed');
        }

        return await response.json();
    }

    /**
     * Check if the connected wallet is the debug wallet
     * Debug wallet: ******************************************
     */
    isDebugWallet(address) {
        const DEBUG_WALLET = '******************************************';
        return address && address.toLowerCase() === DEBUG_WALLET.toLowerCase();
    }

    /**
     * Check if user should be in debug mode based on wallet address
     * This is called after successful authentication
     */
    checkAndEnableDebugMode(userProfile) {
        if (userProfile && userProfile.ethAddress && this.isDebugWallet(userProfile.ethAddress)) {
            console.log('🔧 Debug wallet detected - enabling debug mode with fake WISH tokens');
            this.config.debugMode = true;
            // Mark user as debug user
            userProfile.isDebugUser = true;
        }
        return userProfile;
    }

    async logout() {
        this.user = null;
        this.token = null;
        this.refreshToken = null;
        this.isAuthenticated = false;
        this.notifyAuthCallbacks(false, null);
    }

    getUser() {
        return this.user;
    }

    onAuthStateChange(callback) {
        this.authCallbacks.push(callback);
    }

    notifyAuthCallbacks(isAuthenticated, data) {
        this.authCallbacks.forEach(callback => {
            callback(isAuthenticated, data);
        });
    }
}
