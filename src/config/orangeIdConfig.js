/**
 * Orange ID Configuration
 * Uses Vite environment variables for secure configuration
 */

export const ORANGE_ID_CONFIG = {
    baseUrl: 'https://api.bedrockpassport.com',
    authCallbackUrl: window.location.origin + '/auth/callback.html',
    tenantId: import.meta.env.VITE_ORANGE_ID_TENANT_ID,
    subscriptionKey: import.meta.env.VITE_ORANGE_ID_SUBSCRIPTION_KEY,
};

// Validate configuration
if (!ORANGE_ID_CONFIG.tenantId || !ORANGE_ID_CONFIG.subscriptionKey) {
    console.error('Orange ID configuration missing! Please check your .env file for:');
    console.error('- VITE_ORANGE_ID_TENANT_ID');
    console.error('- VITE_ORANGE_ID_SUBSCRIPTION_KEY');
}

// Set global config for HTML script access
window.OrangeIDConfig = ORANGE_ID_CONFIG;

console.log('Orange ID Config loaded:', {
    baseUrl: ORANGE_ID_CONFIG.baseUrl,
    authCallbackUrl: ORANGE_ID_CONFIG.authCallbackUrl,
    tenantId: ORANGE_ID_CONFIG.tenantId ? '***' + ORANGE_ID_CONFIG.tenantId.slice(-4) : 'MISSING',
    subscriptionKey: ORANGE_ID_CONFIG.subscriptionKey ? '***' + ORANGE_ID_CONFIG.subscriptionKey.slice(-4) : 'MISSING'
});
