import { AuthManager } from '../auth/AuthManager.js';

export class MainMenu {
  constructor(gameEngine) {
    this.gameEngine = gameEngine;
    this.authManager = new AuthManager();
    
    this.container = null;
    this.isVisible = false;
    this.setupEventListeners();
  }

  async initialize() {
    this.createMenuHTML();

    // Check for successful authentication from callback
    this.checkAuthFromCallback();

    this.authManager.onAuthStateChange((isAuthenticated, data) => {
      if (isAuthenticated) {
        this.onAuthenticationSuccess(data);
      } else {
        this.onAuthenticationFailure(data);
      }
    });
  }

  createMenuHTML() {
    this.container = document.createElement('div');
    this.container.id = 'main-menu';
    this.container.className = 'main-menu';
    
    this.container.innerHTML = `
      <div class="menu-background">
        <div class="falling-stars">
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
          <div class="falling-star"></div>
        </div>
        <div class="menu-content">
          <h1 class="game-title">Warp Sector</h1>
          <p class="game-subtitle">Reality Warping Shooter</p>
          
          <div class="auth-section" id="auth-section">
            <div class="orange-id-container">
              <div id="bedrock-login-widget"></div>
            </div>
          </div>
          
          <div class="game-controls" id="game-controls" style="display: none;">
            <button id="start-game-btn" class="menu-button primary">
              Start Game
            </button>
            <button id="genie-shop-btn" class="menu-button">
              Genie Shop 🪔
            </button>
            <button id="audio-settings-btn" class="menu-button">
              Audio Settings 🔊
            </button>
            <button id="instructions-btn" class="menu-button">
              How to Play
            </button>
            <button id="logout-btn" class="menu-button secondary">
              Logout
            </button>
          </div>
          
          <!-- Audio Settings Panel -->
          <div class="audio-settings-panel" id="audio-settings-panel" style="display: none;">
            <h3>Audio Settings</h3>
            <div class="audio-control">
              <label for="master-volume">Master Volume:</label>
              <input type="range" id="master-volume" min="0" max="100" value="70">
              <span id="master-volume-value">70%</span>
            </div>
            <div class="audio-control">
              <label for="music-volume">Music Volume:</label>
              <input type="range" id="music-volume" min="0" max="100" value="70">
              <span id="music-volume-value">70%</span>
            </div>
            <div class="audio-control">
              <label for="sfx-volume">Sound Effects Volume:</label>
              <input type="range" id="sfx-volume" min="0" max="100" value="80">
              <span id="sfx-volume-value">80%</span>
            </div>
            <div class="audio-control">
              <label for="ambient-volume">Ambient Volume:</label>
              <input type="range" id="ambient-volume" min="0" max="100" value="30">
              <span id="ambient-volume-value">30%</span>
            </div>
            <div class="audio-control">
              <label>
                <input type="checkbox" id="mute-all"> Mute All
              </label>
            </div>
            <div class="audio-control-buttons">
              <button id="apply-audio-settings" class="menu-button primary">Apply</button>
              <button id="cancel-audio-settings" class="menu-button secondary">Cancel</button>
            </div>
          </div>
          
          <div class="user-info" id="user-info" style="display: none;">
            <p>Welcome, <span id="user-name"></span>!</p>
          </div>
          
          <div class="error-message" id="error-message" style="display: none;"></div>
        </div>
      </div>
    `;
    
    document.body.appendChild(this.container);
  }

  setupEventListeners() {
    document.addEventListener('click', async (event) => {
      if (event.target.id === 'start-game-btn') {
        this.startGame();
      } else if (event.target.id === 'genie-shop-btn') {
        // Handle audio context activation before showing genie shop
        if (this.gameEngine.audioManager) {
          await this.gameEngine.audioManager.handleUserInteraction();
        }
        this.showGenieShop();
      } else if (event.target.id === 'audio-settings-btn') {
        // Handle audio context activation before showing audio settings
        if (this.gameEngine.audioManager) {
          await this.gameEngine.audioManager.handleUserInteraction();
        }
        this.showAudioSettings();
      } else if (event.target.id === 'instructions-btn') {
        // Handle audio context activation before showing instructions
        if (this.gameEngine.audioManager) {
          await this.gameEngine.audioManager.handleUserInteraction();
        }
        this.showInstructions();
      } else if (event.target.id === 'logout-btn') {
        this.logout();
      } else if (event.target.id === 'apply-audio-settings') {
        this.applyAudioSettings();
      } else if (event.target.id === 'cancel-audio-settings') {
        this.hideAudioSettings();
      }
    });

    // Audio volume sliders
    document.addEventListener('input', (event) => {
      if (event.target.id === 'master-volume') {
        this.updateVolumeDisplay('master-volume', 'master-volume-value');
      } else if (event.target.id === 'music-volume') {
        this.updateVolumeDisplay('music-volume', 'music-volume-value');
      } else if (event.target.id === 'sfx-volume') {
        this.updateVolumeDisplay('sfx-volume', 'sfx-volume-value');
      } else if (event.target.id === 'ambient-volume') {
        this.updateVolumeDisplay('ambient-volume', 'ambient-volume-value');
      }
    });

    // Mute all checkbox
    document.addEventListener('change', (event) => {
      if (event.target.id === 'mute-all') {
        this.toggleMuteAll(event.target.checked);
      }
    });
  }

  onAuthenticationSuccess(user) {
    
    // Hide auth section, show game controls
    document.getElementById('auth-section').style.display = 'none';
    document.getElementById('game-controls').style.display = 'block';
    document.getElementById('user-info').style.display = 'block';
    
    // Update user info
    document.getElementById('user-name').textContent = user.name || user.email || 'Player';
    
    // Initialize wallet connection in token manager
    if (this.gameEngine && this.gameEngine.tokenManager) {
      this.gameEngine.tokenManager.initializeWallet();
    }
    
    // Load user's token balance (would come from Orange SDK later)
    this.loadUserTokenBalance();
    
    this.hideError();
  }

  onAuthenticationFailure(error) {
    console.error('Authentication failed:', error);
    
    // Show auth section, hide game controls
    document.getElementById('auth-section').style.display = 'block';
    document.getElementById('game-controls').style.display = 'none';
    document.getElementById('user-info').style.display = 'none';
    
    if (error && typeof error === 'object') {
      this.showError('Authentication failed. Please try again.');
    }
  }

  async loadUserTokenBalance() {
    // Placeholder for Orange SDK integration
    // Use getBalanceDisplay if available, otherwise show default balance
    if (this.gameEngine && this.gameEngine.tokenManager) {
      const balanceDisplay = this.gameEngine.tokenManager.getBalanceDisplay ?
        this.gameEngine.tokenManager.getBalanceDisplay() :
        `${this.gameEngine.tokenManager.getBalance()} WISH`;
      const tokenElement = document.getElementById('token-balance');
      if (tokenElement) {
        tokenElement.textContent = balanceDisplay;
      }
    } else {
      // Fallback for when token manager is not available
      const tokenElement = document.getElementById('token-balance');
      if (tokenElement) {
        tokenElement.textContent = '100 WISH';
      }
    }
  }

  async startGame() {
      if (!this.authManager.isAuthenticated) {
          this.showError('Please authentication first');
          return;
      }

      // Handle audio context activation (required by modern browsers)
      if (this.gameEngine.audioManager) {
          await this.gameEngine.audioManager.handleUserInteraction();
      }

      this.hide();
      this.gameEngine.startGameplay(this.authManager.getUser());
  }

  showGenieShop() {
    if (!this.authManager.isAuthenticated) {
      this.showError('Please authenticate first');
      return;
    }

    // Get the game engine's genie interface
    if (this.gameEngine && this.gameEngine.genieInterface) {
      // Award debug tokens only if user is in debug mode (specific wallet address)
      const isDebugUser = this.authManager.user?.isDebugUser;
      if (isDebugUser && this.gameEngine.tokenManager && this.gameEngine.tokenManager.getBalance() < 100) {
        console.log('🔧 Debug user detected - awarding fake WISH tokens');
        this.gameEngine.tokenManager.awardTokens(50000, 'debug_menu_tokens');
      }

      this.gameEngine.genieInterface.show();
    } else {
      this.showError('Genie Shop not available. Please start the game first.');
    }
  }

  showInstructions() {
    const instructions = `
      Warp Sector - How to Play
      🚀 CONTROLS:
      • Arrow Keys / WASD / Joystick on Mobile: Move ship
      • Spacebar/Joystick: Fire weapons
      • Shift key / Double tap: BOOST
      • E Key / Tap Power Up Icon: Activate power up
      • P: Pause game
      
      💎 WISH TOKENS:
      • Earn tokens by defeating as many enemies as possible.
      • Spend tokens on reality warps and power-ups.
      • Make purchases in the Genie Shop.
      
      🌟 REALITY WARPS:
      • Transform battlefields with AI-generated environments
      • Different environments affect enemy effectiveness
      • Strategic warping can give you tactical advantages
    `;
    
    alert(instructions); // Replace with proper modal NOW
  }

  async logout() {
    await this.authManager.logout();
    this.onAuthenticationFailure(null);
  }

  show() {
    if (this.container) {
      this.container.style.display = 'block';
      this.isVisible = true;
    }
  }

  hide() {
    if (this.container) {
      this.container.style.display = 'none';
      this.isVisible = false;
    }
  }

  // Audio Settings Methods
  showAudioSettings() {
    const audioPanel = document.getElementById('audio-settings-panel');
    const gameControls = document.getElementById('game-controls');
    
    if (audioPanel && gameControls) {
      gameControls.style.display = 'none';
      audioPanel.style.display = 'block';
      
      // Load current audio settings
      if (this.gameEngine && this.gameEngine.audioManager) {
        try {
          const settings = this.gameEngine.audioManager.getVolumeSettings();
          document.getElementById('master-volume').value = Math.round(settings.master * 100);
          document.getElementById('music-volume').value = Math.round(settings.music * 100);
          document.getElementById('sfx-volume').value = Math.round(settings.sfx * 100);
          document.getElementById('ambient-volume').value = Math.round(settings.ambient * 100);
          document.getElementById('mute-all').checked = !settings.enabled;
          
          // Update display values
          this.updateVolumeDisplay('master-volume', 'master-volume-value');
          this.updateVolumeDisplay('music-volume', 'music-volume-value');
          this.updateVolumeDisplay('sfx-volume', 'sfx-volume-value');
          this.updateVolumeDisplay('ambient-volume', 'ambient-volume-value');
        } catch (error) {
          console.warn('Could not load audio settings:', error);
        }
      } else {
        console.warn('AudioManager not available');
      }
    }
  }

  hideAudioSettings() {
    const audioPanel = document.getElementById('audio-settings-panel');
    const gameControls = document.getElementById('game-controls');
    
    if (audioPanel && gameControls) {
      audioPanel.style.display = 'none';
      gameControls.style.display = 'block';
    }
  }

  applyAudioSettings() {
    if (this.gameEngine && this.gameEngine.audioManager) {
      try {
        const masterVolume = document.getElementById('master-volume').value / 100;
        const musicVolume = document.getElementById('music-volume').value / 100;
        const sfxVolume = document.getElementById('sfx-volume').value / 100;
        const ambientVolume = document.getElementById('ambient-volume').value / 100;
        const muteAll = document.getElementById('mute-all').checked;
        
        // Apply settings to audio manager
        this.gameEngine.audioManager.setVolume('master', masterVolume);
        this.gameEngine.audioManager.setVolume('music', musicVolume);
        this.gameEngine.audioManager.setVolume('sfx', sfxVolume);
        this.gameEngine.audioManager.setVolume('ambient', ambientVolume);
        
        if (muteAll) {
          this.gameEngine.audioManager.mute('all', true);
        } else {
          this.gameEngine.audioManager.mute('all', false);
        }
        
        console.log('Audio settings applied successfully');
      } catch (error) {
        console.warn('Could not apply audio settings:', error);
      }
    } else {
      console.warn('AudioManager not available for applying settings');
    }
    
    this.hideAudioSettings();
  }

  updateVolumeDisplay(sliderId, valueId) {
    const slider = document.getElementById(sliderId);
    const valueDisplay = document.getElementById(valueId);
    
    if (slider && valueDisplay) {
      valueDisplay.textContent = slider.value + '%';
    }
  }

  toggleMuteAll(muted) {
    if (this.gameEngine && this.gameEngine.audioManager) {
      try {
        this.gameEngine.audioManager.mute('all', muted);
        
        // Disable/enable volume sliders
        const sliders = ['master-volume', 'music-volume', 'sfx-volume', 'ambient-volume'];
        sliders.forEach(sliderId => {
          const slider = document.getElementById(sliderId);
          if (slider) {
            slider.disabled = muted;
          }
        });
      } catch (error) {
        console.warn('Could not toggle mute:', error);
      }
    }
  }

  showError(message) {
    const errorElement = document.getElementById('error-message');
    if (errorElement) {
      errorElement.textContent = message;
      errorElement.style.display = 'block';
    }
  }

  hideError() {
    const errorElement = document.getElementById('error-message');
    if (errorElement) {
      errorElement.style.display = 'none';
    }
  }

  destroy() {
    if (this.container) {
      document.body.removeChild(this.container);
      this.container = null;
    }
  }
}
